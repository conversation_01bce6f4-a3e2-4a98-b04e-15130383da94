APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=widdx_ai
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# AI Services Configuration
DEFAULT_AI_SERVICE=deepseek

# DeepSeek Configuration
DEEPSEEK_ENABLED=true
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_URL=https://api.deepseek.com/v1/chat/completions
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_TEMPERATURE=0.7
DEEPSEEK_MAX_TOKENS=1000
DEEPSEEK_TIMEOUT=30

# Gemini Configuration
GEMINI_ENABLED=true
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models
GEMINI_MODEL=gemini-pro
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=1000
GEMINI_TIMEOUT=30

# HuggingFace Configuration
HUGGINGFACE_ENABLED=true
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
HUGGINGFACE_API_URL=https://api-inference.huggingface.co/models
HUGGINGFACE_MODEL=mistralai/Mistral-7B-Instruct-v0.2
HUGGINGFACE_TEMPERATURE=0.7
HUGGINGFACE_MAX_TOKENS=1000
HUGGINGFACE_TIMEOUT=30

# AI Fallback Configuration
AI_FALLBACK_ENABLED=true

# AI Rate Limiting
AI_RATE_LIMIT_ENABLED=true
AI_RATE_LIMIT_MAX=60
AI_RATE_LIMIT_PERIOD=60

# Knowledge Base Configuration
KNOWLEDGE_BASE_ENABLED=true
KNOWLEDGE_BASE_CACHE_ENABLED=true
KNOWLEDGE_BASE_CACHE_TTL=3600
KNOWLEDGE_BASE_SIMILARITY_THRESHOLD=0.7
KNOWLEDGE_BASE_EMBEDDING_DIMENSION=384
KNOWLEDGE_BASE_USE_MOCK_EMBEDDINGS=true
KNOWLEDGE_BASE_EMBEDDING_SERVICE_URL=https://api-inference.huggingface.co/pipeline/feature-extraction/sentence-transformers/all-MiniLM-L6-v2

# AI Caching Configuration
AI_CACHING_ENABLED=true
AI_CACHING_AI_RESPONSE_TTL=7200
AI_CACHING_CONVERSATIONS_LIST_TTL=900
AI_CACHING_CONVERSATION_TTL=3600
AI_CACHING_KNOWLEDGE_STATS_TTL=1800
