# Design Document

## Overview

WIDDX AI is a Laravel-based intelligent assistant that routes user queries to specialized AI models (DeepSeek, Gemini, HuggingFace) based on query analysis. The system features a local knowledge base with embedding-based search to optimize response times and reduce external API costs. The architecture follows Laravel best practices with service-oriented design, dependency injection, and comprehensive error handling.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Request] --> B[Laravel API Controller]
    B --> C[DecisionEngine Service]
    C --> D[KnowledgeBase Service]
    D --> E{Local Knowledge Found?}
    E -->|Yes| F[Return Enhanced Response]
    E -->|No| G[Route to AI Service]
    G --> H[DeepSeek Service]
    G --> I[Gemini Service]
    G --> J[HuggingFace Service]
    H --> K[Store Response]
    I --> K
    J --> K
    K --> L[Return to User]
    
    M[MySQL Database] --> N[Conversations]
    M --> O[Messages]
    M --> P[Knowledge Entries]
```

### Project Structure

```
widdx-ai/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   └── Api/
│   │   │       └── AssistantController.php
│   │   ├── Requests/
│   │   │   └── AskRequest.php
│   │   └── Resources/
│   │       └── MessageResource.php
│   ├── Models/
│   │   ├── Conversation.php
│   │   ├── Message.php
│   │   └── KnowledgeEntry.php
│   ├── Services/
│   │   ├── AI/
│   │   │   ├── DeepSeekService.php
│   │   │   ├── GeminiService.php
│   │   │   ├── HuggingFaceService.php
│   │   │   └── Contracts/
│   │   │       └── AIServiceInterface.php
│   │   ├── DecisionEngine.php
│   │   └── KnowledgeBaseService.php
│   ├── Providers/
│   │   └── AIServiceProvider.php
│   └── Exceptions/
│       └── AIServiceException.php
├── database/
│   ├── migrations/
│   │   ├── 2024_01_01_000001_create_conversations_table.php
│   │   ├── 2024_01_01_000002_create_messages_table.php
│   │   └── 2024_01_01_000003_create_knowledge_entries_table.php
│   └── seeders/
│       └── KnowledgeBaseSeeder.php
├── routes/
│   └── api.php
├── config/
│   └── ai.php
└── .env.example
```

## Components and Interfaces

### AI Service Interface

All AI services implement a common interface for consistency:

```php
interface AIServiceInterface
{
    public function sendMessage(string $message, array $context = []): array;
    public function isAvailable(): bool;
    public function getModelName(): string;
}
```

### Core Services

#### DecisionEngine Service
- Analyzes query intent using keyword matching and pattern recognition
- Routes queries to appropriate AI service based on classification
- Implements fallback strategies for service failures
- Maintains routing statistics and performance metrics

#### KnowledgeBase Service
- Manages local knowledge storage and retrieval
- Implements embedding-based similarity search using cosine similarity
- Handles knowledge entry creation, updating, and tagging
- Provides caching layer for frequently accessed knowledge

#### AI Services (DeepSeek, Gemini, HuggingFace)
- Handle authentication and API communication
- Implement retry logic and rate limiting
- Transform responses to consistent format
- Manage service-specific error handling

### API Controller Design

The AssistantController provides a single `/api/ask` endpoint that:
1. Validates incoming requests
2. Creates or retrieves conversation context
3. Searches local knowledge base
4. Routes to appropriate AI service if needed
5. Stores conversation history
6. Returns formatted response

## Data Models

### Database Schema

#### Conversations Table
```sql
CREATE TABLE conversations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_created_at (created_at)
);
```

#### Messages Table
```sql
CREATE TABLE messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    conversation_id BIGINT UNSIGNED NOT NULL,
    role ENUM('user', 'assistant') NOT NULL,
    model_used VARCHAR(50) NULL,
    content TEXT NOT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    INDEX idx_conversation_created (conversation_id, created_at)
);
```

#### Knowledge Entries Table
```sql
CREATE TABLE knowledge_entries (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    content TEXT NOT NULL,
    embedding_vector JSON NOT NULL,
    source VARCHAR(255) NULL,
    tags JSON NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_source (source),
    INDEX idx_created_at (created_at)
);
```

### Eloquent Models

Models include relationships, scopes, and business logic:
- Conversation: hasMany Messages, with title generation logic
- Message: belongsTo Conversation, with content formatting
- KnowledgeEntry: with embedding search methods and tag management

## Error Handling

### Exception Hierarchy
- `AIServiceException`: Base exception for AI service errors
- `ServiceUnavailableException`: When external APIs are down
- `RateLimitException`: When API rate limits are exceeded
- `InvalidQueryException`: When user input is malformed

### Error Response Format
```json
{
    "success": false,
    "error": {
        "code": "SERVICE_UNAVAILABLE",
        "message": "AI service temporarily unavailable",
        "details": "DeepSeek API returned 503 status"
    },
    "fallback_used": true,
    "model_used": "gemini"
}
```

### Fallback Strategy
1. Primary model selection based on query analysis
2. If primary fails, attempt secondary model
3. If all external services fail, use local knowledge only
4. Return appropriate error message with available alternatives

## Testing Strategy

### Unit Tests
- Service classes with mocked external APIs
- Decision engine logic with various query types
- Knowledge base search algorithms
- Model relationships and validations

### Integration Tests
- Full API endpoint testing with database
- AI service integration with real API calls (in staging)
- Knowledge base embedding and search functionality
- Error handling and fallback scenarios

### Performance Tests
- API response times under load
- Database query optimization
- Embedding search performance
- Memory usage with large knowledge bases

## Configuration Management

### Environment Variables
```env
# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=widdx_ai
DB_USERNAME=root
DB_PASSWORD=

# AI Service APIs
DEEPSEEK_API_KEY=your_deepseek_key
DEEPSEEK_API_URL=https://api.deepseek.com/v1/chat/completions

GEMINI_API_KEY=your_gemini_key
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models

HUGGINGFACE_API_KEY=your_huggingface_key
HUGGINGFACE_API_URL=https://api-inference.huggingface.co/models

# Knowledge Base
KNOWLEDGE_BASE_ENABLED=true
EMBEDDING_DIMENSION=384
SIMILARITY_THRESHOLD=0.7

# Caching
CACHE_DRIVER=redis
CACHE_TTL=3600
```

### AI Configuration File
Centralized configuration for routing rules, model parameters, and service settings in `config/ai.php`.

## Security Considerations

### API Security
- Rate limiting on `/api/ask` endpoint
- Input validation and sanitization
- API key encryption in database
- Request/response logging for audit

### Data Privacy
- Conversation data encryption at rest
- Configurable data retention policies
- User consent management for knowledge storage
- Secure API key management

## Scalability Features

### Performance Optimization
- Database indexing for fast queries
- Redis caching for knowledge base results
- Queue system for heavy embedding operations
- Connection pooling for external APIs

### Horizontal Scaling
- Stateless service design
- Database read replicas support
- Load balancer compatibility
- Microservice-ready architecture

This design provides a robust, scalable foundation for the WIDDX AI assistant while maintaining clean code architecture and Laravel best practices.