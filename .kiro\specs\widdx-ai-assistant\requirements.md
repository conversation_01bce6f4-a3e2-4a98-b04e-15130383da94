# Requirements Document

## Introduction

WIDDX AI is a local smart assistant that intelligently routes user queries to specialized external LLM APIs based on task type. The system uses DeepSeek for code generation and logic, Gemini for research and data extraction, and HuggingFace for NLP and creative tasks. Built with Laravel and MySQL, it features a knowledge base system with embedding-based search to minimize external API calls and provide faster responses.

## Requirements

### Requirement 1

**User Story:** As a user, I want to send queries to a single API endpoint and receive intelligent responses from the most appropriate AI model, so that I get specialized expertise for different types of tasks.

#### Acceptance Criteria

1. WHEN a user sends a POST request to /ask endpoint THEN the system SHALL accept the query and return a response
2. WHEN the system receives a query THEN it SHALL analyze the query type and route it to the appropriate LLM service
3. WHEN a code-related query is detected THEN the system SHALL route it to DeepSeek API
4. WHEN a research or data extraction query is detected THEN the system SHALL route it to Gemini API
5. WHEN an NLP, sentiment, or creative writing query is detected THEN the system SHALL route it to HuggingFace API
6. WHEN the primary model fails THEN the system SHALL implement a fallback strategy to an alternative model

### Requirement 2

**User Story:** As a developer, I want the system to have a modular service architecture, so that I can easily maintain and extend the AI integrations.

#### Acceptance Criteria

1. WHEN implementing AI integrations THEN the system SHALL have separate service classes for DeepSeek, Gemini, and HuggingFace APIs
2. WHEN creating the decision engine THEN the system SHALL have a dedicated DecisionEngine service class
3. WHEN making API calls THEN each service SHALL handle authentication and error handling independently
4. WHEN extending functionality THEN the system SHALL follow Laravel service provider patterns for dependency injection

### Requirement 3

**User Story:** As a user, I want my conversations to be stored and organized, so that I can maintain context and review previous interactions.

#### Acceptance Criteria

1. WHEN a user starts a new conversation THEN the system SHALL create a conversation record with a unique ID and title
2. WHEN messages are exchanged THEN the system SHALL store each message with role, content, model used, and timestamp
3. WHEN storing messages THEN the system SHALL link them to the appropriate conversation ID
4. WHEN retrieving conversations THEN the system SHALL provide chronological message ordering

### Requirement 4

**User Story:** As a user, I want the system to leverage local knowledge before making external API calls, so that I get faster responses and reduce API costs.

#### Acceptance Criteria

1. WHEN a query is received THEN the system SHALL first search the local knowledge base using embedding similarity
2. WHEN relevant local knowledge is found THEN the system SHALL use it to enhance or replace external API calls
3. WHEN storing new information THEN the system SHALL generate embeddings and store them in the knowledge base
4. WHEN no relevant local knowledge exists THEN the system SHALL proceed with external API calls
5. WHEN external responses are received THEN the system SHALL optionally store valuable information in the knowledge base

### Requirement 5

**User Story:** As a system administrator, I want comprehensive data persistence, so that all interactions and knowledge are preserved and searchable.

#### Acceptance Criteria

1. WHEN setting up the database THEN the system SHALL create conversations table with id, title, and created_at fields
2. WHEN setting up the database THEN the system SHALL create messages table with id, conversation_id, role, model_used, content, and created_at fields
3. WHEN setting up the database THEN the system SHALL create knowledge_entries table with id, content, embedding_vector, source, tags, and created_at fields
4. WHEN storing embeddings THEN the system SHALL support vector storage for similarity search
5. WHEN querying knowledge THEN the system SHALL support tag-based and content-based filtering

### Requirement 6

**User Story:** As a developer, I want clean API interfaces and error handling, so that the system is reliable and easy to integrate with.

#### Acceptance Criteria

1. WHEN API errors occur THEN the system SHALL return appropriate HTTP status codes and error messages
2. WHEN external APIs are unavailable THEN the system SHALL implement graceful degradation
3. WHEN rate limits are exceeded THEN the system SHALL handle throttling appropriately
4. WHEN invalid requests are received THEN the system SHALL validate input and return clear error messages
5. WHEN successful responses are generated THEN the system SHALL return consistent JSON structure

### Requirement 7

**User Story:** As a developer, I want the system to be easily configurable and extensible, so that I can add new AI models or modify routing logic without major code changes.

#### Acceptance Criteria

1. WHEN configuring API credentials THEN the system SHALL use Laravel environment variables
2. WHEN adding new AI models THEN the system SHALL support plugin-style architecture
3. WHEN modifying routing logic THEN the system SHALL use configurable rules or patterns
4. WHEN deploying THEN the system SHALL include proper migration files and seeders
5. WHEN scaling THEN the system SHALL support caching and queue systems for heavy operations