# WIDDX AI Assistant API Documentation

## Overview

The WIDDX AI Assistant API provides intelligent query routing to specialized AI models, knowledge base integration, conversation management, and comprehensive monitoring capabilities.

**Base URL**: `http://localhost:8000/api`

**Content-Type**: `application/json`

## Authentication

Currently, the API does not require authentication. In production, consider implementing API keys or OAuth2.

## Rate Limiting

- **Ask Endpoint**: 30 requests per minute
- **Other Endpoints**: 100 requests per minute
- Rate limit headers are included in responses:
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Unix timestamp when the rate limit resets

## Core Endpoints

### 1. Ask the AI Assistant

Send a query to the AI assistant and receive an intelligent response.

**Endpoint**: `POST /ask`

**Request Body**:
```json
{
    "message": "What is <PERSON><PERSON> and how do I get started?",
    "conversation_id": 123,
    "use_knowledge_base": true,
    "preferred_service": "deepseek",
    "knowledge_threshold": 0.7,
    "max_knowledge_results": 5,
    "tags": ["laravel", "php"],
    "source_filter": "docs",
    "context": ["I'm a beginner developer", "Looking for web framework"]
}
```

**Parameters**:
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `message` | string | Yes | - | The user's question or message (1-10,000 chars) |
| `conversation_id` | integer | No | null | ID of existing conversation to continue |
| `use_knowledge_base` | boolean | No | true | Whether to search the knowledge base |
| `preferred_service` | string | No | null | Preferred AI service: `deepseek`, `gemini`, `huggingface` |
| `knowledge_threshold` | float | No | 0.7 | Similarity threshold for knowledge search (0-1) |
| `max_knowledge_results` | integer | No | 5 | Maximum knowledge entries to return (1-20) |
| `tags` | array | No | [] | Tags to filter knowledge base search (max 10) |
| `source_filter` | string | No | null | Filter knowledge base by source (max 100 chars) |
| `context` | array | No | [] | Additional context for the AI (max 10 items, 1000 chars each) |

**Response**:
```json
{
    "success": true,
    "data": {
        "message": {
            "id": 456,
            "conversation_id": 123,
            "role": "assistant",
            "content": "Laravel is a PHP web framework created by Taylor Otwell...",
            "model_used": "deepseek-chat",
            "created_at": "2024-01-15T10:30:00.000000Z",
            "updated_at": "2024-01-15T10:30:00.000000Z"
        },
        "conversation": {
            "id": 123,
            "title": "Laravel Getting Started",
            "created_at": "2024-01-15T10:25:00.000000Z",
            "updated_at": "2024-01-15T10:30:00.000000Z"
        },
        "response": {
            "content": "Laravel is a PHP web framework...",
            "model_used": "deepseek-chat",
            "service_used": "deepseek",
            "fallback_used": false,
            "processing_time": 1250.5
        },
        "knowledge_base": {
            "used": true,
            "entries_found": 3,
            "threshold_used": 0.7
        },
        "metadata": {
            "query_length": 45,
            "context_items": 2,
            "processing_time_ms": 1250.5
        }
    },
    "timestamp": "2024-01-15T10:30:00.000000Z"
}
```

**Status Codes**:
- `200 OK`: Request successful
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error
- `502 Bad Gateway`: AI service error
- `503 Service Unavailable`: AI services unavailable

### 2. Get Conversations List

Retrieve a paginated list of conversations.

**Endpoint**: `GET /conversations`

**Query Parameters**:
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | integer | 1 | Page number |
| `per_page` | integer | 20 | Items per page (max 100) |

**Response**:
```json
{
    "success": true,
    "data": [
        {
            "id": 123,
            "title": "Laravel Getting Started",
            "created_at": "2024-01-15T10:25:00.000000Z",
            "updated_at": "2024-01-15T10:30:00.000000Z",
            "messages_count": 4
        }
    ],
    "meta": {
        "current_page": 1,
        "last_page": 3,
        "per_page": 20,
        "total": 45
    },
    "timestamp": "2024-01-15T10:35:00.000000Z"
}
```

### 3. Get Specific Conversation

Retrieve a specific conversation with all its messages.

**Endpoint**: `GET /conversations/{id}`

**Response**:
```json
{
    "success": true,
    "data": {
        "id": 123,
        "title": "Laravel Getting Started",
        "created_at": "2024-01-15T10:25:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z",
        "messages": [
            {
                "id": 455,
                "conversation_id": 123,
                "role": "user",
                "content": "What is Laravel?",
                "model_used": null,
                "created_at": "2024-01-15T10:25:00.000000Z",
                "updated_at": "2024-01-15T10:25:00.000000Z"
            },
            {
                "id": 456,
                "conversation_id": 123,
                "role": "assistant",
                "content": "Laravel is a PHP web framework...",
                "model_used": "deepseek-chat",
                "created_at": "2024-01-15T10:30:00.000000Z",
                "updated_at": "2024-01-15T10:30:00.000000Z"
            }
        ]
    },
    "timestamp": "2024-01-15T10:35:00.000000Z"
}
```

**Status Codes**:
- `200 OK`: Conversation found
- `404 Not Found`: Conversation not found

### 4. Update Conversation Title

Update the title of a specific conversation.

**Endpoint**: `PATCH /conversations/{id}/title`

**Request Body**:
```json
{
    "title": "New Conversation Title"
}
```

**Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `title` | string | Yes | New conversation title (max 255 chars) |

**Response**:
```json
{
    "success": true,
    "data": {
        "id": 123,
        "title": "New Conversation Title",
        "created_at": "2024-01-15T10:25:00.000000Z",
        "updated_at": "2024-01-15T10:40:00.000000Z"
    },
    "timestamp": "2024-01-15T10:40:00.000000Z"
}
```

**Status Codes**:
- `200 OK`: Title updated successfully
- `404 Not Found`: Conversation not found
- `422 Unprocessable Entity`: Validation errors

### 5. Delete Conversation

Delete a specific conversation and all its messages.

**Endpoint**: `DELETE /conversations/{id}`

**Response**:
```json
{
    "success": true,
    "message": "Conversation deleted successfully.",
    "timestamp": "2024-01-15T10:45:00.000000Z"
}
```

**Status Codes**:
- `200 OK`: Conversation deleted successfully
- `404 Not Found`: Conversation not found

## Monitoring Endpoints

### 6. Get System Metrics

Retrieve comprehensive system metrics and health information.

**Endpoint**: `GET /metrics`

**Response**:
```json
{
    "success": true,
    "data": {
        "system_health": {
            "status": "healthy",
            "checks": {
                "database": {
                    "status": "healthy",
                    "response_time": 12.5
                },
                "cache": {
                    "status": "healthy"
                },
                "memory": {
                    "status": "healthy",
                    "usage_bytes": 67108864,
                    "usage_mb": 64.0,
                    "percentage": 25.6
                }
            },
            "timestamp": "2024-01-15T10:50:00.000000Z"
        },
        "service_metrics": {
            "deepseek": {
                "total_calls": 150,
                "successful_calls": 145,
                "failed_calls": 5,
                "average_duration": 1.25,
                "total_duration": 187.5
            },
            "gemini": {
                "total_calls": 75,
                "successful_calls": 72,
                "failed_calls": 3,
                "average_duration": 0.95,
                "total_duration": 71.25
            },
            "huggingface": {
                "total_calls": 25,
                "successful_calls": 23,
                "failed_calls": 2,
                "average_duration": 2.1,
                "total_duration": 52.5
            }
        },
        "interaction_metrics": {
            "total_interactions": 250,
            "ask_requests": 200,
            "conversation_views": 30,
            "conversation_deletions": 5,
            "title_updates": 15
        },
        "search_metrics": {
            "total_searches": 180,
            "average_results": 3.2,
            "average_search_time": 0.15,
            "total_search_time": 27.0
        },
        "performance_metrics": {
            "operations": {
                "database_query": {
                    "count": 500,
                    "total_duration": 12.5,
                    "average_duration": 0.025,
                    "min_duration": 0.005,
                    "max_duration": 0.15
                }
            }
        },
        "error_metrics": {
            "total_errors": 8,
            "error_levels": {
                "emergency": 0,
                "alert": 0,
                "critical": 1,
                "error": 3,
                "warning": 4,
                "notice": 0,
                "info": 0,
                "debug": 0
            }
        }
    },
    "timestamp": "2024-01-15T10:50:00.000000Z"
}
```

### 7. Clear System Metrics

Clear all collected system metrics.

**Endpoint**: `DELETE /metrics`

**Response**:
```json
{
    "success": true,
    "message": "Metrics cleared successfully.",
    "timestamp": "2024-01-15T10:55:00.000000Z"
}
```

## Utility Endpoints

### 8. Health Check

Simple health check endpoint.

**Endpoint**: `GET /health`

**Response**:
```json
{
    "status": "ok",
    "timestamp": "2024-01-15T11:00:00.000000Z",
    "version": "1.0.0",
    "services": {
        "database": "ok",
        "cache": "ok"
    }
}
```

### 9. API Information

Get information about the API.

**Endpoint**: `GET /info`

**Response**:
```json
{
    "name": "WIDDX AI Assistant API",
    "version": "1.0.0",
    "description": "Intelligent AI assistant that routes queries to specialized AI models",
    "endpoints": {
        "POST /api/ask": "Send a query to the AI assistant",
        "GET /api/health": "Check API health status",
        "GET /api/info": "Get API information"
    },
    "supported_services": {
        "deepseek": "Code generation and logic",
        "gemini": "Research and data extraction",
        "huggingface": "NLP and creative tasks"
    },
    "features": [
        "intelligent_routing",
        "knowledge_base_search",
        "conversation_management",
        "fallback_strategies"
    ]
}
```

## Error Responses

All error responses follow a consistent format:

```json
{
    "success": false,
    "error": {
        "code": "ERROR_CODE",
        "message": "Human-readable error message",
        "details": "Additional error details (optional)"
    },
    "timestamp": "2024-01-15T11:05:00.000000Z",
    "request_id": "req_abc123def456"
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 422 | Request validation failed |
| `CONVERSATION_NOT_FOUND` | 404 | Requested conversation not found |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `AI_SERVICE_ERROR` | 502 | Error communicating with AI service |
| `SERVICE_UNAVAILABLE` | 503 | AI services temporarily unavailable |
| `INTERNAL_ERROR` | 500 | Unexpected server error |

## Response Headers

All responses include these headers:

- `Content-Type: application/json`
- `X-Request-ID`: Unique request identifier
- `X-RateLimit-*`: Rate limiting information (when applicable)

## Best Practices

### 1. Error Handling
Always check the `success` field in responses and handle errors appropriately.

### 2. Rate Limiting
Implement exponential backoff when receiving 429 responses.

### 3. Caching
The API implements intelligent caching. Identical requests may return cached responses for better performance.

### 4. Request IDs
Use the `X-Request-ID` header for debugging and support requests.

### 5. Pagination
Always handle pagination properly when fetching conversation lists.

## Examples

### cURL Examples

#### Ask a Question
```bash
curl -X POST http://localhost:8000/api/ask \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What is Laravel?",
    "use_knowledge_base": true
  }'
```

#### Get Conversations
```bash
curl -X GET "http://localhost:8000/api/conversations?page=1&per_page=10"
```

#### Update Conversation Title
```bash
curl -X PATCH http://localhost:8000/api/conversations/123/title \
  -H "Content-Type: application/json" \
  -d '{"title": "Updated Title"}'
```

### JavaScript Examples

#### Using Fetch API
```javascript
// Ask a question
const response = await fetch('/api/ask', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    message: 'Explain Laravel Eloquent ORM',
    use_knowledge_base: true,
    preferred_service: 'deepseek'
  })
});

const data = await response.json();
if (data.success) {
  console.log('AI Response:', data.data.response.content);
} else {
  console.error('Error:', data.error.message);
}
```

#### Using Axios
```javascript
import axios from 'axios';

try {
  const response = await axios.post('/api/ask', {
    message: 'How do I create a Laravel migration?',
    tags: ['laravel', 'database']
  });
  
  console.log('Response:', response.data.data.response.content);
} catch (error) {
  if (error.response?.status === 429) {
    console.log('Rate limited. Retry after:', error.response.headers['x-ratelimit-reset']);
  } else {
    console.error('Error:', error.response?.data?.error?.message);
  }
}
```
