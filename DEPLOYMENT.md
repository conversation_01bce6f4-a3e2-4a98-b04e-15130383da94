# WIDDX AI Assistant - Deployment Guide

This guide covers deploying the WIDDX AI Assistant to production environments.

## 🚀 Production Deployment

### Prerequisites

- **Server Requirements**:
  - Ubuntu 20.04+ or CentOS 8+
  - PHP 8.1+ with required extensions
  - MySQL 8.0+ or PostgreSQL 13+
  - Redis 6.0+
  - Nginx or Apache
  - SSL certificate

- **AI Service API Keys**:
  - DeepSeek API key
  - Google Gemini API key
  - HuggingFace API key

### 1. Server Setup

#### Install PHP and Extensions
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-redis php8.1-curl php8.1-json php8.1-mbstring php8.1-xml php8.1-zip php8.1-gd

# CentOS/RHEL
sudo dnf install php php-fpm php-mysqlnd php-redis php-curl php-json php-mbstring php-xml php-zip php-gd
```

#### Install Composer
```bash
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### Install and Configure MySQL
```bash
sudo apt install mysql-server
sudo mysql_secure_installation

# Create database and user
mysql -u root -p
CREATE DATABASE widdx_ai;
CREATE USER 'widdx_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON widdx_ai.* TO 'widdx_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### Install and Configure Redis
```bash
sudo apt install redis-server
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Configure Redis (optional)
sudo nano /etc/redis/redis.conf
# Set: maxmemory 256mb
# Set: maxmemory-policy allkeys-lru
sudo systemctl restart redis-server
```

### 2. Application Deployment

#### Clone and Setup Application
```bash
# Clone repository
cd /var/www
sudo git clone https://github.com/your-username/widdx-ai.git
sudo chown -R www-data:www-data widdx-ai
cd widdx-ai

# Install dependencies
sudo -u www-data composer install --no-dev --optimize-autoloader

# Set permissions
sudo chmod -R 755 storage bootstrap/cache
sudo chown -R www-data:www-data storage bootstrap/cache
```

#### Environment Configuration
```bash
# Copy and configure environment
sudo -u www-data cp .env.example .env
sudo -u www-data php artisan key:generate

# Edit environment file
sudo nano .env
```

**Production .env Configuration**:
```env
APP_NAME="WIDDX AI Assistant"
APP_ENV=production
APP_KEY=base64:your_generated_key_here
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=widdx_ai
DB_USERNAME=widdx_user
DB_PASSWORD=secure_password

# Cache
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# AI Services
DEEPSEEK_ENABLED=true
DEEPSEEK_API_KEY=your_deepseek_key
DEEPSEEK_API_URL=https://api.deepseek.com/v1/chat/completions

GEMINI_ENABLED=true
GEMINI_API_KEY=your_gemini_key
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models

HUGGINGFACE_ENABLED=true
HUGGINGFACE_API_KEY=your_huggingface_key
HUGGINGFACE_API_URL=https://api-inference.huggingface.co/models

# Knowledge Base
KNOWLEDGE_BASE_ENABLED=true
KNOWLEDGE_BASE_USE_MOCK_EMBEDDINGS=false
KNOWLEDGE_BASE_EMBEDDING_SERVICE_URL=https://api-inference.huggingface.co/pipeline/feature-extraction/sentence-transformers/all-MiniLM-L6-v2

# Caching
AI_CACHING_ENABLED=true
AI_CACHING_AI_RESPONSE_TTL=7200
AI_CACHING_CONVERSATIONS_LIST_TTL=900

# Rate Limiting
AI_RATE_LIMIT_ENABLED=true
AI_RATE_LIMIT_MAX=60
AI_RATE_LIMIT_PERIOD=60

# Logging
LOG_CHANNEL=daily
LOG_LEVEL=info
```

#### Database Migration and Seeding
```bash
# Run migrations
sudo -u www-data php artisan migrate --force

# Seed database (optional)
sudo -u www-data php artisan db:seed --force

# Cache configuration
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache
```

### 3. Web Server Configuration

#### Nginx Configuration
```bash
sudo nano /etc/nginx/sites-available/widdx-ai
```

```nginx
server {
    listen 80;
    listen [::]:80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    root /var/www/widdx-ai/public;

    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    index index.php;

    charset utf-8;

    # Laravel Routes
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP-FPM Configuration
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    # Security
    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Static Assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Logs
    access_log /var/log/nginx/widdx-ai.access.log;
    error_log /var/log/nginx/widdx-ai.error.log;
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/widdx-ai /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4. Process Management

#### Supervisor Configuration for Queue Workers
```bash
sudo apt install supervisor

# Create supervisor configuration
sudo nano /etc/supervisor/conf.d/widdx-ai-worker.conf
```

```ini
[program:widdx-ai-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/widdx-ai/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/widdx-ai/storage/logs/worker.log
stopwaitsecs=3600
```

```bash
# Start supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start widdx-ai-worker:*
```

#### Systemd Service (Alternative)
```bash
sudo nano /etc/systemd/system/widdx-ai-queue.service
```

```ini
[Unit]
Description=WIDDX AI Queue Worker
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/widdx-ai
ExecStart=/usr/bin/php artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```bash
sudo systemctl enable widdx-ai-queue
sudo systemctl start widdx-ai-queue
```

### 5. Monitoring and Logging

#### Log Rotation
```bash
sudo nano /etc/logrotate.d/widdx-ai
```

```
/var/www/widdx-ai/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 644 www-data www-data
    postrotate
        /usr/bin/supervisorctl restart widdx-ai-worker:*
    endscript
}
```

#### Health Check Script
```bash
sudo nano /usr/local/bin/widdx-ai-health-check.sh
```

```bash
#!/bin/bash

API_URL="https://your-domain.com/api/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $API_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "$(date): API is healthy"
    exit 0
else
    echo "$(date): API is unhealthy (HTTP $RESPONSE)"
    # Send alert (email, Slack, etc.)
    exit 1
fi
```

```bash
sudo chmod +x /usr/local/bin/widdx-ai-health-check.sh

# Add to crontab
sudo crontab -e
# Add: */5 * * * * /usr/local/bin/widdx-ai-health-check.sh >> /var/log/widdx-ai-health.log 2>&1
```

## 🐳 Docker Deployment

### Docker Compose Setup

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: widdx-ai-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./storage:/var/www/storage
    networks:
      - widdx-network
    depends_on:
      - db
      - redis
    environment:
      - APP_ENV=production
      - APP_DEBUG=false

  nginx:
    image: nginx:alpine
    container_name: widdx-ai-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx:/etc/nginx/conf.d
      - ./docker/ssl:/etc/ssl/certs
    networks:
      - widdx-network
    depends_on:
      - app

  db:
    image: mysql:8.0
    container_name: widdx-ai-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: widdx_ai
      MYSQL_USER: widdx_user
      MYSQL_PASSWORD: secure_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - widdx-network

  redis:
    image: redis:7-alpine
    container_name: widdx-ai-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - widdx-network

  queue:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: widdx-ai-queue
    restart: unless-stopped
    command: php artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - widdx-network
    depends_on:
      - db
      - redis

volumes:
  db_data:
  redis_data:

networks:
  widdx-network:
    driver: bridge
```

### Production Dockerfile

Create `Dockerfile.prod`:

```dockerfile
FROM php:8.1-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    && docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Set working directory
WORKDIR /var/www

# Copy application files
COPY . /var/www

# Install PHP dependencies
RUN composer install --no-dev --optimize-autoloader

# Set permissions
RUN chown -R www-data:www-data /var/www \
    && chmod -R 755 /var/www/storage \
    && chmod -R 755 /var/www/bootstrap/cache

# Expose port 9000
EXPOSE 9000

CMD ["php-fpm"]
```

### Deploy with Docker
```bash
# Build and start containers
docker-compose -f docker-compose.prod.yml up -d --build

# Run migrations
docker-compose -f docker-compose.prod.yml exec app php artisan migrate --force

# Cache configuration
docker-compose -f docker-compose.prod.yml exec app php artisan config:cache
docker-compose -f docker-compose.prod.yml exec app php artisan route:cache
docker-compose -f docker-compose.prod.yml exec app php artisan view:cache
```

## 🔧 Performance Optimization

### PHP-FPM Tuning
```bash
sudo nano /etc/php/8.1/fpm/pool.d/www.conf
```

```ini
; Process management
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 1000

; Performance
request_terminate_timeout = 300
rlimit_files = 65536
```

### MySQL Optimization
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

```ini
[mysqld]
# InnoDB Settings
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Query Cache
query_cache_type = 1
query_cache_size = 128M

# Connection Settings
max_connections = 200
```

### Redis Optimization
```bash
sudo nano /etc/redis/redis.conf
```

```ini
# Memory
maxmemory 512mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Network
tcp-keepalive 300
timeout 0
```

## 📊 Monitoring Setup

### Application Monitoring
```bash
# Install monitoring tools
sudo apt install htop iotop nethogs

# Create monitoring script
sudo nano /usr/local/bin/widdx-ai-monitor.sh
```

```bash
#!/bin/bash

LOG_FILE="/var/log/widdx-ai-monitor.log"
API_URL="https://your-domain.com/api/metrics"

# Get system metrics
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
DISK_USAGE=$(df -h / | awk 'NR==2{printf "%s", $5}')

# Get API metrics
API_RESPONSE=$(curl -s $API_URL)
API_STATUS=$?

echo "$(date): CPU: ${CPU_USAGE}%, Memory: ${MEMORY_USAGE}%, Disk: ${DISK_USAGE}, API: $API_STATUS" >> $LOG_FILE

# Alert if thresholds exceeded
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "$(date): HIGH CPU USAGE: ${CPU_USAGE}%" >> $LOG_FILE
fi

if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
    echo "$(date): HIGH MEMORY USAGE: ${MEMORY_USAGE}%" >> $LOG_FILE
fi
```

## 🔒 Security Hardening

### Firewall Configuration
```bash
# Install and configure UFW
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw status
```

### SSL/TLS Configuration
```bash
# Install Certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Application Security
```bash
# Set secure file permissions
sudo find /var/www/widdx-ai -type f -exec chmod 644 {} \;
sudo find /var/www/widdx-ai -type d -exec chmod 755 {} \;
sudo chmod -R 755 /var/www/widdx-ai/storage
sudo chmod -R 755 /var/www/widdx-ai/bootstrap/cache

# Secure sensitive files
sudo chmod 600 /var/www/widdx-ai/.env
sudo chown root:root /var/www/widdx-ai/.env
```

## 🔄 Backup Strategy

### Database Backup Script
```bash
sudo nano /usr/local/bin/widdx-ai-backup.sh
```

```bash
#!/bin/bash

BACKUP_DIR="/var/backups/widdx-ai"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="widdx_ai"
DB_USER="widdx_user"
DB_PASS="secure_password"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Application backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz -C /var/www widdx-ai --exclude='widdx-ai/storage/logs' --exclude='widdx-ai/node_modules'

# Clean old backups (keep 7 days)
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "$(date): Backup completed - $DATE"
```

```bash
sudo chmod +x /usr/local/bin/widdx-ai-backup.sh

# Schedule daily backups
sudo crontab -e
# Add: 0 2 * * * /usr/local/bin/widdx-ai-backup.sh >> /var/log/widdx-ai-backup.log 2>&1
```

## 🚨 Troubleshooting

### Common Issues

1. **Permission Errors**:
   ```bash
   sudo chown -R www-data:www-data /var/www/widdx-ai/storage
   sudo chmod -R 755 /var/www/widdx-ai/storage
   ```

2. **Queue Not Processing**:
   ```bash
   sudo supervisorctl restart widdx-ai-worker:*
   # or
   sudo systemctl restart widdx-ai-queue
   ```

3. **High Memory Usage**:
   ```bash
   # Clear application cache
   php artisan cache:clear
   php artisan config:clear
   php artisan route:clear
   php artisan view:clear
   ```

4. **Database Connection Issues**:
   ```bash
   # Test database connection
   php artisan tinker
   DB::connection()->getPdo();
   ```

### Log Locations
- Application logs: `/var/www/widdx-ai/storage/logs/`
- Nginx logs: `/var/log/nginx/`
- PHP-FPM logs: `/var/log/php8.1-fpm.log`
- MySQL logs: `/var/log/mysql/`
- System logs: `/var/log/syslog`

This deployment guide provides a comprehensive setup for production environments. Adjust configurations based on your specific requirements and infrastructure.
