# WIDDX AI - Intelligent Multi-Model AI Assistant

<p align="center">
  <img src="https://img.shields.io/badge/Laravel-11.x-red.svg" alt="Laravel Version">
  <img src="https://img.shields.io/badge/Vue.js-3.x-green.svg" alt="Vue.js Version">
  <img src="https://img.shields.io/badge/PHP-8.2+-blue.svg" alt="PHP Version">
  <img src="https://img.shields.io/badge/License-MIT-yellow.svg" alt="License">
</p>

WIDDX AI is a sophisticated local smart assistant that integrates multiple Large Language Models (DeepSeek, Gemini, HuggingFace) with a self-learning knowledge base system. Built with Laravel backend and Vue 3 frontend, it provides intelligent routing, conversation management, and automatic knowledge accumulation.

## ✨ Features

### 🤖 Multi-Model AI Integration
- **DeepSeek**: Specialized for coding and technical queries
- **Gemini**: Optimized for research and analysis
- **HuggingFace**: Creative writing and general tasks
- **Intelligent Routing**: Automatic model selection based on query type
- **Fallback System**: Ensures reliability with backup models

### 🧠 Self-Learning Knowledge Base
- **Automatic Response Saving**: High-quality AI responses are automatically saved
- **Local Response Priority**: Uses local knowledge for similar queries (85%+ similarity)
- **Confidence Scoring**: Dynamic scoring based on model and response quality
- **Smart Tagging**: Automatic tag generation for better organization
- **Admin Management**: Full CRUD interface for knowledge entries

### 💬 Modern Chat Interface
- **Real-time Messaging**: Smooth, responsive chat experience
- **Message Editing**: Edit and re-send previous messages
- **Model Indicators**: Visual badges showing which AI model responded
- **Knowledge Badges**: Clear indication when local knowledge is used
- **Mobile Responsive**: Optimized for all device sizes
- **Dark Mode**: Professional dark theme throughout

### 🔧 Advanced Features
- **Conversation Management**: Persistent chat history
- **Context Awareness**: Maintains conversation context
- **Rate Limiting**: Prevents API abuse
- **Caching System**: Optimized performance
- **Comprehensive Logging**: Detailed request/response tracking
- **Error Handling**: Graceful degradation and user feedback

## 🚀 Quick Start

### Prerequisites
- PHP 8.2+
- Node.js 18+
- MySQL 8.0+
- Composer
- API keys for AI services

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/widdx-ai.git
   cd widdx-ai
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Configure your `.env` file**
   ```env
   # Database
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=widdx_ai
   DB_USERNAME=your_username
   DB_PASSWORD=your_password

   # AI Service API Keys
   DEEPSEEK_API_KEY=your_deepseek_key
   GEMINI_API_KEY=your_gemini_key
   HUGGINGFACE_API_KEY=your_huggingface_key

   # Self-Learning Configuration
   AI_SELF_LEARNING_ENABLED=true
   AI_SELF_LEARNING_MIN_CONFIDENCE_SCORE=0.6
   AI_SELF_LEARNING_LOCAL_RESPONSE_THRESHOLD=0.85
   ```

6. **Database setup**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

7. **Build frontend assets**
   ```bash
   npm run build
   ```

8. **Start the development server**
   ```bash
   php artisan serve
   ```

Visit `http://localhost:8000` to access WIDDX AI!

## 🏗️ Architecture

### Backend (Laravel)
```
app/
├── Http/Controllers/Api/
│   ├── AssistantController.php    # Main chat endpoint
│   └── KnowledgeController.php    # Knowledge base management
├── Models/
│   ├── Conversation.php           # Chat conversations
│   ├── Message.php               # Individual messages
│   └── KnowledgeEntry.php        # Knowledge base entries
├── Services/
│   ├── DecisionEngine.php        # AI model routing
│   ├── KnowledgeBaseService.php  # Knowledge management
│   └── AI/                       # AI service integrations
└── Resources/
    └── AssistantResponseResource.php
```

### Frontend (Vue 3)
```
resources/js/
├── components/
│   ├── ChatMessage.vue           # Individual message display
│   ├── ChatInput.vue            # Message input with options
│   ├── ModelBadge.vue           # AI model indicators
│   └── KnowledgeEntryModal.vue  # Admin knowledge management
├── views/
│   ├── ChatView.vue             # Main chat interface
│   └── AdminView.vue            # Knowledge base admin
├── stores/
│   └── chatStore.js             # Pinia state management
└── services/
    └── chatService.js           # API communication
```

## 🧪 Testing

### Backend Tests (PHPUnit)
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature

# Run with coverage
php artisan test --coverage
```

### Frontend Tests (Vitest)
```bash
# Run all frontend tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Generate coverage report
npm run test:coverage
```

## 📚 API Documentation

Comprehensive API documentation is available at [docs/API.md](docs/API.md).

### Key Endpoints

- `POST /api/ask` - Send message to AI assistant
- `GET /api/conversations` - List conversations
- `GET /api/conversations/{id}` - Get conversation details
- `GET /api/knowledge` - List knowledge entries
- `POST /api/knowledge` - Create knowledge entry
- `GET /api/knowledge/search` - Search knowledge base
