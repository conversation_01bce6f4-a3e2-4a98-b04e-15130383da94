<?php

namespace App\Exceptions;

use Exception;

class AIServiceException extends Exception
{
    /**
     * @var string|null The AI service that caused the exception
     */
    protected ?string $serviceName = null;

    /**
     * @var array Additional error details
     */
    protected array $errorDetails = [];

    /**
     * Create a new AI service exception
     *
     * @param string $message The exception message
     * @param string|null $serviceName The name of the AI service
     * @param array $errorDetails Additional error details
     * @param int $code The exception code
     * @param \Throwable|null $previous The previous exception
     */
    public function __construct(
        string $message, 
        ?string $serviceName = null, 
        array $errorDetails = [], 
        int $code = 0, 
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->serviceName = $serviceName;
        $this->errorDetails = $errorDetails;
    }

    /**
     * Get the name of the AI service that caused the exception
     *
     * @return string|null
     */
    public function getServiceName(): ?string
    {
        return $this->serviceName;
    }

    /**
     * Get additional error details
     *
     * @return array
     */
    public function getErrorDetails(): array
    {
        return $this->errorDetails;
    }

    /**
     * Convert the exception to an array for API responses
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'success' => false,
            'error' => [
                'code' => class_basename($this),
                'message' => $this->getMessage(),
                'details' => $this->getErrorDetails(),
                'service' => $this->getServiceName(),
            ]
        ];
    }
}