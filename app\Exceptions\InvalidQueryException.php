<?php

namespace App\Exceptions;

class InvalidQueryException extends AIServiceException
{
    /**
     * Create a new invalid query exception
     *
     * @param string $message The error message
     * @param array $errorDetails Additional error details
     * @param \Throwable|null $previous The previous exception
     */
    public function __construct(
        string $message = "Invalid query format", 
        array $errorDetails = [], 
        \Throwable $previous = null
    ) {
        parent::__construct(
            $message, 
            null, 
            $errorDetails, 
            400, 
            $previous
        );
    }
}