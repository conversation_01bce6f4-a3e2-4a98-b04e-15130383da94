<?php

namespace App\Exceptions;

class ServiceUnavailableException extends AIServiceException
{
    /**
     * Create a new service unavailable exception
     *
     * @param string $message The exception message
     * @param string|null $serviceName The name of the AI service
     * @param array $errorDetails Additional error details
     * @param int $code The exception code
     * @param \Throwable|null $previous The previous exception
     */
    public function __construct(
        string $message = 'AI service is temporarily unavailable', 
        ?string $serviceName = null, 
        array $errorDetails = [], 
        int $code = 503, 
        \Throwable $previous = null
    ) {
        parent::__construct($message, $serviceName, $errorDetails, $code, $previous);
    }
}