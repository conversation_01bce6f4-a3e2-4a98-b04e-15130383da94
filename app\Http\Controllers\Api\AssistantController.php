<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\AskRequest;
use App\Http\Resources\AssistantResponseResource;
use App\Models\Conversation;
use App\Models\Message;
use App\Services\DecisionEngine;
use App\Services\KnowledgeBaseService;
use App\Services\CacheService;
use App\Services\MonitoringService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class AssistantController extends Controller
{
    /**
     * @var DecisionEngine
     */
    protected DecisionEngine $decisionEngine;

    /**
     * @var KnowledgeBaseService
     */
    protected KnowledgeBaseService $knowledgeBaseService;

    /**
     * @var CacheService
     */
    protected CacheService $cacheService;

    /**
     * @var MonitoringService
     */
    protected MonitoringService $monitoringService;

    /**
     * Create a new controller instance.
     */
    public function __construct(DecisionEngine $decisionEngine, KnowledgeBaseService $knowledgeBaseService, CacheService $cacheService, MonitoringService $monitoringService)
    {
        $this->decisionEngine = $decisionEngine;
        $this->knowledgeBaseService = $knowledgeBaseService;
        $this->cacheService = $cacheService;
        $this->monitoringService = $monitoringService;
    }

    /**
     * Handle user query and return AI response
     *
     * @param AskRequest $request
     * @return JsonResponse
     */
    public function ask(AskRequest $request): JsonResponse
    {
        $startTime = microtime(true);

        // Log user interaction
        $this->monitoringService->logUserInteraction('ask', [
            'message_length' => strlen($request->getMessage()),
            'use_knowledge_base' => $request->shouldUseKnowledgeBase(),
            'preferred_service' => $request->getPreferredService(),
            'tags' => $request->getTags(),
        ]);

        try {
            DB::beginTransaction();

            // Get or create conversation
            $conversation = $this->getOrCreateConversation($request);

            // Store user message
            $userMessage = $this->storeUserMessage($conversation, $request);

            // Search knowledge base if enabled
            $knowledgeResults = $this->searchKnowledgeBase($request);

            // Prepare context for AI service
            $context = $this->prepareContext($request, $knowledgeResults);

            // Get AI response
            $aiResponse = $this->getAIResponse($request->getMessage(), $context, $request->getPreferredService());

            // Store assistant message
            $assistantMessage = $this->storeAssistantMessage($conversation, $aiResponse);

            // Auto-save to knowledge base for self-learning
            $this->autoSaveToKnowledgeBase($request, $aiResponse);

            // Update conversation title if it's the first message
            if ($conversation->messages()->count() === 2) { // User + Assistant
                $conversation->generateTitle($request->getMessage());
            }

            DB::commit();

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            // Prepare response data
            $responseData = [
                'message' => $assistantMessage,
                'conversation' => $conversation,
                'response' => [
                    'content' => $aiResponse['result']['content'] ?? $aiResponse['result']['message'] ?? '',
                    'model_used' => $aiResponse['result']['model'] ?? $aiResponse['service_used'],
                    'service_used' => $aiResponse['service_used'],
                    'fallback_used' => $aiResponse['fallback_used'],
                    'processing_time' => $processingTime,
                ],
                'knowledge_base' => [
                    'used' => $request->shouldUseKnowledgeBase() && $knowledgeResults->isNotEmpty(),
                    'entries_found' => $knowledgeResults->count(),
                    'entries' => $knowledgeResults,
                    'threshold_used' => $request->getKnowledgeThreshold(),
                ],
                'metadata' => [
                    'query_length' => strlen($request->getMessage()),
                    'context_items' => count($context),
                    'processing_time_ms' => $processingTime,
                ],
            ];

            Log::info('AssistantController: Request processed successfully', [
                'conversation_id' => $conversation->id,
                'service_used' => $aiResponse['service_used'],
                'fallback_used' => $aiResponse['fallback_used'],
                'processing_time' => $processingTime,
                'knowledge_entries_found' => $knowledgeResults->count(),
            ]);

            return response()->json(new AssistantResponseResource($responseData));

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('AssistantController: Request failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'message_length' => strlen($request->getMessage()),
            ]);

            return $this->handleError($e);
        }
    }

    /**
     * Get existing conversation or create a new one
     *
     * @param AskRequest $request
     * @return Conversation
     */
    protected function getOrCreateConversation(AskRequest $request): Conversation
    {
        $conversationId = $request->getConversationId();

        if ($conversationId) {
            $conversation = Conversation::find($conversationId);
            if ($conversation) {
                return $conversation;
            }
        }

        // Create new conversation with temporary title
        return Conversation::create([
            'title' => 'New Conversation',
        ]);
    }

    /**
     * Store user message in the database
     *
     * @param Conversation $conversation
     * @param AskRequest $request
     * @return Message
     */
    protected function storeUserMessage(Conversation $conversation, AskRequest $request): Message
    {
        return Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => $request->getMessage(),
            'model_used' => null,
            'metadata' => [
                'context' => $request->getContext(),
                'preferred_service' => $request->getPreferredService(),
                'use_knowledge_base' => $request->shouldUseKnowledgeBase(),
                'tags' => $request->getTags(),
                'source_filter' => $request->getSourceFilter(),
            ],
        ]);
    }

    /**
     * Search knowledge base for relevant information
     *
     * @param AskRequest $request
     * @return \Illuminate\Support\Collection
     */
    protected function searchKnowledgeBase(AskRequest $request): \Illuminate\Support\Collection
    {
        if (!$request->shouldUseKnowledgeBase() || !$this->knowledgeBaseService->isEnabled()) {
            return collect();
        }

        $searchStartTime = microtime(true);

        try {
            $results = $this->knowledgeBaseService->search(
                $request->getMessage(),
                $request->getKnowledgeThreshold(),
                $request->getMaxKnowledgeResults(),
                $request->getTags(),
                $request->getSourceFilter()
            );

            $searchTime = microtime(true) - $searchStartTime;

            // Log knowledge base search
            $this->monitoringService->logKnowledgeBaseSearch(
                $request->getMessage(),
                $results->count(),
                $searchTime,
                $request->getKnowledgeThreshold(),
                $request->getTags(),
                $request->getSourceFilter()
            );

            return $results;
        } catch (Exception $e) {
            $searchTime = microtime(true) - $searchStartTime;

            Log::warning('AssistantController: Knowledge base search failed', [
                'error' => $e->getMessage(),
                'search_time' => $searchTime,
            ]);

            // Log failed search
            $this->monitoringService->logKnowledgeBaseSearch(
                $request->getMessage(),
                0,
                $searchTime,
                $request->getKnowledgeThreshold(),
                $request->getTags(),
                $request->getSourceFilter()
            );

            return collect();
        }
    }

    /**
     * Prepare context for AI service
     *
     * @param AskRequest $request
     * @param \Illuminate\Support\Collection $knowledgeResults
     * @return array
     */
    protected function prepareContext(AskRequest $request, \Illuminate\Support\Collection $knowledgeResults): array
    {
        $context = $request->getContext();

        // Add knowledge base results to context
        if ($knowledgeResults->isNotEmpty()) {
            $knowledgeContext = $knowledgeResults->map(function ($entry) {
                return "Knowledge: {$entry->content} (Source: {$entry->source}, Confidence: {$entry->confidence_score})";
            })->toArray();

            $context = array_merge(['--- Relevant Knowledge ---'], $knowledgeContext, ['--- End Knowledge ---'], $context);
        }

        return $context;
    }

    /**
     * Get AI response using decision engine
     *
     * @param string $message
     * @param array $context
     * @param string|null $preferredService
     * @return array
     */
    protected function getAIResponse(string $message, array $context, ?string $preferredService): array
    {
        // Generate cache key for this query
        $queryHash = $this->generateQueryHash($message, $context, $preferredService);

        // Try to get cached response first
        if (config('ai.caching.enabled', true)) {
            $cachedResponse = $this->cacheService->getCachedAIResponse($queryHash);
            if ($cachedResponse) {
                Log::debug('AssistantController: Using cached AI response', [
                    'query_hash' => $queryHash,
                ]);
                return $cachedResponse;
            }
        }

        $response = null;

        // If preferred service is specified, try to use it first
        if ($preferredService) {
            try {
                $result = $this->decisionEngine->executeWithService($preferredService, $message, $context);
                $response = [
                    'service_used' => $preferredService,
                    'fallback_used' => false,
                    'result' => $result,
                ];
            } catch (Exception $e) {
                Log::warning('AssistantController: Preferred service failed, falling back to decision engine', [
                    'preferred_service' => $preferredService,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Use decision engine for routing if no preferred service or it failed
        if (!$response) {
            $response = $this->decisionEngine->routeQuery($message, $context);
        }

        // Cache the response
        if (config('ai.caching.enabled', true) && $response) {
            $this->cacheService->cacheAIResponse($queryHash, $response, config('ai.caching.ai_response_ttl', 7200));
        }

        return $response;
    }

    /**
     * Store assistant message in the database
     *
     * @param Conversation $conversation
     * @param array $aiResponse
     * @return Message
     */
    protected function storeAssistantMessage(Conversation $conversation, array $aiResponse): Message
    {
        $content = $aiResponse['result']['content'] ?? $aiResponse['result']['message'] ?? '';
        $modelUsed = $aiResponse['result']['model'] ?? $aiResponse['service_used'];

        return Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'content' => $content,
            'model_used' => $modelUsed,
            'metadata' => [
                'service_used' => $aiResponse['service_used'],
                'fallback_used' => $aiResponse['fallback_used'],
                'raw_response' => $aiResponse['result'],
            ],
        ]);
    }

    /**
     * Auto-save assistant response to knowledge base for self-learning
     *
     * @param AskRequest $request
     * @param array $aiResponse
     * @return void
     */
    protected function autoSaveToKnowledgeBase(AskRequest $request, array $aiResponse): void
    {
        try {
            // Only save if auto-learning is enabled and response is valid
            if (!config('ai.self_learning.enabled', true)) {
                return;
            }

            $content = $aiResponse['result']['content'] ?? $aiResponse['result']['message'] ?? '';

            // Skip if content is too short or empty
            if (strlen(trim($content)) < config('ai.self_learning.min_content_length', 10)) {
                return;
            }

            // Skip if this was a fallback response (might be lower quality)
            if ($aiResponse['fallback_used'] && !config('ai.self_learning.save_fallback_responses', false)) {
                return;
            }

            // Determine confidence score based on service used and response quality
            $confidenceScore = $this->calculateConfidenceScore($aiResponse, $request);

            // Skip if confidence is too low
            if ($confidenceScore < config('ai.self_learning.min_confidence_score', 0.6)) {
                return;
            }

            // Generate tags based on the query and response
            $tags = $this->generateKnowledgeTags($request, $aiResponse);

            // Save to knowledge base
            $this->knowledgeBaseService->store([
                'content' => $content,
                'source' => 'ai_response',
                'tags' => $tags,
                'confidence_score' => $confidenceScore,
                'metadata' => [
                    'original_query' => $request->getMessage(),
                    'service_used' => $aiResponse['service_used'],
                    'model_used' => $aiResponse['result']['model'] ?? $aiResponse['service_used'],
                    'fallback_used' => $aiResponse['fallback_used'],
                    'created_at' => now()->toISOString(),
                ],
            ]);

            Log::debug('AssistantController: Response auto-saved to knowledge base', [
                'content_length' => strlen($content),
                'confidence_score' => $confidenceScore,
                'tags' => $tags,
                'service_used' => $aiResponse['service_used'],
            ]);

        } catch (Exception $e) {
            // Don't fail the main request if knowledge base saving fails
            Log::warning('AssistantController: Failed to auto-save to knowledge base', [
                'error' => $e->getMessage(),
                'service_used' => $aiResponse['service_used'] ?? 'unknown',
            ]);
        }
    }

    /**
     * Calculate confidence score for knowledge base entry
     *
     * @param array $aiResponse
     * @param AskRequest $request
     * @return float
     */
    protected function calculateConfidenceScore(array $aiResponse, AskRequest $request): float
    {
        $baseScore = 0.7; // Default confidence

        // Adjust based on service used
        $serviceScores = [
            'deepseek' => 0.85,
            'gemini' => 0.8,
            'huggingface' => 0.75,
        ];

        $serviceUsed = $aiResponse['service_used'];
        if (isset($serviceScores[$serviceUsed])) {
            $baseScore = $serviceScores[$serviceUsed];
        }

        // Reduce score if fallback was used
        if ($aiResponse['fallback_used']) {
            $baseScore *= 0.8;
        }

        // Adjust based on response length (longer responses might be more comprehensive)
        $content = $aiResponse['result']['content'] ?? $aiResponse['result']['message'] ?? '';
        $contentLength = strlen($content);

        if ($contentLength > 500) {
            $baseScore += 0.05;
        } elseif ($contentLength < 100) {
            $baseScore -= 0.1;
        }

        // Ensure score is within valid range
        return max(0.1, min(1.0, $baseScore));
    }

    /**
     * Generate tags for knowledge base entry
     *
     * @param AskRequest $request
     * @param array $aiResponse
     * @return array
     */
    protected function generateKnowledgeTags(AskRequest $request, array $aiResponse): array
    {
        $tags = [];

        // Add user-provided tags
        $userTags = $request->getTags();
        if (!empty($userTags)) {
            $tags = array_merge($tags, $userTags);
        }

        // Add service-based tag
        $serviceUsed = $aiResponse['service_used'];
        $tags[] = "service:{$serviceUsed}";

        // Add model-based tag if available
        $modelUsed = $aiResponse['result']['model'] ?? null;
        if ($modelUsed) {
            $tags[] = "model:{$modelUsed}";
        }

        // Add content-type tags based on simple heuristics
        $content = $aiResponse['result']['content'] ?? $aiResponse['result']['message'] ?? '';
        $query = strtolower($request->getMessage());

        // Programming/code related
        if (preg_match('/\b(code|function|class|method|variable|programming|php|javascript|python|laravel|vue|react)\b/', $query) ||
            preg_match('/```|function\s+\w+|class\s+\w+|\$\w+|<\?php/', $content)) {
            $tags[] = 'programming';
            $tags[] = 'code';
        }

        // Question/explanation
        if (preg_match('/\b(what|how|why|when|where|explain|describe|tell me)\b/', $query)) {
            $tags[] = 'explanation';
            $tags[] = 'question';
        }

        // Tutorial/guide
        if (preg_match('/\b(tutorial|guide|step|steps|how to|walkthrough)\b/', $query)) {
            $tags[] = 'tutorial';
            $tags[] = 'guide';
        }

        // Remove duplicates and return
        return array_unique($tags);
    }

    /**
     * Handle errors and return appropriate response
     *
     * @param Exception $e
     * @return JsonResponse
     */
    protected function handleError(Exception $e): JsonResponse
    {
        $statusCode = 500;
        $errorCode = 'INTERNAL_ERROR';
        $message = 'An unexpected error occurred while processing your request.';

        // Customize error response based on exception type
        if ($e instanceof \App\Exceptions\ServiceUnavailableException) {
            $statusCode = 503;
            $errorCode = 'SERVICE_UNAVAILABLE';
            $message = 'AI services are currently unavailable. Please try again later.';
        } elseif ($e instanceof \App\Exceptions\AIServiceException) {
            $statusCode = 502;
            $errorCode = 'AI_SERVICE_ERROR';
            $message = 'There was an error communicating with the AI service.';
        } elseif ($e instanceof \Illuminate\Validation\ValidationException) {
            $statusCode = 422;
            $errorCode = 'VALIDATION_ERROR';
            $message = 'The request data is invalid.';
        }

        return response()->json([
            'success' => false,
            'error' => [
                'code' => $errorCode,
                'message' => $message,
                'details' => config('app.debug') ? $e->getMessage() : null,
            ],
            'timestamp' => now()->toISOString(),
        ], $statusCode);
    }

    /**
     * Get conversation history
     *
     * @param int $conversationId
     * @return JsonResponse
     */
    public function getConversation(int $conversationId): JsonResponse
    {
        try {
            $conversation = Conversation::with(['messages' => function ($query) {
                $query->orderBy('created_at', 'asc');
            }])->find($conversationId);

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CONVERSATION_NOT_FOUND',
                        'message' => 'The requested conversation was not found.',
                    ],
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new \App\Http\Resources\ConversationResource($conversation),
                'timestamp' => now()->toISOString(),
            ]);

        } catch (Exception $e) {
            Log::error('AssistantController: Failed to get conversation', [
                'conversation_id' => $conversationId,
                'error' => $e->getMessage(),
            ]);

            return $this->handleError($e);
        }
    }

    /**
     * Get list of conversations
     *
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse
     */
    public function getConversations(\Illuminate\Http\Request $request): JsonResponse
    {
        try {
            $perPage = min($request->get('per_page', 20), 100); // Max 100 per page
            $page = $request->get('page', 1);

            // Generate cache key for this request
            $cacheKey = "page_{$page}_per_{$perPage}";

            // Try to get cached conversations list
            if (config('ai.caching.enabled', true)) {
                $cachedConversations = $this->cacheService->getCachedConversationsList($cacheKey);
                if ($cachedConversations) {
                    Log::debug('AssistantController: Using cached conversations list', [
                        'cache_key' => $cacheKey,
                    ]);

                    return response()->json([
                        'success' => true,
                        'data' => $cachedConversations,
                        'timestamp' => now()->toISOString(),
                        'cached' => true,
                    ]);
                }
            }

            $conversations = Conversation::with(['messages' => function ($query) {
                $query->latest()->limit(1); // Get only the latest message
            }])
            ->withCount('messages')
            ->orderBy('updated_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

            $responseData = [
                'success' => true,
                'data' => \App\Http\Resources\ConversationResource::collection($conversations),
                'meta' => [
                    'current_page' => $conversations->currentPage(),
                    'last_page' => $conversations->lastPage(),
                    'per_page' => $conversations->perPage(),
                    'total' => $conversations->total(),
                ],
                'timestamp' => now()->toISOString(),
            ];

            // Cache the conversations list
            if (config('ai.caching.enabled', true)) {
                $this->cacheService->cacheConversationsList($cacheKey, collect($responseData['data']), config('ai.caching.conversations_list_ttl', 900));
            }

            return response()->json($responseData);

        } catch (Exception $e) {
            Log::error('AssistantController: Failed to get conversations', [
                'error' => $e->getMessage(),
            ]);

            return $this->handleError($e);
        }
    }

    /**
     * Delete a conversation
     *
     * @param int $conversationId
     * @return JsonResponse
     */
    public function deleteConversation(int $conversationId): JsonResponse
    {
        try {
            $conversation = Conversation::find($conversationId);

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CONVERSATION_NOT_FOUND',
                        'message' => 'The requested conversation was not found.',
                    ],
                ], 404);
            }

            $conversation->delete(); // This will cascade delete messages

            // Invalidate related cache
            $this->cacheService->invalidateConversation($conversationId);
            $this->cacheService->invalidateConversationsList();

            Log::info('AssistantController: Conversation deleted', [
                'conversation_id' => $conversationId,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Conversation deleted successfully.',
                'timestamp' => now()->toISOString(),
            ]);

        } catch (Exception $e) {
            Log::error('AssistantController: Failed to delete conversation', [
                'conversation_id' => $conversationId,
                'error' => $e->getMessage(),
            ]);

            return $this->handleError($e);
        }
    }

    /**
     * Update conversation title
     *
     * @param int $conversationId
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse
     */
    public function updateConversationTitle(int $conversationId, \Illuminate\Http\Request $request): JsonResponse
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
            ]);

            $conversation = Conversation::find($conversationId);

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CONVERSATION_NOT_FOUND',
                        'message' => 'The requested conversation was not found.',
                    ],
                ], 404);
            }

            $conversation->update(['title' => $request->input('title')]);

            Log::info('AssistantController: Conversation title updated', [
                'conversation_id' => $conversationId,
                'new_title' => $request->input('title'),
            ]);

            return response()->json([
                'success' => true,
                'data' => new \App\Http\Resources\ConversationResource($conversation),
                'timestamp' => now()->toISOString(),
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'VALIDATION_ERROR',
                    'message' => 'The request data is invalid.',
                    'details' => $e->errors(),
                ],
            ], 422);

        } catch (Exception $e) {
            Log::error('AssistantController: Failed to update conversation title', [
                'conversation_id' => $conversationId,
                'error' => $e->getMessage(),
            ]);

            return $this->handleError($e);
        }
    }

    /**
     * Generate hash for query caching
     *
     * @param string $message
     * @param array $context
     * @param string|null $preferredService
     * @return string
     */
    protected function generateQueryHash(string $message, array $context, ?string $preferredService): string
    {
        $data = [
            'message' => $message,
            'context' => $context,
            'preferred_service' => $preferredService,
        ];

        return md5(serialize($data));
    }

    /**
     * Get system metrics and health status
     *
     * @return JsonResponse
     */
    public function getMetrics(): JsonResponse
    {
        try {
            $metrics = [
                'system_health' => $this->monitoringService->getSystemHealth(),
                'service_metrics' => $this->monitoringService->getServiceMetrics(),
                'interaction_metrics' => $this->monitoringService->getInteractionMetrics(),
                'search_metrics' => $this->monitoringService->getSearchMetrics(),
                'performance_metrics' => $this->monitoringService->getPerformanceMetrics(),
                'error_metrics' => $this->monitoringService->getErrorMetrics(),
                'timestamp' => now()->toISOString(),
            ];

            return response()->json([
                'success' => true,
                'data' => $metrics,
                'timestamp' => now()->toISOString(),
            ]);

        } catch (Exception $e) {
            Log::error('AssistantController: Failed to get metrics', [
                'error' => $e->getMessage(),
            ]);

            return $this->handleError($e);
        }
    }

    /**
     * Clear system metrics
     *
     * @return JsonResponse
     */
    public function clearMetrics(): JsonResponse
    {
        try {
            $this->monitoringService->clearMetrics();

            Log::info('AssistantController: Metrics cleared');

            return response()->json([
                'success' => true,
                'message' => 'Metrics cleared successfully.',
                'timestamp' => now()->toISOString(),
            ]);

        } catch (Exception $e) {
            Log::error('AssistantController: Failed to clear metrics', [
                'error' => $e->getMessage(),
            ]);

            return $this->handleError($e);
        }
    }
}
