<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\Conversation;
use App\Models\Message;
use App\Services\AIService;

class ChatController extends Controller
{
    protected $aiService;

    public function __construct(AIService $aiService)
    {
        $this->aiService = $aiService;
    }

    /**
     * Send a message to AI and get response
     */
    public function ask(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'message' => 'required|string|max:4000',
                'conversation_id' => 'nullable|integer|exists:conversations,id',
                'preferred_service' => 'nullable|string|in:auto,deepseek,gemini,huggingface',
                'use_knowledge_base' => 'boolean',
                'knowledge_threshold' => 'numeric|min:0|max:1',
                'max_knowledge_results' => 'integer|min:1|max:20',
                'tags' => 'array',
                'source_filter' => 'nullable|string',
                'context' => 'array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Get or create conversation
            $conversation = null;
            if (!empty($data['conversation_id'])) {
                $conversation = Conversation::find($data['conversation_id']);
            }

            if (!$conversation) {
                $conversation = Conversation::create([
                    'title' => $this->generateTitle($data['message']),
                    'user_id' => null, // For now, no user authentication
                    'metadata' => json_encode([
                        'preferred_service' => $data['preferred_service'] ?? 'auto',
                        'use_knowledge_base' => $data['use_knowledge_base'] ?? true
                    ])
                ]);
            }

            // Save user message
            $userMessage = Message::create([
                'conversation_id' => $conversation->id,
                'role' => 'user',
                'content' => $data['message'],
                'metadata' => json_encode([
                    'timestamp' => now(),
                    'ip_address' => $request->ip()
                ])
            ]);

            // Get AI response
            $aiResponse = $this->aiService->generateResponse([
                'message' => $data['message'],
                'conversation_id' => $conversation->id,
                'preferred_service' => $data['preferred_service'] ?? 'auto',
                'use_knowledge_base' => $data['use_knowledge_base'] ?? true,
                'knowledge_threshold' => $data['knowledge_threshold'] ?? 0.7,
                'max_knowledge_results' => $data['max_knowledge_results'] ?? 5,
                'context' => $data['context'] ?? []
            ]);

            // Save AI message
            $aiMessage = Message::create([
                'conversation_id' => $conversation->id,
                'role' => 'assistant',
                'content' => $aiResponse['content'],
                'model_used' => $aiResponse['service'] ?? 'auto',
                'metadata' => json_encode([
                    'model' => $aiResponse['model'] ?? 'unknown',
                    'service' => $aiResponse['service'] ?? 'unknown',
                    'timestamp' => now()->toISOString(),
                    'tokens_used' => $aiResponse['tokens_used'] ?? 0,
                    'processing_time' => $aiResponse['processing_time'] ?? 0
                ])
            ]);

            // Update conversation
            $conversation->update([
                'updated_at' => now(),
                'messages_count' => $conversation->messages()->count()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Response generated successfully',
                'data' => [
                    'message' => [
                        'id' => $aiMessage->id,
                        'content' => $aiMessage->content,
                        'role' => $aiMessage->role,
                        'conversation_id' => $conversation->id,
                        'created_at' => $aiMessage->created_at
                    ],
                    'conversation' => [
                        'id' => $conversation->id,
                        'title' => $conversation->title,
                        'messages_count' => $conversation->messages_count
                    ],
                    'model' => $aiResponse['model'] ?? 'auto',
                    'service' => $aiResponse['service'] ?? 'auto',
                    'tokens_used' => $aiResponse['tokens_used'] ?? 0,
                    'processing_time' => $aiResponse['processing_time'] ?? 0
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Chat error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get conversations list
     */
    public function conversations(Request $request): JsonResponse
    {
        try {
            $conversations = Conversation::with(['messages' => function($query) {
                $query->latest()->limit(1);
            }])
            ->withCount('messages')
            ->orderBy('updated_at', 'desc')
            ->limit(50)
            ->get()
            ->map(function($conversation) {
                return [
                    'id' => $conversation->id,
                    'title' => $conversation->title,
                    'messages_count' => $conversation->messages_count,
                    'preview' => $conversation->messages->first()?->content ?? 'No messages',
                    'created_at' => $conversation->created_at,
                    'updated_at' => $conversation->updated_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $conversations
            ]);

        } catch (\Exception $e) {
            Log::error('Conversations error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to load conversations',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get specific conversation
     */
    public function conversation(Request $request, $id): JsonResponse
    {
        try {
            $conversation = Conversation::with('messages')
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $conversation->id,
                    'title' => $conversation->title,
                    'messages' => $conversation->messages->map(function($message) {
                        return [
                            'id' => $message->id,
                            'role' => $message->role,
                            'content' => $message->content,
                            'created_at' => $message->created_at,
                            'metadata' => json_decode($message->metadata, true)
                        ];
                    }),
                    'created_at' => $conversation->created_at,
                    'updated_at' => $conversation->updated_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Conversation error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Conversation not found',
                'error' => config('app.debug') ? $e->getMessage() : 'Not found'
            ], 404);
        }
    }

    /**
     * Generate conversation title from first message
     */
    private function generateTitle(string $message): string
    {
        $title = substr($message, 0, 50);
        if (strlen($message) > 50) {
            $title .= '...';
        }
        return $title;
    }
}
