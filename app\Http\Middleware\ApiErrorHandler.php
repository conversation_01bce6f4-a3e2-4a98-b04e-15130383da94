<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use App\Exceptions\AIServiceException;
use App\Exceptions\ServiceUnavailableException;
use App\Exceptions\RateLimitException;
use App\Exceptions\InvalidQueryException;

class ApiErrorHandler
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $response = $next($request);
            
            // Add request ID to response headers
            if ($response instanceof JsonResponse) {
                $response->header('X-Request-ID', $this->getRequestId($request));
            }
            
            return $response;
            
        } catch (ServiceUnavailableException $e) {
            return $this->handleServiceUnavailableException($e, $request);
        } catch (RateLimitException $e) {
            return $this->handleRateLimitException($e, $request);
        } catch (InvalidQueryException $e) {
            return $this->handleInvalidQueryException($e, $request);
        } catch (AIServiceException $e) {
            return $this->handleAIServiceException($e, $request);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->handleValidationException($e, $request);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->handleModelNotFoundException($e, $request);
        } catch (\Illuminate\Auth\AuthenticationException $e) {
            return $this->handleAuthenticationException($e, $request);
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            return $this->handleAuthorizationException($e, $request);
        } catch (\Throwable $e) {
            return $this->handleGenericException($e, $request);
        }
    }

    /**
     * Handle ServiceUnavailableException
     */
    protected function handleServiceUnavailableException(ServiceUnavailableException $e, Request $request): JsonResponse
    {
        Log::warning('API: Service unavailable', [
            'request_id' => $this->getRequestId($request),
            'url' => $request->fullUrl(),
            'error' => $e->getMessage(),
        ]);

        return response()->json([
            'success' => false,
            'error' => [
                'code' => 'SERVICE_UNAVAILABLE',
                'message' => 'AI services are temporarily unavailable. Please try again later.',
                'details' => config('app.debug') ? $e->getMessage() : null,
                'retry_after' => 60, // Suggest retry after 60 seconds
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => $this->getRequestId($request),
        ], 503);
    }

    /**
     * Handle RateLimitException
     */
    protected function handleRateLimitException(RateLimitException $e, Request $request): JsonResponse
    {
        Log::warning('API: Rate limit exceeded', [
            'request_id' => $this->getRequestId($request),
            'url' => $request->fullUrl(),
            'error' => $e->getMessage(),
        ]);

        return response()->json([
            'success' => false,
            'error' => [
                'code' => 'RATE_LIMIT_EXCEEDED',
                'message' => 'Too many requests. Please slow down and try again later.',
                'details' => config('app.debug') ? $e->getMessage() : null,
                'retry_after' => 300, // Suggest retry after 5 minutes
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => $this->getRequestId($request),
        ], 429);
    }

    /**
     * Handle InvalidQueryException
     */
    protected function handleInvalidQueryException(InvalidQueryException $e, Request $request): JsonResponse
    {
        Log::info('API: Invalid query', [
            'request_id' => $this->getRequestId($request),
            'url' => $request->fullUrl(),
            'error' => $e->getMessage(),
        ]);

        return response()->json([
            'success' => false,
            'error' => [
                'code' => 'INVALID_QUERY',
                'message' => 'The query contains invalid or potentially harmful content.',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => $this->getRequestId($request),
        ], 400);
    }

    /**
     * Handle AIServiceException
     */
    protected function handleAIServiceException(AIServiceException $e, Request $request): JsonResponse
    {
        Log::error('API: AI service error', [
            'request_id' => $this->getRequestId($request),
            'url' => $request->fullUrl(),
            'error' => $e->getMessage(),
        ]);

        return response()->json([
            'success' => false,
            'error' => [
                'code' => 'AI_SERVICE_ERROR',
                'message' => 'There was an error processing your request with the AI service.',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => $this->getRequestId($request),
        ], 502);
    }

    /**
     * Handle ValidationException
     */
    protected function handleValidationException(\Illuminate\Validation\ValidationException $e, Request $request): JsonResponse
    {
        Log::info('API: Validation error', [
            'request_id' => $this->getRequestId($request),
            'url' => $request->fullUrl(),
            'errors' => $e->errors(),
        ]);

        return response()->json([
            'success' => false,
            'error' => [
                'code' => 'VALIDATION_ERROR',
                'message' => 'The request data is invalid.',
                'details' => $e->errors(),
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => $this->getRequestId($request),
        ], 422);
    }

    /**
     * Handle ModelNotFoundException
     */
    protected function handleModelNotFoundException(\Illuminate\Database\Eloquent\ModelNotFoundException $e, Request $request): JsonResponse
    {
        Log::info('API: Resource not found', [
            'request_id' => $this->getRequestId($request),
            'url' => $request->fullUrl(),
            'model' => $e->getModel(),
        ]);

        return response()->json([
            'success' => false,
            'error' => [
                'code' => 'RESOURCE_NOT_FOUND',
                'message' => 'The requested resource was not found.',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => $this->getRequestId($request),
        ], 404);
    }

    /**
     * Handle AuthenticationException
     */
    protected function handleAuthenticationException(\Illuminate\Auth\AuthenticationException $e, Request $request): JsonResponse
    {
        Log::info('API: Authentication required', [
            'request_id' => $this->getRequestId($request),
            'url' => $request->fullUrl(),
        ]);

        return response()->json([
            'success' => false,
            'error' => [
                'code' => 'AUTHENTICATION_REQUIRED',
                'message' => 'Authentication is required to access this resource.',
                'details' => null,
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => $this->getRequestId($request),
        ], 401);
    }

    /**
     * Handle AuthorizationException
     */
    protected function handleAuthorizationException(\Illuminate\Auth\Access\AuthorizationException $e, Request $request): JsonResponse
    {
        Log::info('API: Access forbidden', [
            'request_id' => $this->getRequestId($request),
            'url' => $request->fullUrl(),
        ]);

        return response()->json([
            'success' => false,
            'error' => [
                'code' => 'ACCESS_FORBIDDEN',
                'message' => 'You do not have permission to access this resource.',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => $this->getRequestId($request),
        ], 403);
    }

    /**
     * Handle generic exceptions
     */
    protected function handleGenericException(\Throwable $e, Request $request): JsonResponse
    {
        Log::error('API: Unexpected error', [
            'request_id' => $this->getRequestId($request),
            'url' => $request->fullUrl(),
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);

        return response()->json([
            'success' => false,
            'error' => [
                'code' => 'INTERNAL_ERROR',
                'message' => 'An unexpected error occurred. Please try again later.',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => $this->getRequestId($request),
        ], 500);
    }

    /**
     * Get or generate request ID
     */
    protected function getRequestId(Request $request): string
    {
        return $request->header('X-Request-ID', uniqid('req_', true));
    }
}
