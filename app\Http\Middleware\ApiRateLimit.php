<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use App\Exceptions\RateLimitException;

class ApiRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $maxAttempts = '60', string $decayMinutes = '1'): Response
    {
        if (!config('ai.rate_limits.enabled', true)) {
            return $next($request);
        }

        $maxAttempts = (int) ($maxAttempts ?: config('ai.rate_limits.max_requests', 60));
        $decayMinutes = (int) ($decayMinutes ?: config('ai.rate_limits.period_minutes', 1));

        $key = $this->resolveRequestSignature($request);
        $attempts = Cache::get($key, 0);

        if ($attempts >= $maxAttempts) {
            Log::warning('API: Rate limit exceeded', [
                'key' => $key,
                'attempts' => $attempts,
                'max_attempts' => $maxAttempts,
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
            ]);

            throw new RateLimitException(
                "Too many requests. Maximum {$maxAttempts} requests per {$decayMinutes} minute(s) allowed."
            );
        }

        // Increment the attempt count
        Cache::put($key, $attempts + 1, now()->addMinutes($decayMinutes));

        $response = $next($request);

        // Add rate limit headers to response
        if ($response instanceof \Illuminate\Http\JsonResponse) {
            $response->headers->set('X-RateLimit-Limit', $maxAttempts);
            $response->headers->set('X-RateLimit-Remaining', max(0, $maxAttempts - $attempts - 1));
            $response->headers->set('X-RateLimit-Reset', now()->addMinutes($decayMinutes)->timestamp);
        }

        return $response;
    }

    /**
     * Resolve the request signature for rate limiting
     */
    protected function resolveRequestSignature(Request $request): string
    {
        // Use IP address as the primary identifier
        $identifier = $request->ip();

        // If user is authenticated, use user ID instead
        if ($request->user()) {
            $identifier = 'user:' . $request->user()->id;
        }

        // Include the route name for different limits per endpoint
        $route = $request->route()?->getName() ?? 'unknown';

        return 'rate_limit:' . $identifier . ':' . $route;
    }
}
