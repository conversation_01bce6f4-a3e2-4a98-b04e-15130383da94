<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ApiRequestLogger
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        $requestId = $request->header('X-Request-ID', uniqid('req_', true));

        // Log incoming request
        Log::info('API: Incoming request', [
            'request_id' => $requestId,
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'content_type' => $request->header('Content-Type'),
            'content_length' => $request->header('Content-Length'),
            'timestamp' => now()->toISOString(),
        ]);

        // Log request body for POST/PUT/PATCH requests (excluding sensitive data)
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH']) && $request->isJson()) {
            $body = $request->all();
            
            // Remove sensitive fields
            $sensitiveFields = ['password', 'token', 'api_key', 'secret'];
            foreach ($sensitiveFields as $field) {
                if (isset($body[$field])) {
                    $body[$field] = '[REDACTED]';
                }
            }

            // Truncate long message content for logging
            if (isset($body['message']) && strlen($body['message']) > 200) {
                $body['message'] = substr($body['message'], 0, 200) . '... [TRUNCATED]';
            }

            Log::debug('API: Request body', [
                'request_id' => $requestId,
                'body' => $body,
            ]);
        }

        $response = $next($request);

        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2); // Duration in milliseconds

        // Log response
        Log::info('API: Response sent', [
            'request_id' => $requestId,
            'status_code' => $response->getStatusCode(),
            'duration_ms' => $duration,
            'content_type' => $response->headers->get('Content-Type'),
            'content_length' => $response->headers->get('Content-Length'),
            'timestamp' => now()->toISOString(),
        ]);

        // Log response body for errors or debug mode
        if ($response->getStatusCode() >= 400 || config('app.debug')) {
            $responseContent = $response->getContent();
            
            // Only log JSON responses and limit size
            if ($response->headers->get('Content-Type') === 'application/json' && 
                strlen($responseContent) < 5000) {
                
                Log::debug('API: Response body', [
                    'request_id' => $requestId,
                    'status_code' => $response->getStatusCode(),
                    'body' => json_decode($responseContent, true),
                ]);
            }
        }

        // Add request ID to response headers
        if (method_exists($response, 'header')) {
            $response->header('X-Request-ID', $requestId);
        }

        return $response;
    }
}
