<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

class AskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // For now, allow all requests
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'message' => [
                'required',
                'string',
                'min:1',
                'max:10000',
                function ($attribute, $value, $fail) {
                    // Check for potentially harmful content
                    if ($this->containsHarmfulContent($value)) {
                        $fail('The message contains potentially harmful content.');
                    }
                },
            ],
            'conversation_id' => [
                'nullable',
                'integer',
                'exists:conversations,id',
            ],
            'context' => [
                'nullable',
                'array',
                'max:10', // Limit context items
            ],
            'context.*' => [
                'string',
                'max:1000',
            ],
            'preferred_service' => [
                'nullable',
                'string',
                'in:deepseek,gemini,huggingface',
            ],
            'use_knowledge_base' => [
                'nullable',
                'boolean',
            ],
            'knowledge_threshold' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1',
            ],
            'max_knowledge_results' => [
                'nullable',
                'integer',
                'min:1',
                'max:20',
            ],
            'tags' => [
                'nullable',
                'array',
                'max:10',
            ],
            'tags.*' => [
                'string',
                'max:50',
                'regex:/^[a-zA-Z0-9_-]+$/', // Only alphanumeric, underscore, and dash
            ],
            'source_filter' => [
                'nullable',
                'string',
                'max:100',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'message.required' => 'A message is required to process your request.',
            'message.min' => 'The message must contain at least one character.',
            'message.max' => 'The message is too long. Please keep it under 10,000 characters.',
            'conversation_id.exists' => 'The specified conversation does not exist.',
            'context.max' => 'Too many context items provided. Maximum is 10.',
            'context.*.max' => 'Each context item must be under 1,000 characters.',
            'preferred_service.in' => 'Invalid AI service specified. Must be one of: deepseek, gemini, huggingface.',
            'knowledge_threshold.min' => 'Knowledge threshold must be between 0 and 1.',
            'knowledge_threshold.max' => 'Knowledge threshold must be between 0 and 1.',
            'max_knowledge_results.min' => 'Maximum knowledge results must be at least 1.',
            'max_knowledge_results.max' => 'Maximum knowledge results cannot exceed 20.',
            'tags.max' => 'Too many tags provided. Maximum is 10.',
            'tags.*.max' => 'Each tag must be under 50 characters.',
            'tags.*.regex' => 'Tags can only contain letters, numbers, underscores, and dashes.',
            'source_filter.max' => 'Source filter must be under 100 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'message' => 'message',
            'conversation_id' => 'conversation ID',
            'context' => 'context',
            'preferred_service' => 'preferred AI service',
            'use_knowledge_base' => 'knowledge base usage',
            'knowledge_threshold' => 'knowledge similarity threshold',
            'max_knowledge_results' => 'maximum knowledge results',
            'tags' => 'tags',
            'source_filter' => 'source filter',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param Validator $validator
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'error' => [
                    'code' => 'VALIDATION_ERROR',
                    'message' => 'The request data is invalid.',
                    'details' => $validator->errors()->toArray(),
                ],
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY)
        );
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Sanitize the message
        if ($this->has('message')) {
            $this->merge([
                'message' => $this->sanitizeMessage($this->input('message')),
            ]);
        }

        // Set default values
        $this->merge([
            'use_knowledge_base' => $this->input('use_knowledge_base', true),
            'knowledge_threshold' => $this->input('knowledge_threshold', config('ai.knowledge_base.similarity_threshold', 0.7)),
            'max_knowledge_results' => $this->input('max_knowledge_results', 5),
        ]);
    }

    /**
     * Get the validated data with additional processing.
     *
     * @param string|null $key
     * @param mixed $default
     * @return mixed
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);

        if ($key === null) {
            // Process context array if present
            if (isset($validated['context']) && is_array($validated['context'])) {
                $validated['context'] = array_map([$this, 'sanitizeString'], $validated['context']);
            }

            // Process tags array if present
            if (isset($validated['tags']) && is_array($validated['tags'])) {
                $validated['tags'] = array_map('strtolower', $validated['tags']);
                $validated['tags'] = array_unique($validated['tags']);
                $validated['tags'] = array_values($validated['tags']); // Re-index
            }

            // Sanitize source filter
            if (isset($validated['source_filter'])) {
                $validated['source_filter'] = $this->sanitizeString($validated['source_filter']);
            }
        }

        return $validated;
    }

    /**
     * Sanitize message content.
     *
     * @param string $message
     * @return string
     */
    protected function sanitizeMessage(string $message): string
    {
        // Remove null bytes
        $message = str_replace("\0", '', $message);

        // Normalize line endings
        $message = str_replace(["\r\n", "\r"], "\n", $message);

        // Trim whitespace
        $message = trim($message);

        // Remove excessive whitespace
        $message = preg_replace('/\s+/', ' ', $message);

        // Remove control characters except newlines and tabs
        $message = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $message);

        return $message;
    }

    /**
     * Sanitize general string input.
     *
     * @param string $input
     * @return string
     */
    protected function sanitizeString(string $input): string
    {
        // Remove null bytes
        $input = str_replace("\0", '', $input);

        // Trim whitespace
        $input = trim($input);

        // Remove control characters
        $input = preg_replace('/[\x00-\x1F\x7F]/', '', $input);

        return $input;
    }

    /**
     * Check if content contains potentially harmful patterns.
     *
     * @param string $content
     * @return bool
     */
    protected function containsHarmfulContent(string $content): bool
    {
        // List of potentially harmful patterns
        $harmfulPatterns = [
            // SQL injection patterns
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b).*(\bFROM\b|\bINTO\b|\bWHERE\b)/i',

            // Script injection patterns
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/on\w+\s*=/i',

            // Command injection patterns
            '/(\||;|&|`|\$\(|\${)/i',

            // Path traversal patterns
            '/\.\.[\/\\\\]/i',

            // Excessive repetition (potential DoS)
            '/(.)\1{100,}/',
        ];

        foreach ($harmfulPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the sanitized message content.
     *
     * @return string
     */
    public function getMessage(): string
    {
        return $this->validated('message');
    }

    /**
     * Get the conversation ID if provided.
     *
     * @return int|null
     */
    public function getConversationId(): ?int
    {
        return $this->validated('conversation_id');
    }

    /**
     * Get the context array.
     *
     * @return array
     */
    public function getContext(): array
    {
        return $this->validated('context', []);
    }

    /**
     * Get the preferred AI service.
     *
     * @return string|null
     */
    public function getPreferredService(): ?string
    {
        return $this->validated('preferred_service');
    }

    /**
     * Check if knowledge base should be used.
     *
     * @return bool
     */
    public function shouldUseKnowledgeBase(): bool
    {
        return $this->validated('use_knowledge_base', true);
    }

    /**
     * Get the knowledge similarity threshold.
     *
     * @return float
     */
    public function getKnowledgeThreshold(): float
    {
        return $this->validated('knowledge_threshold', 0.7);
    }

    /**
     * Get the maximum number of knowledge results.
     *
     * @return int
     */
    public function getMaxKnowledgeResults(): int
    {
        return $this->validated('max_knowledge_results', 5);
    }

    /**
     * Get the tags for filtering.
     *
     * @return array
     */
    public function getTags(): array
    {
        return $this->validated('tags', []);
    }

    /**
     * Get the source filter.
     *
     * @return string|null
     */
    public function getSourceFilter(): ?string
    {
        return $this->validated('source_filter');
    }
}
