<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AssistantResponseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'success' => true,
            'data' => [
                'message' => $this->when(isset($this->resource['message']), function () {
                    return new MessageResource($this->resource['message']);
                }),
                'conversation' => $this->when(isset($this->resource['conversation']), function () {
                    return new ConversationResource($this->resource['conversation']);
                }),
                'response' => [
                    'content' => $this->resource['response']['content'] ?? '',
                    'model_used' => $this->resource['response']['model_used'] ?? null,
                    'service_used' => $this->resource['response']['service_used'] ?? null,
                    'fallback_used' => $this->resource['response']['fallback_used'] ?? false,
                    'processing_time' => $this->resource['response']['processing_time'] ?? null,
                ],
                'knowledge_base' => $this->when(isset($this->resource['knowledge_base']), [
                    'used' => $this->resource['knowledge_base']['used'] ?? false,
                    'entries_found' => $this->resource['knowledge_base']['entries_found'] ?? 0,
                    'entries' => $this->when(
                        isset($this->resource['knowledge_base']['entries']) && 
                        $request->query('include_knowledge_entries') === 'true',
                        function () {
                            return KnowledgeEntryResource::collection($this->resource['knowledge_base']['entries']);
                        }
                    ),
                    'threshold_used' => $this->resource['knowledge_base']['threshold_used'] ?? null,
                ]),
                'metadata' => $this->when(isset($this->resource['metadata']), $this->resource['metadata']),
            ],
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get additional data that should be merged with the resource array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'version' => '1.0',
                'api_endpoint' => $request->url(),
                'request_id' => $request->header('X-Request-ID', uniqid()),
            ],
        ];
    }
}
