<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ConversationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Include messages if loaded
            'messages' => $this->whenLoaded('messages', function () {
                return MessageResource::collection($this->messages);
            }),
            
            // Include message count
            'messages_count' => $this->whenCounted('messages'),
            
            // Include latest message if loaded
            'latest_message' => $this->whenLoaded('latestMessage', function () {
                return new MessageResource($this->latestMessage);
            }),
        ];
    }
}
