<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KnowledgeEntryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'content' => $this->content,
            'source' => $this->source,
            'tags' => $this->tags ?? [],
            'confidence_score' => $this->confidence_score,
            'similarity_score' => $this->when(isset($this->similarity_score), $this->similarity_score),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Optionally include embedding vector (usually not needed in API responses)
            'embedding_vector' => $this->when(
                $request->query('include_embedding') === 'true',
                $this->embedding_vector
            ),
        ];
    }
}
