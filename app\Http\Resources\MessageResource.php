<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'conversation_id' => $this->conversation_id,
            'role' => $this->role,
            'content' => $this->content,
            'model_used' => $this->model_used,
            'metadata' => $this->when($this->metadata, $this->metadata),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Include conversation data if loaded
            'conversation' => $this->whenLoaded('conversation', function () {
                return [
                    'id' => $this->conversation->id,
                    'title' => $this->conversation->title,
                    'created_at' => $this->conversation->created_at?->toISOString(),
                ];
            }),
        ];
    }
}
