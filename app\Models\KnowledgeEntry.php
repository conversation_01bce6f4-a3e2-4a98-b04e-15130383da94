<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class KnowledgeEntry extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'content',
        'embedding_vector',
        'source',
        'tags',
        'confidence_score',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'embedding_vector' => 'array',
        'tags' => 'array',
        'confidence_score' => 'float',
    ];

    /**
     * Scope a query to filter by source.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $source
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFromSource(Builder $query, string $source): Builder
    {
        return $query->where('source', $source);
    }

    /**
     * Scope a query to filter by tag.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $tag
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithTag(Builder $query, string $tag): Builder
    {
        return $query->whereJsonContains('tags', $tag);
    }

    /**
     * Find similar knowledge entries based on embedding vector.
     *
     * @param array $embeddingVector
     * @param float $threshold
     * @param int $limit
     * @return \Illuminate\Support\Collection
     */
    public static function findSimilar(array $embeddingVector, float $threshold = 0.7, int $limit = 5): Collection
    {
        // Get all knowledge entries
        $entries = self::all();
        
        // Calculate cosine similarity for each entry
        $entriesWithScores = $entries->map(function ($entry) use ($embeddingVector) {
            $similarity = self::cosineSimilarity($entry->embedding_vector, $embeddingVector);
            return [
                'entry' => $entry,
                'similarity' => $similarity,
            ];
        });
        
        // Filter by threshold and sort by similarity (highest first)
        return $entriesWithScores
            ->filter(function ($item) use ($threshold) {
                return $item['similarity'] >= $threshold;
            })
            ->sortByDesc('similarity')
            ->take($limit)
            ->map(function ($item) {
                $entry = $item['entry'];
                $entry->similarity_score = $item['similarity'];
                return $entry;
            });
    }

    /**
     * Calculate cosine similarity between two embedding vectors.
     *
     * @param array $vectorA
     * @param array $vectorB
     * @return float
     */
    protected static function cosineSimilarity(array $vectorA, array $vectorB): float
    {
        if (count($vectorA) !== count($vectorB)) {
            throw new \InvalidArgumentException('Vectors must have the same dimensions');
        }

        $dotProduct = 0;
        $magnitudeA = 0;
        $magnitudeB = 0;

        foreach ($vectorA as $i => $valueA) {
            $valueB = $vectorB[$i];
            $dotProduct += $valueA * $valueB;
            $magnitudeA += $valueA * $valueA;
            $magnitudeB += $valueB * $valueB;
        }

        $magnitudeA = sqrt($magnitudeA);
        $magnitudeB = sqrt($magnitudeB);

        if ($magnitudeA == 0 || $magnitudeB == 0) {
            return 0;
        }

        return $dotProduct / ($magnitudeA * $magnitudeB);
    }

    /**
     * Add tags to the knowledge entry.
     *
     * @param array|string $newTags
     * @return $this
     */
    public function addTags($newTags): self
    {
        $tags = $this->tags ?? [];
        
        if (is_string($newTags)) {
            $newTags = [$newTags];
        }
        
        $this->tags = array_values(array_unique(array_merge($tags, $newTags)));
        $this->save();
        
        return $this;
    }

    /**
     * Remove tags from the knowledge entry.
     *
     * @param array|string $tagsToRemove
     * @return $this
     */
    public function removeTags($tagsToRemove): self
    {
        if (!$this->tags) {
            return $this;
        }
        
        if (is_string($tagsToRemove)) {
            $tagsToRemove = [$tagsToRemove];
        }
        
        $this->tags = array_values(array_diff($this->tags, $tagsToRemove));
        $this->save();
        
        return $this;
    }
}