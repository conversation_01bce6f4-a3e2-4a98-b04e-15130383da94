<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\AI\Contracts\AIServiceInterface;

class AIServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register AI services first
        $this->registerAIServices();

        // Register the decision engine as a singleton with services injected
        $this->app->singleton(\App\Services\DecisionEngine::class, function ($app) {
            $services = [];

            // Collect enabled AI services
            if (config('ai.services.deepseek.enabled', false)) {
                $services['deepseek'] = $app->make('ai.service.deepseek');
            }
            if (config('ai.services.gemini.enabled', false)) {
                $services['gemini'] = $app->make('ai.service.gemini');
            }
            if (config('ai.services.huggingface.enabled', false)) {
                $services['huggingface'] = $app->make('ai.service.huggingface');
            }

            $decisionEngine = new \App\Services\DecisionEngine($services);
            return $decisionEngine;
        });

        // Register KnowledgeBaseService as singleton
        $this->app->singleton(\App\Services\KnowledgeBaseService::class, function ($app) {
            return new \App\Services\KnowledgeBaseService();
        });

        // Register CacheService as singleton
        $this->app->singleton(\App\Services\CacheService::class, function ($app) {
            return new \App\Services\CacheService();
        });

        // Register MonitoringService as singleton
        $this->app->singleton(\App\Services\MonitoringService::class, function ($app) {
            return new \App\Services\MonitoringService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (method_exists($this->app, 'configPath')) {
            // Publish configuration file
            $this->publishes([
                __DIR__.'/../../config/ai.php' => config_path('ai.php'),
            ], 'config');
        }
    }

    /**
     * Register AI services based on configuration
     */
    protected function registerAIServices(): void
    {
        $config = $this->app->make('config');

        // Register DeepSeek service if enabled
        if ($config->get('ai.services.deepseek.enabled', false)) {
            $this->app->bind('ai.service.deepseek', function ($app) use ($config) {
                return new \App\Services\AI\DeepSeekService(
                    $config->get('ai.services.deepseek.api_key'),
                    $config->get('ai.services.deepseek.api_url'),
                    $config->get('ai.services.deepseek.model'),
                    $config->get('ai.services.deepseek.options', [])
                );
            });
        }

        // Register Gemini service if enabled
        if ($config->get('ai.services.gemini.enabled', false)) {
            $this->app->bind('ai.service.gemini', function ($app) use ($config) {
                return new \App\Services\AI\GeminiService(
                    $config->get('ai.services.gemini.api_key'),
                    $config->get('ai.services.gemini.api_url'),
                    $config->get('ai.services.gemini.model'),
                    $config->get('ai.services.gemini.options', [])
                );
            });
        }

        // Register HuggingFace service if enabled
        if ($config->get('ai.services.huggingface.enabled', false)) {
            $this->app->bind('ai.service.huggingface', function ($app) use ($config) {
                return new \App\Services\AI\HuggingFaceService(
                    $config->get('ai.services.huggingface.api_key'),
                    $config->get('ai.services.huggingface.api_url'),
                    $config->get('ai.services.huggingface.model'),
                    $config->get('ai.services.huggingface.options', [])
                );
            });
        }

        // Register the default AI service based on configuration
        $this->app->bind(AIServiceInterface::class, function ($app) use ($config) {
            $defaultService = $config->get('ai.default_service', 'deepseek');
            return $app->make("ai.service.$defaultService");
        });
    }
}
