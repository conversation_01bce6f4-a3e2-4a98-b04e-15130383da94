<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class AIService
{
    protected $services = [
        'deepseek' => DeepSeekService::class,
        'gemini' => GeminiService::class,
        'huggingface' => HuggingFaceService::class
    ];

    /**
     * Generate AI response
     */
    public function generateResponse(array $params): array
    {
        $startTime = microtime(true);

        try {
            // Determine which service to use
            $service = $this->selectService($params);

            // Generate response
            $response = $this->callService($service, $params);

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            return [
                'content' => $response['content'] ?? 'Sorry, I could not generate a response.',
                'model' => $response['model'] ?? $service,
                'service' => $service,
                'tokens_used' => $response['tokens_used'] ?? 0,
                'processing_time' => $processingTime
            ];

        } catch (\Exception $e) {
            Log::error('AI Service error: ' . $e->getMessage());

            return [
                'content' => 'I apologize, but I encountered an error while processing your request. Please try again.',
                'model' => 'fallback',
                'service' => 'fallback',
                'tokens_used' => 0,
                'processing_time' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }

    /**
     * Select appropriate AI service
     */
    protected function selectService(array $params): string
    {
        // If preferred service is specified and available (but not 'auto')
        if (!empty($params['preferred_service']) &&
            $params['preferred_service'] !== 'auto' &&
            isset($this->services[$params['preferred_service']])) {
            return $params['preferred_service'];
        }

        // Auto-select based on message content
        $message = strtolower($params['message']);

        // Code-related queries -> DeepSeek
        if (preg_match('/\b(code|programming|function|class|variable|algorithm|debug|syntax|php|javascript|python|java|css|html|sql)\b/', $message)) {
            return 'deepseek';
        }

        // Creative or general queries -> Gemini
        if (preg_match('/\b(creative|story|poem|explain|help|what|how|why|write|create|generate)\b/', $message)) {
            return 'gemini';
        }

        // Technical or analysis queries -> HuggingFace
        if (preg_match('/\b(analyze|analysis|research|data|science|machine learning|ai|model)\b/', $message)) {
            return 'huggingface';
        }

        // Default to DeepSeek
        return 'deepseek';
    }

    /**
     * Call specific AI service
     */
    protected function callService(string $serviceName, array $params): array
    {
        // For now, return a mock response since we don't have API keys configured
        return $this->getMockResponse($serviceName, $params['message']);
    }

    /**
     * Get mock response for testing
     */
    protected function getMockResponse(string $service, string $message): array
    {
        // Generate more realistic responses based on message content
        $messageWords = str_word_count(strtolower($message));
        $isQuestion = str_contains($message, '?');
        $isGreeting = preg_match('/\b(hello|hi|hey|greetings)\b/i', $message);

        if ($isGreeting) {
            $responses = [
                'deepseek' => [
                    'content' => "Hello! I'm WIDDX AI powered by **DeepSeek**. Nice to meet you! 👋\n\nI'm particularly good at:\n- **Code generation** and debugging\n- **Technical problem solving**\n- **Mathematical reasoning**\n- **System architecture** discussions\n\nWhat technical challenge can I help you with today?",
                    'model' => 'deepseek-chat',
                    'tokens_used' => rand(80, 120)
                ],
                'gemini' => [
                    'content' => "Hi there! I'm WIDDX AI using **Google's Gemini**. Great to connect with you! ✨\n\nI excel at:\n- **Creative writing** and brainstorming\n- **Research** and information synthesis\n- **Explanations** in simple terms\n- **General conversation** and advice\n\nWhat would you like to explore or discuss?",
                    'model' => 'gemini-pro',
                    'tokens_used' => rand(70, 110)
                ],
                'huggingface' => [
                    'content' => "Greetings! I'm WIDDX AI utilizing **HuggingFace** models. Welcome! 🤗\n\nI'm powered by the open-source community and specialize in:\n- **Text analysis** and processing\n- **Multi-language** support\n- **Sentiment analysis**\n- **Content generation**\n\nHow can I assist you with your text-related needs?",
                    'model' => 'huggingface-inference',
                    'tokens_used' => rand(60, 100)
                ]
            ];
        } else {
            // Generate contextual responses
            $baseResponses = [
                'deepseek' => [
                    'content' => $this->generateDeepSeekResponse($message),
                    'model' => 'deepseek-chat',
                    'tokens_used' => rand(100, 200)
                ],
                'gemini' => [
                    'content' => $this->generateGeminiResponse($message),
                    'model' => 'gemini-pro',
                    'tokens_used' => rand(90, 180)
                ],
                'huggingface' => [
                    'content' => $this->generateHuggingFaceResponse($message),
                    'model' => 'huggingface-inference',
                    'tokens_used' => rand(80, 160)
                ]
            ];
            $responses = $baseResponses;
        }

        return $responses[$service] ?? $responses['deepseek'];
    }

    private function generateDeepSeekResponse(string $message): string
    {
        if (preg_match('/\b(code|programming|function|debug)\b/i', $message)) {
            return "I'd be happy to help with your coding question! 💻\n\n**DeepSeek Analysis:**\nBased on your query: \"*{$message}*\"\n\nI can assist with:\n- Writing clean, efficient code\n- Debugging and troubleshooting\n- Code optimization\n- Best practices and patterns\n\nCould you provide more specific details about what you're working on?";
        }

        return "Thank you for your question! 🧠\n\n**DeepSeek Response:**\nRegarding: \"*{$message}*\"\n\nI'm analyzing your request and can provide detailed technical insights. My reasoning capabilities allow me to break down complex problems systematically.\n\nWould you like me to elaborate on any specific aspect?";
    }

    private function generateGeminiResponse(string $message): string
    {
        if (preg_match('/\b(creative|story|write|generate)\b/i', $message)) {
            return "What an interesting creative request! ✨\n\n**Gemini's Take:**\nYou asked: \"*{$message}*\"\n\nI love helping with creative projects! I can help you:\n- Brainstorm ideas\n- Write engaging content\n- Develop storylines\n- Polish your writing\n\nShall we dive into the creative process together?";
        }

        return "Great question! 💎\n\n**Gemini Analysis:**\nFor your inquiry: \"*{$message}*\"\n\nI can provide comprehensive insights drawing from my broad knowledge base. I'm designed to give helpful, accurate, and nuanced responses.\n\nWhat specific information would be most valuable to you?";
    }

    private function generateHuggingFaceResponse(string $message): string
    {
        if (preg_match('/\b(analyze|analysis|data|research)\b/i', $message)) {
            return "Excellent analytical question! 📊\n\n**HuggingFace Processing:**\nAnalyzing: \"*{$message}*\"\n\nUsing open-source models, I can help with:\n- Text analysis and insights\n- Data interpretation\n- Research synthesis\n- Pattern recognition\n\nWhat type of analysis would be most helpful?";
        }

        return "Thank you for reaching out! 🤗\n\n**HuggingFace Response:**\nProcessing your message: \"*{$message}*\"\n\nPowered by community-driven AI, I can provide diverse perspectives and solutions. My open-source foundation ensures transparency and reliability.\n\nHow can I best assist you today?";
    }
}

// Placeholder service classes
class DeepSeekService
{
    // Implementation would go here
}

class GeminiService
{
    // Implementation would go here
}

class HuggingFaceService
{
    // Implementation would go here
}
