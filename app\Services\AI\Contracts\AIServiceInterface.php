<?php

namespace App\Services\AI\Contracts;

interface AIServiceInterface
{
    /**
     * Send a message to the AI service and get a response
     *
     * @param string $message The user message to send
     * @param array $context Additional context for the AI (e.g. conversation history)
     * @return array The AI response with at least 'content' key
     * @throws \App\Exceptions\AIServiceException If the service call fails
     */
    public function sendMessage(string $message, array $context = []): array;

    /**
     * Check if the AI service is currently available
     *
     * @return bool True if the service is available, false otherwise
     */
    public function isAvailable(): bool;

    /**
     * Get the name of the AI model being used
     *
     * @return string The model name
     */
    public function getModelName(): string;
}