<?php

namespace App\Services\AI;

use App\Exceptions\AIServiceException;
use App\Exceptions\RateLimitException;
use App\Exceptions\ServiceUnavailableException;
use App\Services\AI\Contracts\AIServiceInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Cache;

class DeepSeekService implements AIServiceInterface
{
    /**
     * @var string The API key for DeepSeek
     */
    protected string $apiKey;

    /**
     * @var string The API URL for DeepSeek
     */
    protected string $apiUrl;

    /**
     * @var string The model name to use
     */
    protected string $modelName;

    /**
     * @var array Additional options for the API
     */
    protected array $options;

    /**
     * @var int Maximum number of retry attempts
     */
    protected int $maxRetries = 3;

    /**
     * @var string Cache key for rate limit tracking
     */
    protected string $rateLimitCacheKey = 'deepseek_rate_limit';

    /**
     * Create a new DeepSeek service instance
     *
     * @param string $apiKey The API key for DeepSeek
     * @param string $apiUrl The API URL for DeepSeek
     * @param string $modelName The model name to use
     * @param array $options Additional options for the API
     */
    public function __construct(
        string $apiKey,
        string $apiUrl,
        string $modelName,
        array $options = []
    ) {
        $this->apiKey = $apiKey;
        $this->apiUrl = $apiUrl;
        $this->modelName = $modelName;
        $this->options = $options;
    }

    /**
     * Send a message to the DeepSeek API and get a response
     *
     * @param string $message The user message to send
     * @param array $context Additional context for the AI (e.g. conversation history)
     * @return array The AI response with at least 'content' key
     * @throws AIServiceException If the service call fails
     */
    public function sendMessage(string $message, array $context = []): array
    {
        if (!$this->isAvailable()) {
            throw new ServiceUnavailableException(
                'DeepSeek API is not available: API key is missing',
                'deepseek',
                ['error_type' => 'configuration']
            );
        }

        // Check rate limiting
        if ($this->isRateLimited()) {
            throw new RateLimitException(
                'Rate limit exceeded for DeepSeek API',
                'deepseek',
                ['error_type' => 'rate_limit']
            );
        }

        // Format the messages for the API
        $messages = $this->formatMessages($message, $context);

        // Prepare the request payload
        $payload = [
            'model' => $this->modelName,
            'messages' => $messages,
            'temperature' => $this->options['temperature'] ?? 0.7,
            'max_tokens' => $this->options['max_tokens'] ?? 1000,
        ];

        // Add any additional options from config
        foreach ($this->options as $key => $value) {
            if (!in_array($key, ['temperature', 'max_tokens'])) {
                $payload[$key] = $value;
            }
        }

        return $this->makeRequest($payload);
    }

    /**
     * Make the API request with retry logic
     *
     * @param array $payload The request payload
     * @return array The parsed response
     * @throws AIServiceException If all retry attempts fail
     */
    protected function makeRequest(array $payload): array
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $this->maxRetries) {
            try {
                // Increment the attempt counter
                $attempt++;

                // Make the API request
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ])
                ->timeout($this->options['timeout'] ?? 30)
                ->post($this->apiUrl, $payload);

                // Track rate limit usage
                $this->trackRateLimitUsage();

                // Check for errors
                if ($response->failed()) {
                    $errorData = $response->json() ?? [];
                    $errorMessage = $errorData['error']['message'] ?? 'Unknown error';
                    $statusCode = $response->status();

                    // Handle specific error types
                    if ($statusCode === 429) {
                        throw new RateLimitException(
                            'DeepSeek API rate limit exceeded: ' . $errorMessage,
                            'deepseek',
                            ['status_code' => $statusCode, 'error_data' => $errorData]
                        );
                    }
                    
                    if ($statusCode >= 500 && $statusCode < 600) {
                        throw new ServiceUnavailableException(
                            'DeepSeek API server error: ' . $errorMessage,
                            'deepseek',
                            ['status_code' => $statusCode, 'error_data' => $errorData]
                        );
                    }

                    throw new AIServiceException(
                        'DeepSeek API error: ' . $errorMessage,
                        'deepseek',
                        ['status_code' => $statusCode, 'error_data' => $errorData]
                    );
                }

                // Parse and return the response
                return $this->parseResponse($response->json());

            } catch (RequestException $e) {
                $lastException = new AIServiceException(
                    'DeepSeek API request failed: ' . $e->getMessage(),
                    'deepseek',
                    ['attempt' => $attempt, 'max_retries' => $this->maxRetries],
                    $e->getCode(),
                    $e
                );

                // Log the error
                Log::warning('DeepSeek API request failed (attempt ' . $attempt . '/' . $this->maxRetries . '): ' . $e->getMessage());

                // Only retry on connection errors or server errors (5xx)
                if (!$this->shouldRetry($e)) {
                    break;
                }

                // Wait before retrying (exponential backoff)
                $backoffSeconds = min(2 ** $attempt, 10);
                sleep($backoffSeconds);
            } catch (\Exception $e) {
                $lastException = new AIServiceException(
                    'DeepSeek API error: ' . $e->getMessage(),
                    'deepseek',
                    ['attempt' => $attempt],
                    $e->getCode(),
                    $e
                );

                // Log the error
                Log::error('DeepSeek API error: ' . $e->getMessage());
                break;
            }
        }

        // If we've exhausted all retries, throw the last exception
        throw $lastException ?? new AIServiceException(
            'DeepSeek API request failed after ' . $this->maxRetries . ' attempts',
            'deepseek',
            ['max_retries' => $this->maxRetries]
        );
    }

    /**
     * Determine if we should retry the request based on the exception
     *
     * @param RequestException $e The exception that occurred
     * @return bool True if we should retry, false otherwise
     */
    protected function shouldRetry(RequestException $e): bool
    {
        $statusCode = $e->response?->status() ?? 0;
        
        // Retry on connection errors or server errors (5xx)
        return $statusCode === 0 || ($statusCode >= 500 && $statusCode < 600);
    }

    /**
     * Parse the API response into a standardized format
     *
     * @param array $responseData The raw API response data
     * @return array The standardized response
     */
    protected function parseResponse(array $responseData): array
    {
        // Extract the content from the response
        $content = $responseData['choices'][0]['message']['content'] ?? null;
        
        if ($content === null) {
            throw new AIServiceException(
                'Invalid response format from DeepSeek API',
                'deepseek',
                ['response_data' => $responseData]
            );
        }

        // Return the standardized response format
        return [
            'content' => $content,
            'model' => $responseData['model'] ?? $this->modelName,
            'usage' => $responseData['usage'] ?? [],
            'finish_reason' => $responseData['choices'][0]['finish_reason'] ?? null,
        ];
    }

    /**
     * Format the messages for the DeepSeek API
     *
     * @param string $message The current user message
     * @param array $context Additional context (e.g. conversation history)
     * @return array The formatted messages
     */
    protected function formatMessages(string $message, array $context = []): array
    {
        $messages = [];

        // Add conversation history if provided
        if (isset($context['history']) && is_array($context['history'])) {
            foreach ($context['history'] as $historyMessage) {
                if (isset($historyMessage['role'], $historyMessage['content'])) {
                    $messages[] = [
                        'role' => $historyMessage['role'],
                        'content' => $historyMessage['content']
                    ];
                }
            }
        }

        // Add the current user message
        $messages[] = [
            'role' => 'user',
            'content' => $message
        ];

        return $messages;
    }

    /**
     * Check if the service is currently available
     *
     * @return bool True if the service is available, false otherwise
     */
    public function isAvailable(): bool
    {
        return !empty($this->apiKey);
    }

    /**
     * Get the name of the AI model being used
     *
     * @return string The model name
     */
    public function getModelName(): string
    {
        return $this->modelName;
    }

    /**
     * Check if the service is currently rate limited
     *
     * @return bool True if rate limited, false otherwise
     */
    protected function isRateLimited(): bool
    {
        if (!config('ai.rate_limits.enabled', true)) {
            return false;
        }

        $maxRequests = config('ai.rate_limits.max_requests', 60);
        $periodMinutes = config('ai.rate_limits.period_minutes', 60);
        
        $usageData = Cache::get($this->rateLimitCacheKey, ['count' => 0, 'reset_at' => now()]);
        
        // If the reset time has passed, reset the counter
        if (now()->gt($usageData['reset_at'])) {
            return false;
        }
        
        // Check if we've exceeded the limit
        return $usageData['count'] >= $maxRequests;
    }

    /**
     * Track rate limit usage
     */
    protected function trackRateLimitUsage(): void
    {
        if (!config('ai.rate_limits.enabled', true)) {
            return;
        }

        $periodMinutes = config('ai.rate_limits.period_minutes', 60);
        
        $usageData = Cache::get($this->rateLimitCacheKey, [
            'count' => 0,
            'reset_at' => now()->addMinutes($periodMinutes)
        ]);
        
        // If the reset time has passed, reset the counter
        if (now()->gt($usageData['reset_at'])) {
            $usageData = [
                'count' => 1,
                'reset_at' => now()->addMinutes($periodMinutes)
            ];
        } else {
            // Increment the counter
            $usageData['count']++;
        }
        
        // Store the updated usage data
        Cache::put($this->rateLimitCacheKey, $usageData, $usageData['reset_at']);
    }
}