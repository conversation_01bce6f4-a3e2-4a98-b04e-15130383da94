<?php

namespace App\Services\AI;

use App\Exceptions\AIServiceException;
use App\Exceptions\RateLimitException;
use App\Exceptions\ServiceUnavailableException;
use App\Services\AI\Contracts\AIServiceInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Cache;

class GeminiService implements AIServiceInterface
{
    /**
     * @var string The API key for Gemini
     */
    protected string $apiKey;

    /**
     * @var string The API URL for Gemini
     */
    protected string $apiUrl;

    /**
     * @var string The model name to use
     */
    protected string $modelName;

    /**
     * @var array Additional options for the API
     */
    protected array $options;

    /**
     * @var int Maximum number of retry attempts
     */
    protected int $maxRetries = 3;

    /**
     * @var string Cache key for rate limit tracking
     */
    protected string $rateLimitCacheKey = 'gemini_rate_limit';

    /**
     * Create a new Gemini service instance
     *
     * @param string $apiKey The API key for Gemini
     * @param string $apiUrl The API URL for Gemini
     * @param string $modelName The model name to use
     * @param array $options Additional options for the API
     */
    public function __construct(
        string $apiKey,
        string $apiUrl,
        string $modelName,
        array $options = []
    ) {
        $this->apiKey = $apiKey;
        $this->apiUrl = $apiUrl;
        $this->modelName = $modelName;
        $this->options = $options;
    }

    /**
     * Send a message to the Gemini API and get a response
     *
     * @param string $message The user message to send
     * @param array $context Additional context for the AI (e.g. conversation history)
     * @return array The AI response with at least 'content' key
     * @throws AIServiceException If the service call fails
     */
    public function sendMessage(string $message, array $context = []): array
    {
        if (!$this->isAvailable()) {
            throw new ServiceUnavailableException(
                'Gemini API is not available: API key is missing',
                'gemini',
                ['error_type' => 'configuration']
            );
        }

        // Check rate limiting
        if ($this->isRateLimited()) {
            throw new RateLimitException(
                'Rate limit exceeded for Gemini API',
                'gemini',
                ['error_type' => 'rate_limit']
            );
        }

        // Format the messages for the API
        $contents = $this->formatMessages($message, $context);

        // Prepare the request payload
        $payload = [
            'contents' => $contents,
            'generationConfig' => [
                'temperature' => $this->options['temperature'] ?? 0.7,
                'maxOutputTokens' => $this->options['max_tokens'] ?? 1000,
            ],
        ];

        return $this->makeRequest($payload);
    }

    /**
     * Make the API request with retry logic
     *
     * @param array $payload The request payload
     * @return array The parsed response
     * @throws AIServiceException If all retry attempts fail
     */
    protected function makeRequest(array $payload): array
    {
        $attempt = 0;
        $lastException = null;
        $fullUrl = $this->buildApiUrl();

        while ($attempt < $this->maxRetries) {
            try {
                // Increment the attempt counter
                $attempt++;

                // Make the API request
                $response = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->timeout($this->options['timeout'] ?? 30)
                ->post($fullUrl, $payload);

                // Track rate limit usage
                $this->trackRateLimitUsage();

                // Check for errors
                if ($response->failed()) {
                    $errorData = $response->json() ?? [];
                    $errorMessage = $errorData['error']['message'] ?? 'Unknown error';
                    $statusCode = $response->status();

                    // Handle specific error types
                    if ($statusCode === 429) {
                        throw new RateLimitException(
                            'Gemini API rate limit exceeded: ' . $errorMessage,
                            'gemini',
                            ['status_code' => $statusCode, 'error_data' => $errorData]
                        );
                    }
                    
                    if ($statusCode >= 500 && $statusCode < 600) {
                        throw new ServiceUnavailableException(
                            'Gemini API server error: ' . $errorMessage,
                            'gemini',
                            ['status_code' => $statusCode, 'error_data' => $errorData]
                        );
                    }

                    throw new AIServiceException(
                        'Gemini API error: ' . $errorMessage,
                        'gemini',
                        ['status_code' => $statusCode, 'error_data' => $errorData]
                    );
                }

                // Parse and return the response
                $responseData = $response->json();
                if (!is_array($responseData)) {
                    throw new AIServiceException(
                        'Invalid response format from Gemini API: Empty or invalid JSON response',
                        'gemini',
                        ['response' => $response->body()]
                    );
                }
                return $this->parseResponse($responseData);

            } catch (RequestException $e) {
                $lastException = new AIServiceException(
                    'Gemini API request failed: ' . $e->getMessage(),
                    'gemini',
                    ['attempt' => $attempt, 'max_retries' => $this->maxRetries],
                    $e->getCode(),
                    $e
                );

                // Log the error
                Log::warning('Gemini API request failed (attempt ' . $attempt . '/' . $this->maxRetries . '): ' . $e->getMessage());

                // Only retry on connection errors or server errors (5xx)
                if (!$this->shouldRetry($e)) {
                    break;
                }

                // Wait before retrying (exponential backoff)
                $backoffSeconds = min(2 ** $attempt, 10);
                sleep($backoffSeconds);
            } catch (\Exception $e) {
                $lastException = new AIServiceException(
                    'Gemini API error: ' . $e->getMessage(),
                    'gemini',
                    ['attempt' => $attempt],
                    $e->getCode(),
                    $e
                );

                // Log the error
                Log::error('Gemini API error: ' . $e->getMessage());
                break;
            }
        }

        // If we've exhausted all retries, throw the last exception
        throw $lastException ?? new AIServiceException(
            'Gemini API request failed after ' . $this->maxRetries . ' attempts',
            'gemini',
            ['max_retries' => $this->maxRetries]
        );
    }

    /**
     * Build the full API URL including the model name and API key
     *
     * @return string The complete API URL
     */
    protected function buildApiUrl(): string
    {
        return $this->apiUrl . '/' . $this->modelName . ':generateContent' . '?key=' . $this->apiKey;
    }

    /**
     * Determine if we should retry the request based on the exception
     *
     * @param RequestException $e The exception that occurred
     * @return bool True if we should retry, false otherwise
     */
    protected function shouldRetry(RequestException $e): bool
    {
        $statusCode = $e->response?->status() ?? 0;
        
        // Retry on connection errors or server errors (5xx)
        return $statusCode === 0 || ($statusCode >= 500 && $statusCode < 600);
    }

    /**
     * Parse the API response into a standardized format
     *
     * @param array $responseData The raw API response data
     * @return array The standardized response
     */
    protected function parseResponse(array $responseData): array
    {
        // Extract the content from the response
        $content = $responseData['candidates'][0]['content']['parts'][0]['text'] ?? null;
        
        if ($content === null) {
            throw new AIServiceException(
                'Invalid response format from Gemini API',
                'gemini',
                ['response_data' => $responseData]
            );
        }

        // Return the standardized response format
        return [
            'content' => $content,
            'model' => $this->modelName,
            'usage' => $responseData['usageMetadata'] ?? [],
            'finish_reason' => $responseData['candidates'][0]['finishReason'] ?? null,
        ];
    }

    /**
     * Format the messages for the Gemini API
     *
     * @param string $message The current user message
     * @param array $context Additional context (e.g. conversation history)
     * @return array The formatted contents
     */
    protected function formatMessages(string $message, array $context = []): array
    {
        $contents = [];

        // Add conversation history if provided
        if (isset($context['history']) && is_array($context['history'])) {
            foreach ($context['history'] as $historyMessage) {
                if (isset($historyMessage['role'], $historyMessage['content'])) {
                    $role = $historyMessage['role'] === 'user' ? 'user' : 'model';
                    $contents[] = [
                        'role' => $role,
                        'parts' => [
                            ['text' => $historyMessage['content']]
                        ]
                    ];
                }
            }
        }

        // Add the current user message
        $contents[] = [
            'role' => 'user',
            'parts' => [
                ['text' => $message]
            ]
        ];

        return $contents;
    }

    /**
     * Check if the service is currently available
     *
     * @return bool True if the service is available, false otherwise
     */
    public function isAvailable(): bool
    {
        return !empty($this->apiKey);
    }

    /**
     * Get the name of the AI model being used
     *
     * @return string The model name
     */
    public function getModelName(): string
    {
        return $this->modelName;
    }

    /**
     * Check if the service is currently rate limited
     *
     * @return bool True if rate limited, false otherwise
     */
    protected function isRateLimited(): bool
    {
        if (!config('ai.rate_limits.enabled', true)) {
            return false;
        }

        $maxRequests = config('ai.rate_limits.max_requests', 60);
        $periodMinutes = config('ai.rate_limits.period_minutes', 60);
        
        $usageData = Cache::get($this->rateLimitCacheKey, ['count' => 0, 'reset_at' => now()]);
        
        // If the reset time has passed, reset the counter
        if (now()->gt($usageData['reset_at'])) {
            return false;
        }
        
        // Check if we've exceeded the limit
        return $usageData['count'] >= $maxRequests;
    }

    /**
     * Track rate limit usage
     */
    protected function trackRateLimitUsage(): void
    {
        if (!config('ai.rate_limits.enabled', true)) {
            return;
        }

        $periodMinutes = config('ai.rate_limits.period_minutes', 60);
        
        $usageData = Cache::get($this->rateLimitCacheKey, [
            'count' => 0,
            'reset_at' => now()->addMinutes($periodMinutes)
        ]);
        
        // If the reset time has passed, reset the counter
        if (now()->gt($usageData['reset_at'])) {
            $usageData = [
                'count' => 1,
                'reset_at' => now()->addMinutes($periodMinutes)
            ];
        } else {
            // Increment the counter
            $usageData['count']++;
        }
        
        // Store the updated usage data
        Cache::put($this->rateLimitCacheKey, $usageData, $usageData['reset_at']);
    }
}