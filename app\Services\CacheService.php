<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class CacheService
{
    /**
     * @var string Cache key prefix
     */
    protected string $prefix = 'widdx_ai';

    /**
     * @var int Default cache TTL in seconds
     */
    protected int $defaultTtl = 3600;

    /**
     * Cache conversation data
     *
     * @param int $conversationId
     * @param array $data
     * @param int|null $ttl
     * @return bool
     */
    public function cacheConversation(int $conversationId, array $data, ?int $ttl = null): bool
    {
        $key = $this->generateKey('conversation', $conversationId);
        $ttl = $ttl ?? $this->defaultTtl;

        try {
            return Cache::put($key, $data, $ttl);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to cache conversation', [
                'conversation_id' => $conversationId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get cached conversation data
     *
     * @param int $conversationId
     * @return array|null
     */
    public function getCachedConversation(int $conversationId): ?array
    {
        $key = $this->generateKey('conversation', $conversationId);

        try {
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to get cached conversation', [
                'conversation_id' => $conversationId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Cache conversations list
     *
     * @param string $cacheKey
     * @param Collection $conversations
     * @param int|null $ttl
     * @return bool
     */
    public function cacheConversationsList(string $cacheKey, Collection $conversations, ?int $ttl = null): bool
    {
        $key = $this->generateKey('conversations_list', $cacheKey);
        $ttl = $ttl ?? ($this->defaultTtl / 2); // Shorter TTL for lists

        try {
            return Cache::put($key, $conversations->toArray(), $ttl);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to cache conversations list', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get cached conversations list
     *
     * @param string $cacheKey
     * @return Collection|null
     */
    public function getCachedConversationsList(string $cacheKey): ?Collection
    {
        $key = $this->generateKey('conversations_list', $cacheKey);

        try {
            $cached = Cache::get($key);
            return $cached ? collect($cached) : null;
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to get cached conversations list', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Cache AI service response
     *
     * @param string $queryHash
     * @param array $response
     * @param int|null $ttl
     * @return bool
     */
    public function cacheAIResponse(string $queryHash, array $response, ?int $ttl = null): bool
    {
        $key = $this->generateKey('ai_response', $queryHash);
        $ttl = $ttl ?? ($this->defaultTtl * 2); // Longer TTL for AI responses

        try {
            return Cache::put($key, $response, $ttl);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to cache AI response', [
                'query_hash' => $queryHash,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get cached AI service response
     *
     * @param string $queryHash
     * @return array|null
     */
    public function getCachedAIResponse(string $queryHash): ?array
    {
        $key = $this->generateKey('ai_response', $queryHash);

        try {
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to get cached AI response', [
                'query_hash' => $queryHash,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Cache knowledge base statistics
     *
     * @param array $stats
     * @param int|null $ttl
     * @return bool
     */
    public function cacheKnowledgeStats(array $stats, ?int $ttl = null): bool
    {
        $key = $this->generateKey('knowledge_stats');
        $ttl = $ttl ?? ($this->defaultTtl / 4); // Shorter TTL for stats

        try {
            return Cache::put($key, $stats, $ttl);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to cache knowledge stats', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get cached knowledge base statistics
     *
     * @return array|null
     */
    public function getCachedKnowledgeStats(): ?array
    {
        $key = $this->generateKey('knowledge_stats');

        try {
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to get cached knowledge stats', [
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Invalidate conversation cache
     *
     * @param int $conversationId
     * @return bool
     */
    public function invalidateConversation(int $conversationId): bool
    {
        $key = $this->generateKey('conversation', $conversationId);

        try {
            return Cache::forget($key);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to invalidate conversation cache', [
                'conversation_id' => $conversationId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Invalidate conversations list cache
     *
     * @return bool
     */
    public function invalidateConversationsList(): bool
    {
        try {
            $pattern = $this->generateKey('conversations_list', '*');
            return $this->clearByPattern($pattern);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to invalidate conversations list cache', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Invalidate knowledge base related cache
     *
     * @return bool
     */
    public function invalidateKnowledgeCache(): bool
    {
        try {
            $patterns = [
                $this->generateKey('knowledge_stats'),
                $this->generateKey('knowledge_base', '*'),
            ];

            $success = true;
            foreach ($patterns as $pattern) {
                $success = $success && $this->clearByPattern($pattern);
            }

            return $success;
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to invalidate knowledge cache', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Clear all application cache
     *
     * @return bool
     */
    public function clearAll(): bool
    {
        try {
            $pattern = $this->prefix . ':*';
            return $this->clearByPattern($pattern);
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to clear all cache', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Generate cache key
     *
     * @param string $type
     * @param string|int|null $identifier
     * @return string
     */
    protected function generateKey(string $type, $identifier = null): string
    {
        $key = $this->prefix . ':' . $type;
        
        if ($identifier !== null) {
            $key .= ':' . $identifier;
        }

        return $key;
    }

    /**
     * Clear cache by pattern
     *
     * @param string $pattern
     * @return bool
     */
    protected function clearByPattern(string $pattern): bool
    {
        try {
            // For Redis cache
            if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                $keys = Cache::getRedis()->keys($pattern);
                if (!empty($keys)) {
                    return Cache::getRedis()->del($keys) > 0;
                }
                return true;
            }

            // For other cache stores, we can't use patterns, so clear specific keys
            // This is a limitation of non-Redis cache stores
            Log::info('CacheService: Pattern clearing not supported for current cache store', [
                'pattern' => $pattern,
                'store' => get_class(Cache::getStore()),
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('CacheService: Failed to clear cache by pattern', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get cache statistics
     *
     * @return array
     */
    public function getStats(): array
    {
        try {
            $stats = [
                'store_type' => get_class(Cache::getStore()),
                'prefix' => $this->prefix,
                'default_ttl' => $this->defaultTtl,
            ];

            // Add Redis-specific stats if available
            if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                $redis = Cache::getRedis();
                $info = $redis->info();
                
                $stats['redis'] = [
                    'connected_clients' => $info['connected_clients'] ?? 'unknown',
                    'used_memory_human' => $info['used_memory_human'] ?? 'unknown',
                    'keyspace_hits' => $info['keyspace_hits'] ?? 'unknown',
                    'keyspace_misses' => $info['keyspace_misses'] ?? 'unknown',
                ];
            }

            return $stats;
        } catch (\Exception $e) {
            Log::warning('CacheService: Failed to get cache stats', [
                'error' => $e->getMessage(),
            ]);
            
            return [
                'store_type' => get_class(Cache::getStore()),
                'prefix' => $this->prefix,
                'default_ttl' => $this->defaultTtl,
                'error' => 'Failed to retrieve detailed stats',
            ];
        }
    }
}
