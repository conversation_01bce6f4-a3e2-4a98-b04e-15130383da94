<?php

namespace App\Services;

use App\Services\AI\Contracts\AIServiceInterface;
use App\Exceptions\AIServiceException;
use App\Exceptions\ServiceUnavailableException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class DecisionEngine
{
    /**
     * @var array Available AI services
     */
    protected array $services;

    /**
     * @var array Routing configuration
     */
    protected array $routingConfig;

    /**
     * @var array Fallback configuration
     */
    protected array $fallbackConfig;

    /**
     * @var string Cache key for routing statistics
     */
    protected string $statsCacheKey = 'decision_engine_stats';

    /**
     * Create a new DecisionEngine instance
     *
     * @param array $services Available AI services
     */
    public function __construct(array $services = [])
    {
        $this->services = $services;
        $this->routingConfig = config('ai.routing', []);
        $this->fallbackConfig = config('ai.fallback', []);
    }

    /**
     * Analyze query and route to appropriate AI service
     *
     * @param string $query The user query to analyze
     * @param array $context Additional context for routing decision
     * @return array Response with service name and result
     * @throws AIServiceException If all services fail
     */
    public function routeQuery(string $query, array $context = []): array
    {
        $primaryService = $this->analyzeQuery($query);

        Log::info('DecisionEngine: Query routed', [
            'query_length' => strlen($query),
            'primary_service' => $primaryService,
            'context_keys' => array_keys($context)
        ]);

        // Try primary service first
        try {
            $result = $this->executeWithService($primaryService, $query, $context);
            $this->updateStats($primaryService, true);
            return [
                'service_used' => $primaryService,
                'fallback_used' => false,
                'result' => $result
            ];
        } catch (AIServiceException $e) {
            Log::warning('DecisionEngine: Primary service failed', [
                'service' => $primaryService,
                'error' => $e->getMessage()
            ]);
            $this->updateStats($primaryService, false);
        }

        // Try fallback strategy if enabled
        if ($this->fallbackConfig['enabled'] ?? false) {
            return $this->executeFallbackStrategy($query, $context, $primaryService);
        }

        throw new ServiceUnavailableException("All AI services are currently unavailable");
    }

    /**
     * Analyze query to determine the most appropriate AI service
     *
     * @param string $query The query to analyze
     * @return string The service name to use
     */
    public function analyzeQuery(string $query): string
    {
        $originalQuery = $query;
        $query = strtolower(trim($query));
        $scores = [];

        foreach ($this->routingConfig as $category => $config) {
            $score = 0;

            // Check keywords
            if (isset($config['keywords'])) {
                foreach ($config['keywords'] as $keyword) {
                    if (strpos($query, strtolower($keyword)) !== false) {
                        $score += 1;
                    }
                }
            }

            // Check patterns (use original query for case-sensitive patterns)
            if (isset($config['patterns'])) {
                foreach ($config['patterns'] as $pattern) {
                    if (preg_match($pattern, $originalQuery)) {
                        $score += 3; // Patterns have higher weight than keywords
                    }
                }
            }

            if ($score > 0) {
                $scores[$config['service']] = ($scores[$config['service']] ?? 0) + $score;
            }
        }

        // Return service with highest score, or default if no matches
        if (!empty($scores)) {
            arsort($scores);
            return array_key_first($scores);
        }

        return config('ai.default_service', 'deepseek');
    }

    /**
     * Execute fallback strategy when primary service fails
     *
     * @param string $query The query to execute
     * @param array $context Query context
     * @param string $failedService The service that failed
     * @return array Response with fallback service result
     * @throws ServiceUnavailableException If all fallback services fail
     */
    protected function executeFallbackStrategy(string $query, array $context, string $failedService): array
    {
        $fallbackOrder = $this->fallbackConfig['order'] ?? [];

        // Remove the failed service from fallback order
        $fallbackOrder = array_filter($fallbackOrder, fn($service) => $service !== $failedService);

        foreach ($fallbackOrder as $serviceName) {
            try {
                $result = $this->executeWithService($serviceName, $query, $context);
                $this->updateStats($serviceName, true, true);

                Log::info('DecisionEngine: Fallback successful', [
                    'fallback_service' => $serviceName,
                    'failed_service' => $failedService
                ]);

                return [
                    'service_used' => $serviceName,
                    'fallback_used' => true,
                    'result' => $result
                ];
            } catch (AIServiceException $e) {
                Log::warning('DecisionEngine: Fallback service failed', [
                    'service' => $serviceName,
                    'error' => $e->getMessage()
                ]);
                $this->updateStats($serviceName, false, true);
                continue;
            }
        }

        throw new ServiceUnavailableException("All AI services including fallbacks are currently unavailable");
    }

    /**
     * Execute query with specific AI service
     *
     * @param string $serviceName Name of the service to use
     * @param string $query The query to execute
     * @param array $context Query context
     * @return array Service response
     * @throws AIServiceException If service execution fails
     */
    public function executeWithService(string $serviceName, string $query, array $context): array
    {
        if (!isset($this->services[$serviceName])) {
            throw new ServiceUnavailableException("Service '{$serviceName}' is not available");
        }

        $service = $this->services[$serviceName];

        if (!$service instanceof AIServiceInterface) {
            throw new AIServiceException("Service '{$serviceName}' does not implement AIServiceInterface");
        }

        if (!$service->isAvailable()) {
            throw new ServiceUnavailableException("Service '{$serviceName}' is currently unavailable");
        }

        return $service->sendMessage($query, $context);
    }

    /**
     * Update routing statistics
     *
     * @param string $serviceName Service that was used
     * @param bool $success Whether the call was successful
     * @param bool $isFallback Whether this was a fallback call
     */
    protected function updateStats(string $serviceName, bool $success, bool $isFallback = false): void
    {
        $stats = Cache::get($this->statsCacheKey, []);

        $key = $isFallback ? 'fallback' : 'primary';
        $stats[$serviceName][$key]['total'] = ($stats[$serviceName][$key]['total'] ?? 0) + 1;

        if ($success) {
            $stats[$serviceName][$key]['successful'] = ($stats[$serviceName][$key]['successful'] ?? 0) + 1;
        } else {
            $stats[$serviceName][$key]['failed'] = ($stats[$serviceName][$key]['failed'] ?? 0) + 1;
        }

        Cache::put($this->statsCacheKey, $stats, now()->addHours(24));
    }

    /**
     * Get routing statistics
     *
     * @return array Current routing statistics
     */
    public function getStats(): array
    {
        return Cache::get($this->statsCacheKey, []);
    }

    /**
     * Clear routing statistics
     */
    public function clearStats(): void
    {
        Cache::forget($this->statsCacheKey);
    }

    /**
     * Check if a specific service is available
     *
     * @param string $serviceName Name of the service to check
     * @return bool True if service is available
     */
    public function isServiceAvailable(string $serviceName): bool
    {
        if (!isset($this->services[$serviceName])) {
            return false;
        }

        $service = $this->services[$serviceName];

        if (!$service instanceof AIServiceInterface) {
            return false;
        }

        return $service->isAvailable();
    }

    /**
     * Get list of available services
     *
     * @return array List of available service names
     */
    public function getAvailableServices(): array
    {
        $available = [];

        foreach ($this->services as $name => $service) {
            if ($service instanceof AIServiceInterface && $service->isAvailable()) {
                $available[] = $name;
            }
        }

        return $available;
    }

    /**
     * Set AI services for dependency injection
     *
     * @param array $services Array of AI services
     */
    public function setServices(array $services): void
    {
        $this->services = $services;
    }
}
