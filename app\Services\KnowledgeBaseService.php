<?php

namespace App\Services;

use App\Models\KnowledgeEntry;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Exception;

class KnowledgeBaseService
{
    /**
     * @var string Cache key prefix for knowledge base results
     */
    protected string $cachePrefix = 'knowledge_base';

    /**
     * @var int Cache TTL in seconds
     */
    protected int $cacheTtl;

    /**
     * @var float Default similarity threshold
     */
    protected float $defaultThreshold;

    /**
     * @var int Default embedding dimension
     */
    protected int $embeddingDimension;

    /**
     * @var string Embedding service URL
     */
    protected string $embeddingServiceUrl;

    /**
     * Create a new KnowledgeBaseService instance
     */
    public function __construct()
    {
        $this->cacheTtl = config('ai.knowledge_base.cache_ttl', 3600);
        $this->defaultThreshold = config('ai.knowledge_base.similarity_threshold', 0.7);
        $this->embeddingDimension = config('ai.knowledge_base.embedding_dimension', 384);
        $this->embeddingServiceUrl = config('ai.knowledge_base.embedding_service_url', 'https://api-inference.huggingface.co/pipeline/feature-extraction/sentence-transformers/all-MiniLM-L6-v2');
    }

    /**
     * Search for relevant knowledge entries based on query
     *
     * @param string $query The search query
     * @param float|null $threshold Similarity threshold (optional)
     * @param int $limit Maximum number of results
     * @param array $tags Filter by specific tags (optional)
     * @param string|null $source Filter by specific source (optional)
     * @return Collection Collection of relevant knowledge entries
     */
    public function search(string $query, ?float $threshold = null, int $limit = 5, array $tags = [], ?string $source = null): Collection
    {
        $threshold = $threshold ?? $this->defaultThreshold;
        $cacheKey = $this->generateCacheKey($query, $threshold, $limit, $tags, $source);

        // Try to get from cache first
        if (config('ai.knowledge_base.cache_enabled', true)) {
            $cached = Cache::get($cacheKey);
            if ($cached !== null) {
                Log::debug('KnowledgeBaseService: Cache hit', ['query' => substr($query, 0, 50)]);
                return collect($cached);
            }
        }

        try {
            // Generate embedding for the query
            $queryEmbedding = $this->generateEmbedding($query);

            // Find similar entries
            $results = KnowledgeEntry::findSimilar($queryEmbedding, $threshold, $limit * 2); // Get more to filter

            // Apply additional filters
            if (!empty($tags)) {
                $results = $results->filter(function ($entry) use ($tags) {
                    return !empty(array_intersect($entry->tags ?? [], $tags));
                });
            }

            if ($source) {
                $results = $results->filter(function ($entry) use ($source) {
                    return $entry->source === $source;
                });
            }

            // Limit results
            $results = $results->take($limit);

            // Cache the results
            if (config('ai.knowledge_base.cache_enabled', true)) {
                Cache::put($cacheKey, $results->toArray(), $this->cacheTtl);
            }

            Log::info('KnowledgeBaseService: Search completed', [
                'query_length' => strlen($query),
                'results_count' => $results->count(),
                'threshold' => $threshold
            ]);

            return $results;

        } catch (Exception $e) {
            Log::error('KnowledgeBaseService: Search failed', [
                'query' => substr($query, 0, 50),
                'error' => $e->getMessage()
            ]);

            return collect();
        }
    }

    /**
     * Create a new knowledge entry
     *
     * @param string $content The content of the knowledge entry
     * @param string|null $source Source of the knowledge (optional)
     * @param array $tags Tags for the knowledge entry (optional)
     * @param float|null $confidenceScore Confidence score (optional)
     * @return KnowledgeEntry The created knowledge entry
     * @throws Exception If embedding generation fails
     */
    public function createEntry(string $content, ?string $source = null, array $tags = [], ?float $confidenceScore = null): KnowledgeEntry
    {
        try {
            // Generate embedding for the content
            $embedding = $this->generateEmbedding($content);

            // Create the knowledge entry
            $entry = KnowledgeEntry::create([
                'content' => $content,
                'embedding_vector' => $embedding,
                'source' => $source,
                'tags' => $tags,
                'confidence_score' => $confidenceScore ?? 0.8,
            ]);

            // Clear related cache
            $this->clearCache();

            Log::info('KnowledgeBaseService: Entry created', [
                'entry_id' => $entry->id,
                'content_length' => strlen($content),
                'source' => $source,
                'tags_count' => count($tags)
            ]);

            return $entry;

        } catch (Exception $e) {
            Log::error('KnowledgeBaseService: Entry creation failed', [
                'content_length' => strlen($content),
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Update an existing knowledge entry
     *
     * @param int $entryId The ID of the entry to update
     * @param array $data Data to update
     * @return KnowledgeEntry|null The updated entry or null if not found
     * @throws Exception If embedding generation fails
     */
    public function updateEntry(int $entryId, array $data): ?KnowledgeEntry
    {
        $entry = KnowledgeEntry::find($entryId);

        if (!$entry) {
            return null;
        }

        try {
            // If content is being updated, regenerate embedding
            if (isset($data['content'])) {
                $data['embedding_vector'] = $this->generateEmbedding($data['content']);
            }

            $entry->update($data);

            // Clear related cache
            $this->clearCache();

            Log::info('KnowledgeBaseService: Entry updated', [
                'entry_id' => $entryId,
                'updated_fields' => array_keys($data)
            ]);

            return $entry->fresh();

        } catch (Exception $e) {
            Log::error('KnowledgeBaseService: Entry update failed', [
                'entry_id' => $entryId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Delete a knowledge entry
     *
     * @param int $entryId The ID of the entry to delete
     * @return bool True if deleted, false if not found
     */
    public function deleteEntry(int $entryId): bool
    {
        $entry = KnowledgeEntry::find($entryId);

        if (!$entry) {
            return false;
        }

        $entry->delete();

        // Clear related cache
        $this->clearCache();

        Log::info('KnowledgeBaseService: Entry deleted', ['entry_id' => $entryId]);

        return true;
    }

    /**
     * Add tags to a knowledge entry
     *
     * @param int $entryId The ID of the entry
     * @param array|string $tags Tags to add
     * @return KnowledgeEntry|null The updated entry or null if not found
     */
    public function addTags(int $entryId, $tags): ?KnowledgeEntry
    {
        $entry = KnowledgeEntry::find($entryId);

        if (!$entry) {
            return null;
        }

        $entry->addTags($tags);

        // Clear related cache
        $this->clearCache();

        Log::info('KnowledgeBaseService: Tags added', [
            'entry_id' => $entryId,
            'tags' => is_array($tags) ? $tags : [$tags]
        ]);

        return $entry;
    }

    /**
     * Remove tags from a knowledge entry
     *
     * @param int $entryId The ID of the entry
     * @param array|string $tags Tags to remove
     * @return KnowledgeEntry|null The updated entry or null if not found
     */
    public function removeTags(int $entryId, $tags): ?KnowledgeEntry
    {
        $entry = KnowledgeEntry::find($entryId);

        if (!$entry) {
            return null;
        }

        $entry->removeTags($tags);

        // Clear related cache
        $this->clearCache();

        Log::info('KnowledgeBaseService: Tags removed', [
            'entry_id' => $entryId,
            'tags' => is_array($tags) ? $tags : [$tags]
        ]);

        return $entry;
    }

    /**
     * Get knowledge entries by source
     *
     * @param string $source The source to filter by
     * @param int $limit Maximum number of results
     * @return Collection Collection of knowledge entries
     */
    public function getBySource(string $source, int $limit = 50): Collection
    {
        return KnowledgeEntry::fromSource($source)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get knowledge entries by tag
     *
     * @param string $tag The tag to filter by
     * @param int $limit Maximum number of results
     * @return Collection Collection of knowledge entries
     */
    public function getByTag(string $tag, int $limit = 50): Collection
    {
        return KnowledgeEntry::withTag($tag)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get all unique sources
     *
     * @return Collection Collection of unique sources
     */
    public function getAllSources(): Collection
    {
        return KnowledgeEntry::whereNotNull('source')
            ->distinct()
            ->pluck('source');
    }

    /**
     * Get all unique tags
     *
     * @return Collection Collection of unique tags
     */
    public function getAllTags(): Collection
    {
        $allTags = KnowledgeEntry::whereNotNull('tags')
            ->pluck('tags')
            ->flatten()
            ->unique()
            ->values();

        return $allTags;
    }

    /**
     * Get knowledge base statistics
     *
     * @return array Statistics about the knowledge base
     */
    public function getStatistics(): array
    {
        $totalEntries = KnowledgeEntry::count();
        $sourcesCount = KnowledgeEntry::whereNotNull('source')->distinct('source')->count();
        $tagsCount = $this->getAllTags()->count();
        $averageConfidence = KnowledgeEntry::avg('confidence_score');

        return [
            'total_entries' => $totalEntries,
            'unique_sources' => $sourcesCount,
            'unique_tags' => $tagsCount,
            'average_confidence' => round($averageConfidence, 2),
            'last_updated' => KnowledgeEntry::latest()->first()?->updated_at,
        ];
    }

    /**
     * Generate embedding vector for given text
     *
     * @param string $text The text to generate embedding for
     * @return array The embedding vector
     * @throws Exception If embedding generation fails
     */
    protected function generateEmbedding(string $text): array
    {
        // For now, we'll use a simple mock embedding generation
        // In production, this should call an actual embedding service
        if (config('ai.knowledge_base.use_mock_embeddings', true)) {
            return $this->generateMockEmbedding($text);
        }

        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . config('ai.services.huggingface.api_key'),
                    'Content-Type' => 'application/json',
                ])
                ->post($this->embeddingServiceUrl, [
                    'inputs' => $text,
                    'options' => [
                        'wait_for_model' => true,
                    ],
                ]);

            if (!$response->successful()) {
                throw new Exception('Embedding service returned error: ' . $response->body());
            }

            $embedding = $response->json();

            if (!is_array($embedding) || empty($embedding)) {
                throw new Exception('Invalid embedding response format');
            }

            // Ensure the embedding has the correct dimension
            if (count($embedding) !== $this->embeddingDimension) {
                throw new Exception('Embedding dimension mismatch');
            }

            return $embedding;

        } catch (Exception $e) {
            Log::error('KnowledgeBaseService: Embedding generation failed', [
                'text_length' => strlen($text),
                'error' => $e->getMessage()
            ]);

            // Fallback to mock embedding
            return $this->generateMockEmbedding($text);
        }
    }

    /**
     * Generate a mock embedding vector for testing purposes
     *
     * @param string $text The text to generate mock embedding for
     * @return array Mock embedding vector
     */
    protected function generateMockEmbedding(string $text): array
    {
        // Generate a deterministic but pseudo-random embedding based on text content
        $hash = md5($text);
        $embedding = [];

        for ($i = 0; $i < $this->embeddingDimension; $i++) {
            $seed = hexdec(substr($hash, $i % 32, 2)) + $i;
            $embedding[] = (sin($seed) + cos($seed * 2)) / 2;
        }

        // Normalize the vector
        $magnitude = sqrt(array_sum(array_map(fn($x) => $x * $x, $embedding)));
        if ($magnitude > 0) {
            $embedding = array_map(fn($x) => $x / $magnitude, $embedding);
        }

        return $embedding;
    }

    /**
     * Generate cache key for search results
     *
     * @param string $query The search query
     * @param float $threshold Similarity threshold
     * @param int $limit Result limit
     * @param array $tags Filter tags
     * @param string|null $source Filter source
     * @return string Cache key
     */
    protected function generateCacheKey(string $query, float $threshold, int $limit, array $tags, ?string $source): string
    {
        $keyData = [
            'query' => md5($query),
            'threshold' => $threshold,
            'limit' => $limit,
            'tags' => sort($tags) ? implode(',', $tags) : '',
            'source' => $source ?? '',
        ];

        return $this->cachePrefix . ':search:' . md5(serialize($keyData));
    }

    /**
     * Clear knowledge base cache
     */
    protected function clearCache(): void
    {
        if (config('ai.knowledge_base.cache_enabled', true)) {
            try {
                // For Redis cache
                if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                    $keys = Cache::getRedis()->keys($this->cachePrefix . ':*');
                    if (!empty($keys)) {
                        Cache::getRedis()->del($keys);
                    }
                } else {
                    // For other cache stores, clear specific known keys
                    $keysToDelete = [
                        $this->cachePrefix . ':search:*',
                        $this->cachePrefix . ':entry:*',
                        $this->cachePrefix . ':stats',
                    ];

                    foreach ($keysToDelete as $pattern) {
                        // For non-Redis stores, we can't use patterns, so we'll just flush all cache
                        Cache::flush();
                        break;
                    }
                }
            } catch (\Exception $e) {
                Log::warning('KnowledgeBaseService: Failed to clear cache', [
                    'error' => $e->getMessage(),
                    'cache_store' => get_class(Cache::getStore()),
                ]);
            }
        }
    }

    /**
     * Check if knowledge base is enabled
     *
     * @return bool True if enabled
     */
    public function isEnabled(): bool
    {
        return config('ai.knowledge_base.enabled', true);
    }

    /**
     * Bulk import knowledge entries from array
     *
     * @param array $entries Array of entries with content, source, tags, etc.
     * @return array Results with success/failure counts
     */
    public function bulkImport(array $entries): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        foreach ($entries as $index => $entryData) {
            try {
                $this->createEntry(
                    $entryData['content'] ?? '',
                    $entryData['source'] ?? null,
                    $entryData['tags'] ?? [],
                    $entryData['confidence_score'] ?? null
                );
                $results['success']++;
            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Entry {$index}: " . $e->getMessage();
            }
        }

        Log::info('KnowledgeBaseService: Bulk import completed', $results);

        return $results;
    }
}
