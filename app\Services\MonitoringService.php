<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class MonitoringService
{
    /**
     * @var string Cache prefix for metrics
     */
    protected string $metricsPrefix = 'metrics';

    /**
     * @var int Metrics retention period in seconds
     */
    protected int $metricsRetention = 86400; // 24 hours

    /**
     * Log AI service call with performance metrics
     *
     * @param string $service
     * @param string $model
     * @param array $request
     * @param array $response
     * @param float $duration
     * @param bool $success
     * @param string|null $error
     */
    public function logAIServiceCall(
        string $service,
        string $model,
        array $request,
        array $response,
        float $duration,
        bool $success,
        ?string $error = null
    ): void {
        $logData = [
            'service' => $service,
            'model' => $model,
            'request_size' => strlen(json_encode($request)),
            'response_size' => strlen(json_encode($response)),
            'duration_ms' => round($duration * 1000, 2),
            'success' => $success,
            'error' => $error,
            'timestamp' => now()->toISOString(),
        ];

        // Log the service call
        if ($success) {
            Log::info('AI Service Call Successful', $logData);
        } else {
            Log::error('AI Service Call Failed', $logData);
        }

        // Update metrics
        $this->updateServiceMetrics($service, $duration, $success);
    }

    /**
     * Log user interaction
     *
     * @param string $action
     * @param array $context
     * @param string|null $userId
     * @param string|null $sessionId
     */
    public function logUserInteraction(
        string $action,
        array $context,
        ?string $userId = null,
        ?string $sessionId = null
    ): void {
        $logData = [
            'action' => $action,
            'user_id' => $userId,
            'session_id' => $sessionId,
            'context' => $context,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ];

        Log::info('User Interaction', $logData);

        // Update interaction metrics
        $this->updateInteractionMetrics($action);
    }

    /**
     * Log knowledge base search
     *
     * @param string $query
     * @param int $resultsFound
     * @param float $searchTime
     * @param float $threshold
     * @param array $tags
     * @param string|null $source
     */
    public function logKnowledgeBaseSearch(
        string $query,
        int $resultsFound,
        float $searchTime,
        float $threshold,
        array $tags = [],
        ?string $source = null
    ): void {
        $logData = [
            'query_length' => strlen($query),
            'results_found' => $resultsFound,
            'search_time_ms' => round($searchTime * 1000, 2),
            'threshold' => $threshold,
            'tags' => $tags,
            'source_filter' => $source,
            'timestamp' => now()->toISOString(),
        ];

        Log::info('Knowledge Base Search', $logData);

        // Update search metrics
        $this->updateSearchMetrics($resultsFound, $searchTime);
    }

    /**
     * Log performance metrics
     *
     * @param string $operation
     * @param float $duration
     * @param array $context
     */
    public function logPerformanceMetric(string $operation, float $duration, array $context = []): void
    {
        $logData = [
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'context' => $context,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'timestamp' => now()->toISOString(),
        ];

        Log::info('Performance Metric', $logData);

        // Update performance metrics
        $this->updatePerformanceMetrics($operation, $duration);
    }

    /**
     * Log error with context
     *
     * @param string $error
     * @param array $context
     * @param string $level
     */
    public function logError(string $error, array $context = [], string $level = 'error'): void
    {
        $logData = [
            'error_message' => $error,
            'context' => $context,
            'stack_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10),
            'timestamp' => now()->toISOString(),
        ];

        Log::log($level, 'Application Error', $logData);

        // Update error metrics
        $this->updateErrorMetrics($level);
    }

    /**
     * Get service performance metrics
     *
     * @param string|null $service
     * @return array
     */
    public function getServiceMetrics(?string $service = null): array
    {
        $cacheKey = $this->metricsPrefix . ':services' . ($service ? ":{$service}" : '');
        
        $metrics = Cache::get($cacheKey, [
            'total_calls' => 0,
            'successful_calls' => 0,
            'failed_calls' => 0,
            'average_duration' => 0,
            'total_duration' => 0,
        ]);

        if ($service) {
            return $metrics;
        }

        // Get metrics for all services
        $services = ['deepseek', 'gemini', 'huggingface'];
        $allMetrics = [];

        foreach ($services as $serviceName) {
            $serviceKey = $this->metricsPrefix . ":services:{$serviceName}";
            $allMetrics[$serviceName] = Cache::get($serviceKey, [
                'total_calls' => 0,
                'successful_calls' => 0,
                'failed_calls' => 0,
                'average_duration' => 0,
                'total_duration' => 0,
            ]);
        }

        return $allMetrics;
    }

    /**
     * Get interaction metrics
     *
     * @return array
     */
    public function getInteractionMetrics(): array
    {
        $cacheKey = $this->metricsPrefix . ':interactions';
        
        return Cache::get($cacheKey, [
            'total_interactions' => 0,
            'ask_requests' => 0,
            'conversation_views' => 0,
            'conversation_deletions' => 0,
            'title_updates' => 0,
        ]);
    }

    /**
     * Get search metrics
     *
     * @return array
     */
    public function getSearchMetrics(): array
    {
        $cacheKey = $this->metricsPrefix . ':searches';
        
        return Cache::get($cacheKey, [
            'total_searches' => 0,
            'average_results' => 0,
            'average_search_time' => 0,
            'total_search_time' => 0,
        ]);
    }

    /**
     * Get performance metrics
     *
     * @return array
     */
    public function getPerformanceMetrics(): array
    {
        $cacheKey = $this->metricsPrefix . ':performance';
        
        return Cache::get($cacheKey, [
            'operations' => [],
        ]);
    }

    /**
     * Get error metrics
     *
     * @return array
     */
    public function getErrorMetrics(): array
    {
        $cacheKey = $this->metricsPrefix . ':errors';
        
        return Cache::get($cacheKey, [
            'total_errors' => 0,
            'error_levels' => [
                'emergency' => 0,
                'alert' => 0,
                'critical' => 0,
                'error' => 0,
                'warning' => 0,
                'notice' => 0,
                'info' => 0,
                'debug' => 0,
            ],
        ]);
    }

    /**
     * Get system health metrics
     *
     * @return array
     */
    public function getSystemHealth(): array
    {
        $health = [
            'status' => 'healthy',
            'checks' => [],
            'timestamp' => now()->toISOString(),
        ];

        // Database health check
        try {
            DB::connection()->getPdo();
            $health['checks']['database'] = [
                'status' => 'healthy',
                'response_time' => $this->measureDatabaseResponseTime(),
            ];
        } catch (\Exception $e) {
            $health['status'] = 'unhealthy';
            $health['checks']['database'] = [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }

        // Cache health check
        try {
            Cache::put('health_check', 'ok', 60);
            $cached = Cache::get('health_check');
            $health['checks']['cache'] = [
                'status' => $cached === 'ok' ? 'healthy' : 'unhealthy',
            ];
        } catch (\Exception $e) {
            $health['checks']['cache'] = [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }

        // Memory usage check
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $memoryPercentage = ($memoryUsage / $memoryLimit) * 100;

        $health['checks']['memory'] = [
            'status' => $memoryPercentage < 80 ? 'healthy' : 'warning',
            'usage_bytes' => $memoryUsage,
            'usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'percentage' => round($memoryPercentage, 2),
        ];

        return $health;
    }

    /**
     * Clear all metrics
     */
    public function clearMetrics(): void
    {
        $keys = [
            $this->metricsPrefix . ':services',
            $this->metricsPrefix . ':services:deepseek',
            $this->metricsPrefix . ':services:gemini',
            $this->metricsPrefix . ':services:huggingface',
            $this->metricsPrefix . ':interactions',
            $this->metricsPrefix . ':searches',
            $this->metricsPrefix . ':performance',
            $this->metricsPrefix . ':errors',
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }

        Log::info('Monitoring: Metrics cleared');
    }

    /**
     * Update service metrics
     */
    protected function updateServiceMetrics(string $service, float $duration, bool $success): void
    {
        $cacheKey = $this->metricsPrefix . ":services:{$service}";
        
        $metrics = Cache::get($cacheKey, [
            'total_calls' => 0,
            'successful_calls' => 0,
            'failed_calls' => 0,
            'average_duration' => 0,
            'total_duration' => 0,
        ]);

        $metrics['total_calls']++;
        $metrics['total_duration'] += $duration;
        $metrics['average_duration'] = $metrics['total_duration'] / $metrics['total_calls'];

        if ($success) {
            $metrics['successful_calls']++;
        } else {
            $metrics['failed_calls']++;
        }

        Cache::put($cacheKey, $metrics, $this->metricsRetention);
    }

    /**
     * Update interaction metrics
     */
    protected function updateInteractionMetrics(string $action): void
    {
        $cacheKey = $this->metricsPrefix . ':interactions';
        
        $metrics = Cache::get($cacheKey, [
            'total_interactions' => 0,
            'ask_requests' => 0,
            'conversation_views' => 0,
            'conversation_deletions' => 0,
            'title_updates' => 0,
        ]);

        $metrics['total_interactions']++;

        switch ($action) {
            case 'ask':
                $metrics['ask_requests']++;
                break;
            case 'view_conversation':
                $metrics['conversation_views']++;
                break;
            case 'delete_conversation':
                $metrics['conversation_deletions']++;
                break;
            case 'update_title':
                $metrics['title_updates']++;
                break;
        }

        Cache::put($cacheKey, $metrics, $this->metricsRetention);
    }

    /**
     * Update search metrics
     */
    protected function updateSearchMetrics(int $resultsFound, float $searchTime): void
    {
        $cacheKey = $this->metricsPrefix . ':searches';
        
        $metrics = Cache::get($cacheKey, [
            'total_searches' => 0,
            'total_results' => 0,
            'average_results' => 0,
            'total_search_time' => 0,
            'average_search_time' => 0,
        ]);

        $metrics['total_searches']++;
        $metrics['total_results'] += $resultsFound;
        $metrics['total_search_time'] += $searchTime;
        $metrics['average_results'] = $metrics['total_results'] / $metrics['total_searches'];
        $metrics['average_search_time'] = $metrics['total_search_time'] / $metrics['total_searches'];

        Cache::put($cacheKey, $metrics, $this->metricsRetention);
    }

    /**
     * Update performance metrics
     */
    protected function updatePerformanceMetrics(string $operation, float $duration): void
    {
        $cacheKey = $this->metricsPrefix . ':performance';
        
        $metrics = Cache::get($cacheKey, ['operations' => []]);

        if (!isset($metrics['operations'][$operation])) {
            $metrics['operations'][$operation] = [
                'count' => 0,
                'total_duration' => 0,
                'average_duration' => 0,
                'min_duration' => $duration,
                'max_duration' => $duration,
            ];
        }

        $op = &$metrics['operations'][$operation];
        $op['count']++;
        $op['total_duration'] += $duration;
        $op['average_duration'] = $op['total_duration'] / $op['count'];
        $op['min_duration'] = min($op['min_duration'], $duration);
        $op['max_duration'] = max($op['max_duration'], $duration);

        Cache::put($cacheKey, $metrics, $this->metricsRetention);
    }

    /**
     * Update error metrics
     */
    protected function updateErrorMetrics(string $level): void
    {
        $cacheKey = $this->metricsPrefix . ':errors';
        
        $metrics = Cache::get($cacheKey, [
            'total_errors' => 0,
            'error_levels' => [
                'emergency' => 0,
                'alert' => 0,
                'critical' => 0,
                'error' => 0,
                'warning' => 0,
                'notice' => 0,
                'info' => 0,
                'debug' => 0,
            ],
        ]);

        $metrics['total_errors']++;
        if (isset($metrics['error_levels'][$level])) {
            $metrics['error_levels'][$level]++;
        }

        Cache::put($cacheKey, $metrics, $this->metricsRetention);
    }

    /**
     * Measure database response time
     */
    protected function measureDatabaseResponseTime(): float
    {
        $start = microtime(true);
        DB::select('SELECT 1');
        return round((microtime(true) - $start) * 1000, 2);
    }

    /**
     * Parse memory limit string to bytes
     */
    protected function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}
