<?php

namespace Database\Factories;

use App\Models\KnowledgeEntry;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\KnowledgeEntry>
 */
class KnowledgeEntryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = KnowledgeEntry::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'content' => $this->faker->paragraph(3),
            'embedding_vector' => $this->generateRandomEmbedding(),
            'source' => $this->faker->randomElement(['user_input', 'documentation', 'api_response', null]),
            'tags' => $this->faker->randomElements(['code', 'research', 'nlp', 'creative', 'technical'], $this->faker->numberBetween(0, 3)),
            'confidence_score' => $this->faker->randomFloat(2, 0, 1),
            'created_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'updated_at' => function (array $attributes) {
                return $attributes['created_at'];
            },
        ];
    }

    /**
     * Generate a random embedding vector for testing.
     *
     * @param int $dimensions
     * @return array
     */
    protected function generateRandomEmbedding(int $dimensions = 3): array
    {
        $embedding = [];
        for ($i = 0; $i < $dimensions; $i++) {
            $embedding[] = $this->faker->randomFloat(2, -1, 1);
        }
        return $embedding;
    }

    /**
     * Set a specific embedding vector.
     *
     * @param array $vector
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withEmbedding(array $vector)
    {
        return $this->state(function (array $attributes) use ($vector) {
            return [
                'embedding_vector' => $vector,
            ];
        });
    }

    /**
     * Set specific tags.
     *
     * @param array $tags
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withTags(array $tags)
    {
        return $this->state(function (array $attributes) use ($tags) {
            return [
                'tags' => $tags,
            ];
        });
    }
}