<?php

namespace Database\Factories;

use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Message>
 */
class MessageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Message::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'conversation_id' => Conversation::factory(),
            'role' => $this->faker->randomElement(['user', 'assistant']),
            'model_used' => $this->faker->randomElement([null, 'deepseek', 'gemini', 'huggingface']),
            'content' => $this->faker->paragraph(3),
            'metadata' => null,
            'created_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'updated_at' => function (array $attributes) {
                return $attributes['created_at'];
            },
        ];
    }

    /**
     * Indicate that the message is from a user.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function user()
    {
        return $this->state(function (array $attributes) {
            return [
                'role' => 'user',
                'model_used' => null,
            ];
        });
    }

    /**
     * Indicate that the message is from the assistant.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function assistant()
    {
        return $this->state(function (array $attributes) {
            return [
                'role' => 'assistant',
                'model_used' => $this->faker->randomElement(['deepseek', 'gemini', 'huggingface']),
            ];
        });
    }
}