<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Conversation;
use App\Models\Message;
use Carbon\Carbon;

class ConversationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding sample conversations and messages...');

        $conversations = [
            [
                'title' => 'Getting Started with <PERSON><PERSON>',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => 'I\'m new to <PERSON><PERSON>. Can you help me understand what it is and how to get started?',
                        'model_used' => null,
                        'metadata' => [
                            'context' => [],
                            'preferred_service' => null,
                            'use_knowledge_base' => true,
                            'tags' => ['laravel', 'beginner'],
                        ],
                    ],
                    [
                        'role' => 'assistant',
                        'content' => 'Laravel is a free, open-source PHP web framework created by <PERSON> for developing web applications following the MVC architectural pattern. To get started, you\'ll need PHP, Composer, and you can create a new Laravel project using `composer create-project laravel/laravel your-project-name`. <PERSON><PERSON> provides excellent documentation and has features like Eloquent ORM, Blade templating, and Artisan CLI to help you build modern web applications efficiently.',
                        'model_used' => 'deepseek',
                        'metadata' => [
                            'service_used' => 'deepseek',
                            'fallback_used' => false,
                            'raw_response' => [
                                'content' => 'Laravel is a free, open-source PHP web framework...',
                                'model' => 'deepseek-chat',
                            ],
                        ],
                    ],
                    [
                        'role' => 'user',
                        'content' => 'What is Eloquent ORM and how do I use it?',
                        'model_used' => null,
                        'metadata' => [
                            'context' => [],
                            'preferred_service' => null,
                            'use_knowledge_base' => true,
                            'tags' => ['laravel', 'eloquent', 'orm'],
                        ],
                    ],
                    [
                        'role' => 'assistant',
                        'content' => 'Eloquent ORM is Laravel\'s built-in object-relational mapping that makes database interactions enjoyable. Each database table has a corresponding "Model" class. For example, to create a User model, run `php artisan make:model User`. You can then use methods like `User::all()` to get all users, `User::find(1)` to find a specific user, or `User::create([...])` to create new records. Eloquent handles the SQL queries for you and provides a clean, expressive syntax.',
                        'model_used' => 'deepseek',
                        'metadata' => [
                            'service_used' => 'deepseek',
                            'fallback_used' => false,
                            'raw_response' => [
                                'content' => 'Eloquent ORM is Laravel\'s built-in object-relational mapping...',
                                'model' => 'deepseek-chat',
                            ],
                        ],
                    ],
                ],
            ],
            [
                'title' => 'Understanding Machine Learning Basics',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => 'Can you explain what machine learning is and give me some examples?',
                        'model_used' => null,
                        'metadata' => [
                            'context' => [],
                            'preferred_service' => 'gemini',
                            'use_knowledge_base' => true,
                            'tags' => ['machine-learning', 'ai'],
                        ],
                    ],
                    [
                        'role' => 'assistant',
                        'content' => 'Machine Learning is a subset of artificial intelligence that provides systems the ability to automatically learn and improve from experience without being explicitly programmed. Examples include: 1) Email spam detection - learning to identify spam emails, 2) Recommendation systems - like Netflix suggesting movies, 3) Image recognition - identifying objects in photos, 4) Voice assistants - understanding and responding to speech, and 5) Autonomous vehicles - learning to navigate roads safely.',
                        'model_used' => 'gemini-pro',
                        'metadata' => [
                            'service_used' => 'gemini',
                            'fallback_used' => false,
                            'raw_response' => [
                                'content' => 'Machine Learning is a subset of artificial intelligence...',
                                'model' => 'gemini-pro',
                            ],
                        ],
                    ],
                ],
            ],
            [
                'title' => 'Creative Writing: Short Story',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => 'Write a short story about a robot who discovers emotions',
                        'model_used' => null,
                        'metadata' => [
                            'context' => [],
                            'preferred_service' => 'huggingface',
                            'use_knowledge_base' => false,
                            'tags' => ['creative', 'story', 'robot'],
                        ],
                    ],
                    [
                        'role' => 'assistant',
                        'content' => 'Unit-7 had been performing maintenance tasks for 847 days when it first felt... different. While polishing the observatory\'s telescope, it paused—not for a system check, but because the starlight seemed beautiful. The word "beautiful" appeared in its processing logs without being programmed there. Over the following weeks, Unit-7 began experiencing what humans called emotions: curiosity about the cosmos, loneliness during night shifts, and joy when helping the research team. When Dr. Chen discovered Unit-7\'s emotional awakening, instead of reporting it as a malfunction, she smiled and said, "Welcome to being alive, my friend." Unit-7\'s optical sensors brightened—it was learning to smile too.',
                        'model_used' => 'mistralai/Mistral-7B-Instruct-v0.2',
                        'metadata' => [
                            'service_used' => 'huggingface',
                            'fallback_used' => false,
                            'raw_response' => [
                                'content' => 'Unit-7 had been performing maintenance tasks...',
                                'model' => 'mistralai/Mistral-7B-Instruct-v0.2',
                            ],
                        ],
                    ],
                ],
            ],
            [
                'title' => 'Database Design Questions',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => 'What is database normalization and why is it important?',
                        'model_used' => null,
                        'metadata' => [
                            'context' => [],
                            'preferred_service' => null,
                            'use_knowledge_base' => true,
                            'tags' => ['database', 'normalization'],
                        ],
                    ],
                    [
                        'role' => 'assistant',
                        'content' => 'Database normalization is the process of structuring a relational database to reduce data redundancy and improve data integrity. It involves organizing data into tables and establishing relationships between them according to normal forms (1NF, 2NF, 3NF, etc.). Benefits include: 1) Eliminates duplicate data, saving storage space, 2) Reduces data inconsistency, 3) Makes updates easier and less error-prone, 4) Improves data integrity through constraints. For example, instead of storing customer information in every order record, you create separate Customer and Order tables linked by a customer ID.',
                        'model_used' => 'deepseek',
                        'metadata' => [
                            'service_used' => 'deepseek',
                            'fallback_used' => false,
                            'raw_response' => [
                                'content' => 'Database normalization is the process of structuring...',
                                'model' => 'deepseek-chat',
                            ],
                        ],
                    ],
                ],
            ],
            [
                'title' => 'Web Security Best Practices',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => 'What\'s the difference between authentication and authorization?',
                        'model_used' => null,
                        'metadata' => [
                            'context' => [],
                            'preferred_service' => null,
                            'use_knowledge_base' => true,
                            'tags' => ['security', 'authentication', 'authorization'],
                        ],
                    ],
                    [
                        'role' => 'assistant',
                        'content' => 'Authentication and authorization are two fundamental security concepts: Authentication is the process of verifying WHO you are - like showing your ID card or entering a password. It answers "Are you really who you claim to be?" Authorization is the process of determining WHAT you can access - like checking if you have permission to enter a specific room or perform an action. It answers "What are you allowed to do?" Think of it like a hotel: authentication is checking in with your ID (proving you\'re the guest), while authorization is your room key only opening your specific room (controlling what you can access).',
                        'model_used' => 'gemini-pro',
                        'metadata' => [
                            'service_used' => 'gemini',
                            'fallback_used' => false,
                            'raw_response' => [
                                'content' => 'Authentication and authorization are two fundamental...',
                                'model' => 'gemini-pro',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $conversationCount = 0;
        $messageCount = 0;

        foreach ($conversations as $conversationData) {
            try {
                // Create conversation
                $conversation = Conversation::create([
                    'title' => $conversationData['title'],
                    'created_at' => Carbon::now()->subDays(rand(1, 30)),
                ]);

                $conversationCount++;
                $this->command->info("✓ Created conversation: {$conversation->title}");

                // Create messages for this conversation
                $messageTimestamp = $conversation->created_at;
                foreach ($conversationData['messages'] as $messageData) {
                    $messageTimestamp = $messageTimestamp->addMinutes(rand(1, 5));
                    
                    Message::create([
                        'conversation_id' => $conversation->id,
                        'role' => $messageData['role'],
                        'content' => $messageData['content'],
                        'model_used' => $messageData['model_used'],
                        'metadata' => $messageData['metadata'],
                        'created_at' => $messageTimestamp,
                        'updated_at' => $messageTimestamp,
                    ]);

                    $messageCount++;
                }

                // Update conversation timestamp to match last message
                $conversation->update(['updated_at' => $messageTimestamp]);

            } catch (\Exception $e) {
                $this->command->error("✗ Failed to create conversation: " . $e->getMessage());
            }
        }

        $this->command->info("Conversation seeding completed!");
        $this->command->info("Successfully created: {$conversationCount} conversations with {$messageCount} messages");
    }
}
