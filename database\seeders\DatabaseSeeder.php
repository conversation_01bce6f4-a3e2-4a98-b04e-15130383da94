<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting WIDDX AI Assistant database seeding...');

        // Seed knowledge base first (required for conversations that reference it)
        $this->call(KnowledgeBaseSeeder::class);

        // Seed sample conversations and messages
        $this->call(ConversationSeeder::class);

        // Create a test user if needed
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::factory()->create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
            ]);
            $this->command->info('✓ Created test user: <EMAIL>');
        }

        $this->command->info('🎉 Database seeding completed successfully!');
        $this->command->info('');
        $this->command->info('You can now:');
        $this->command->info('- Test the API endpoints with sample data');
        $this->command->info('- Explore conversations at /api/conversations');
        $this->command->info('- Query the knowledge base through /api/ask');
    }
}
