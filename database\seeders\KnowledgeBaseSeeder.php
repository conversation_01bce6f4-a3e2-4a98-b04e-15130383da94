<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\KnowledgeEntry;
use App\Services\KnowledgeBaseService;
use Illuminate\Support\Facades\Log;

class KnowledgeBaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding knowledge base entries...');

        $knowledgeService = new KnowledgeBaseService();

        $entries = [
            // Laravel Framework Knowledge
            [
                'content' => 'Laravel is a free, open-source PHP web framework, created by <PERSON> and intended for the development of web applications following the model–view–controller (MVC) architectural pattern.',
                'source' => 'laravel_docs',
                'tags' => ['laravel', 'php', 'framework', 'mvc'],
                'confidence_score' => 0.95,
            ],
            [
                'content' => 'Eloquent ORM is Laravel\'s built-in object-relational mapping (ORM) that makes it enjoyable to interact with your database. Each database table has a corresponding "Model" which is used to interact with that table.',
                'source' => 'laravel_docs',
                'tags' => ['laravel', 'eloquent', 'orm', 'database'],
                'confidence_score' => 0.92,
            ],
            [
                'content' => 'Laravel Artisan is the command-line interface included with <PERSON><PERSON>. It provides a number of helpful commands that can assist you while you build your application.',
                'source' => 'laravel_docs',
                'tags' => ['laravel', 'artisan', 'cli', 'commands'],
                'confidence_score' => 0.90,
            ],
            [
                'content' => 'Laravel Blade is a simple, yet powerful templating engine provided with Laravel. Unlike other popular PHP templating engines, Blade does not restrict you from using plain PHP code in your views.',
                'source' => 'laravel_docs',
                'tags' => ['laravel', 'blade', 'templating', 'views'],
                'confidence_score' => 0.88,
            ],
            [
                'content' => 'Laravel migrations are like version control for your database, allowing your team to define and share the application\'s database schema definition.',
                'source' => 'laravel_docs',
                'tags' => ['laravel', 'migrations', 'database', 'schema'],
                'confidence_score' => 0.91,
            ],

            // Programming Concepts
            [
                'content' => 'Object-Oriented Programming (OOP) is a programming paradigm based on the concept of "objects", which can contain data and code: data in the form of fields (often known as attributes or properties), and code, in the form of procedures (often known as methods).',
                'source' => 'programming_concepts',
                'tags' => ['oop', 'programming', 'concepts', 'objects'],
                'confidence_score' => 0.94,
            ],
            [
                'content' => 'REST (Representational State Transfer) is an architectural style for providing standards between computer systems on the web, making it easier for systems to communicate with each other.',
                'source' => 'programming_concepts',
                'tags' => ['rest', 'api', 'architecture', 'web'],
                'confidence_score' => 0.89,
            ],
            [
                'content' => 'JSON (JavaScript Object Notation) is a lightweight data-interchange format. It is easy for humans to read and write. It is easy for machines to parse and generate.',
                'source' => 'programming_concepts',
                'tags' => ['json', 'data', 'format', 'javascript'],
                'confidence_score' => 0.87,
            ],

            // AI and Machine Learning
            [
                'content' => 'Machine Learning is a subset of artificial intelligence (AI) that provides systems the ability to automatically learn and improve from experience without being explicitly programmed.',
                'source' => 'ai_concepts',
                'tags' => ['machine-learning', 'ai', 'artificial-intelligence', 'learning'],
                'confidence_score' => 0.93,
            ],
            [
                'content' => 'Natural Language Processing (NLP) is a branch of artificial intelligence that helps computers understand, interpret and manipulate human language.',
                'source' => 'ai_concepts',
                'tags' => ['nlp', 'natural-language', 'ai', 'text-processing'],
                'confidence_score' => 0.91,
            ],
            [
                'content' => 'Large Language Models (LLMs) are a type of artificial intelligence model designed to understand and generate human-like text based on the input they receive.',
                'source' => 'ai_concepts',
                'tags' => ['llm', 'language-model', 'ai', 'text-generation'],
                'confidence_score' => 0.90,
            ],

            // Database Concepts
            [
                'content' => 'SQL (Structured Query Language) is a domain-specific language used in programming and designed for managing data held in a relational database management system.',
                'source' => 'database_concepts',
                'tags' => ['sql', 'database', 'query', 'relational'],
                'confidence_score' => 0.92,
            ],
            [
                'content' => 'Database normalization is the process of structuring a relational database in accordance with a series of so-called normal forms in order to reduce data redundancy and improve data integrity.',
                'source' => 'database_concepts',
                'tags' => ['database', 'normalization', 'data-integrity', 'relational'],
                'confidence_score' => 0.88,
            ],
            [
                'content' => 'ACID (Atomicity, Consistency, Isolation, Durability) is a set of properties of database transactions intended to guarantee validity even in the event of errors, power failures, etc.',
                'source' => 'database_concepts',
                'tags' => ['acid', 'database', 'transactions', 'properties'],
                'confidence_score' => 0.89,
            ],

            // Web Development
            [
                'content' => 'HTTP (Hypertext Transfer Protocol) is an application-layer protocol for transmitting hypermedia documents, such as HTML. It was designed for communication between web browsers and web servers.',
                'source' => 'web_concepts',
                'tags' => ['http', 'protocol', 'web', 'browser'],
                'confidence_score' => 0.90,
            ],
            [
                'content' => 'HTTPS (HTTP Secure) is an extension of HTTP. It is used for secure communication over a computer network, and is widely used on the Internet.',
                'source' => 'web_concepts',
                'tags' => ['https', 'security', 'encryption', 'web'],
                'confidence_score' => 0.91,
            ],
            [
                'content' => 'CSS (Cascading Style Sheets) is a style sheet language used for describing the presentation of a document written in a markup language such as HTML.',
                'source' => 'web_concepts',
                'tags' => ['css', 'styling', 'web', 'html'],
                'confidence_score' => 0.86,
            ],

            // Software Engineering
            [
                'content' => 'Version control is a system that records changes to a file or set of files over time so that you can recall specific versions later. Git is the most popular version control system.',
                'source' => 'software_engineering',
                'tags' => ['version-control', 'git', 'software', 'development'],
                'confidence_score' => 0.93,
            ],
            [
                'content' => 'Test-Driven Development (TDD) is a software development process that relies on the repetition of a very short development cycle: requirements are turned into very specific test cases, then the code is improved so that the tests pass.',
                'source' => 'software_engineering',
                'tags' => ['tdd', 'testing', 'development', 'methodology'],
                'confidence_score' => 0.87,
            ],
            [
                'content' => 'Continuous Integration (CI) is a development practice where developers integrate code into a shared repository frequently, preferably several times a day.',
                'source' => 'software_engineering',
                'tags' => ['ci', 'continuous-integration', 'development', 'automation'],
                'confidence_score' => 0.89,
            ],

            // Security
            [
                'content' => 'Authentication is the process of verifying the identity of a user, device, or other entity in a computer system, often as a prerequisite to allowing access to resources in a system.',
                'source' => 'security_concepts',
                'tags' => ['authentication', 'security', 'identity', 'access'],
                'confidence_score' => 0.92,
            ],
            [
                'content' => 'Authorization is the function of specifying access rights/privileges to resources, which is related to general information security and computer security, and to access control in particular.',
                'source' => 'security_concepts',
                'tags' => ['authorization', 'security', 'access-control', 'permissions'],
                'confidence_score' => 0.90,
            ],
        ];

        $successCount = 0;
        $errorCount = 0;

        foreach ($entries as $entryData) {
            try {
                $knowledgeService->createEntry(
                    $entryData['content'],
                    $entryData['source'],
                    $entryData['tags'],
                    $entryData['confidence_score']
                );
                $successCount++;
                $this->command->info("✓ Created knowledge entry: " . substr($entryData['content'], 0, 50) . "...");
            } catch (\Exception $e) {
                $errorCount++;
                $this->command->error("✗ Failed to create knowledge entry: " . $e->getMessage());
                Log::error('KnowledgeBaseSeeder: Failed to create entry', [
                    'content' => substr($entryData['content'], 0, 100),
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $this->command->info("Knowledge base seeding completed!");
        $this->command->info("Successfully created: {$successCount} entries");
        
        if ($errorCount > 0) {
            $this->command->warn("Failed to create: {$errorCount} entries");
        }
    }
}
