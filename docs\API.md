# WIDDX AI API Documentation

## Overview

The WIDDX AI API provides endpoints for interacting with multiple AI services (DeepSeek, Gemini, HuggingFace) and managing a local knowledge base with self-learning capabilities.

## Base URL

```
http://localhost:8000/api
```

## Authentication

Currently, the API does not require authentication for basic usage. Future versions may include API key authentication.

## Rate Limiting

- General endpoints: 60 requests per minute
- Knowledge base endpoints: 50 requests per minute
- Ask endpoint: 30 requests per minute

## Core Endpoints

### Chat & Conversations

#### POST /ask

Send a message to the AI assistant.

**Request Body:**
```json
{
  "message": "What is <PERSON><PERSON>?",
  "conversation_id": 123,
  "use_knowledge_base": true,
  "knowledge_threshold": 0.7,
  "preferred_service": "deepseek",
  "tags": ["laravel", "php"],
  "context": []
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": {
      "id": 456,
      "conversation_id": 123,
      "role": "user",
      "content": "What is <PERSON><PERSON>?",
      "created_at": "2024-01-15T10:30:00Z"
    },
    "conversation": {
      "id": 123,
      "title": "Laravel Discussion",
      "created_at": "2024-01-15T10:30:00Z"
    },
    "response": {
      "content": "Laravel is a PHP web application framework...",
      "model_used": "deepseek-coder",
      "service_used": "deepseek",
      "fallback_used": false,
      "processing_time": 1.25,
      "local_response": false
    },
    "knowledge_base": {
      "used": true,
      "entries_found": 3,
      "local_response_used": false,
      "threshold_used": 0.7
    }
  },
  "timestamp": "2024-01-15T10:30:01Z"
}
```

#### GET /conversations

Get list of conversations.

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `per_page` (integer): Items per page (default: 20, max: 100)

**Response:**
```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "id": 123,
        "title": "Laravel Discussion",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:35:00Z",
        "message_count": 4
      }
    ],
    "pagination": {
      "current_page": 1,
      "last_page": 5,
      "per_page": 20,
      "total": 95
    }
  }
}
```

#### GET /conversations/{id}

Get a specific conversation with messages.

**Response:**
```json
{
  "success": true,
  "data": {
    "conversation": {
      "id": 123,
      "title": "Laravel Discussion",
      "created_at": "2024-01-15T10:30:00Z"
    },
    "messages": [
      {
        "id": 456,
        "role": "user",
        "content": "What is Laravel?",
        "created_at": "2024-01-15T10:30:00Z"
      },
      {
        "id": 457,
        "role": "assistant",
        "content": "Laravel is a PHP framework...",
        "model_used": "deepseek-coder",
        "created_at": "2024-01-15T10:30:01Z"
      }
    ]
  }
}
```

### Knowledge Base Management

#### GET /knowledge

Get paginated list of knowledge entries.

**Query Parameters:**
- `page` (integer): Page number
- `per_page` (integer): Items per page (max: 100)
- `sort` (string): Sort field (created_at, confidence_score, content)
- `order` (string): Sort order (asc, desc)
- `source` (string): Filter by source
- `tags` (array): Filter by tags
- `search` (string): Search in content

**Response:**
```json
{
  "success": true,
  "data": {
    "entries": [
      {
        "id": 789,
        "content": "Laravel is a PHP web application framework...",
        "source": "ai_response",
        "tags": ["laravel", "php", "framework"],
        "confidence_score": 0.85,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total": 150
    },
    "stats": {
      "total": 150,
      "high_confidence": 95,
      "recent": 12,
      "unique_tags": 45
    }
  }
}
```

#### POST /knowledge

Create a new knowledge entry.

**Request Body:**
```json
{
  "content": "Laravel Eloquent is an ORM that provides...",
  "source": "manual",
  "tags": ["laravel", "eloquent", "orm"],
  "confidence_score": 0.9,
  "metadata": {
    "author": "admin",
    "category": "documentation"
  }
}
```

#### GET /knowledge/search

Search knowledge entries.

**Query Parameters:**
- `q` (string, required): Search query
- `threshold` (float): Similarity threshold (0-1)
- `limit` (integer): Max results (max: 50)
- `tags` (array): Filter by tags
- `source` (string): Filter by source

**Response:**
```json
{
  "success": true,
  "data": {
    "query": "Laravel framework",
    "results": [
      {
        "id": 789,
        "content": "Laravel is a PHP framework...",
        "similarity_score": 0.92,
        "confidence_score": 0.85,
        "tags": ["laravel", "php"]
      }
    ],
    "count": 1,
    "threshold": 0.7
  }
}
```

#### PATCH /knowledge/{id}

Update a knowledge entry.

#### DELETE /knowledge/{id}

Delete a knowledge entry.

#### GET /knowledge/stats

Get knowledge base statistics.

## Error Responses

All endpoints return errors in this format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The request data is invalid.",
    "details": {
      "message": ["The message field is required."]
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Common Error Codes

- `VALIDATION_ERROR` (422): Request validation failed
- `RATE_LIMIT_EXCEEDED` (429): Too many requests
- `INTERNAL_ERROR` (500): Server error
- `SERVICE_UNAVAILABLE` (503): AI service temporarily unavailable
- `ENTRY_NOT_FOUND` (404): Knowledge entry not found

## Self-Learning Features

The system automatically saves high-quality AI responses to the knowledge base:

1. **Auto-Save Criteria:**
   - Content length > 10 characters
   - Confidence score > 0.6
   - Not a fallback response (configurable)

2. **Local Response Usage:**
   - When similarity score > 0.85, uses local knowledge instead of AI
   - Indicated by `local_response: true` in response

3. **Tag Generation:**
   - Automatically generates tags based on content and query
   - Includes service and model tags
   - Content-type tags (programming, explanation, tutorial)

## Configuration

Key configuration options in `config/ai.php`:

```php
'self_learning' => [
    'enabled' => true,
    'min_confidence_score' => 0.6,
    'local_response_threshold' => 0.85,
    'save_fallback_responses' => false,
],
```
