<?php

echo "🔧 WIDDX AI Project Comprehensive Fix\n";
echo "=====================================\n\n";

// Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "❌ Error: Please run this script from the Laravel project root directory.\n";
    exit(1);
}

echo "1. Clearing all caches...\n";
exec('php artisan config:clear', $output1, $return1);
exec('php artisan cache:clear', $output2, $return2);
exec('php artisan route:clear', $output3, $return3);
exec('php artisan view:clear', $output4, $return4);

if ($return1 === 0 && $return2 === 0 && $return3 === 0 && $return4 === 0) {
    echo "✅ All caches cleared successfully\n\n";
} else {
    echo "⚠️  Some cache clearing commands failed\n\n";
}

echo "2. Checking database connection...\n";
try {
    exec('php artisan tinker --execute="use Illuminate\Support\Facades\DB; echo DB::connection()->getPdo() ? \'OK\' : \'Failed\';"', $dbOutput, $dbReturn);
    if (strpos(implode('', $dbOutput), 'OK') !== false) {
        echo "✅ Database connection: OK\n\n";
    } else {
        echo "❌ Database connection: Failed\n\n";
    }
} catch (Exception $e) {
    echo "❌ Database connection test failed: " . $e->getMessage() . "\n\n";
}

echo "3. Checking migrations...\n";
exec('php artisan migrate:status', $migrateOutput, $migrateReturn);
if ($migrateReturn === 0) {
    echo "✅ Migrations status checked\n";
    foreach ($migrateOutput as $line) {
        if (strpos($line, 'Ran') !== false) {
            echo "  " . $line . "\n";
        }
    }
    echo "\n";
} else {
    echo "❌ Migration check failed\n\n";
}

echo "4. Testing API endpoints...\n";

// Test health endpoint
$healthUrl = 'http://127.0.0.1:8000/api/health';
$healthResponse = @file_get_contents($healthUrl);
if ($healthResponse) {
    echo "✅ Health endpoint: OK\n";
} else {
    echo "❌ Health endpoint: Failed\n";
}

// Test conversations endpoint
$conversationsUrl = 'http://127.0.0.1:8000/api/conversations';
$conversationsResponse = @file_get_contents($conversationsUrl);
if ($conversationsResponse) {
    echo "✅ Conversations endpoint: OK\n";
} else {
    echo "❌ Conversations endpoint: Failed\n";
}

// Test ask endpoint
$askUrl = 'http://127.0.0.1:8000/api/ask';
$askData = json_encode([
    'message' => 'Test message',
    'use_knowledge_base' => false,
    'preferred_service' => null,
    'knowledge_threshold' => 0.7,
    'max_knowledge_results' => 5,
    'tags' => [],
    'source_filter' => null,
    'context' => []
]);

$askContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($askData)
        ],
        'content' => $askData
    ]
]);

$askResponse = @file_get_contents($askUrl, false, $askContext);
if ($askResponse) {
    echo "✅ Ask endpoint: OK\n";
} else {
    echo "❌ Ask endpoint: Failed\n";
}

echo "\n5. Checking log files...\n";
$logFile = 'storage/logs/laravel.log';
if (file_exists($logFile)) {
    $logSize = filesize($logFile);
    echo "✅ Log file exists (Size: " . number_format($logSize) . " bytes)\n";
    
    // Check for recent errors
    $logContent = file_get_contents($logFile);
    $errorCount = substr_count($logContent, '.ERROR:');
    if ($errorCount > 0) {
        echo "⚠️  Found $errorCount errors in log file\n";
    } else {
        echo "✅ No errors found in log file\n";
    }
} else {
    echo "✅ No log file (clean start)\n";
}

echo "\n6. Environment check...\n";
if (file_exists('.env')) {
    echo "✅ .env file exists\n";
    
    $envContent = file_get_contents('.env');
    $requiredVars = ['DB_CONNECTION', 'DB_DATABASE', 'DEEPSEEK_API_KEY', 'GEMINI_API_KEY'];
    
    foreach ($requiredVars as $var) {
        if (strpos($envContent, $var . '=') !== false) {
            echo "✅ $var is set\n";
        } else {
            echo "❌ $var is missing\n";
        }
    }
} else {
    echo "❌ .env file missing\n";
}

echo "\n7. File permissions check...\n";
$writableDirs = ['storage', 'bootstrap/cache'];
foreach ($writableDirs as $dir) {
    if (is_writable($dir)) {
        echo "✅ $dir is writable\n";
    } else {
        echo "❌ $dir is not writable\n";
    }
}

echo "\n🎉 Project fix completed!\n";
echo "=============================\n";
echo "Next steps:\n";
echo "1. Open http://127.0.0.1:8000/test.html to test the interface\n";
echo "2. Open http://127.0.0.1:8000 to use the main application\n";
echo "3. Check the browser console for any JavaScript errors\n";
echo "4. If issues persist, check storage/logs/laravel.log for errors\n";
