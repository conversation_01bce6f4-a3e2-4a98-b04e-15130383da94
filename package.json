{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "tailwindcss": "^4.0.0", "vite": "^6.2.4"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/compiler-sfc": "^3.5.17", "lucide-vue-next": "^0.525.0", "pinia": "^2.3.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}}