/* WIDDX AI - Animations */

/* Keyframe Animations */
@keyframes widdxFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes widdxSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes widdxBounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes widdxTyping {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

@keyframes widdxGradient {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes widdxShimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes widdxPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes widdxSpin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes widdxSlideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes widdxSlideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes widdxScaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes widdxMessageAppear {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes widdxFloatUp {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

@keyframes widdxGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    }
}

@keyframes widdxRipple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Animation Classes */
.animate-widdx-fade-in {
    animation: widdxFadeIn 0.3s ease-out;
}

.animate-widdx-slide-up {
    animation: widdxSlideUp 0.3s ease-out;
}

.animate-widdx-bounce-in {
    animation: widdxBounceIn 0.5s ease-out;
}

.animate-widdx-typing {
    animation: widdxTyping 1.4s infinite;
}

.animate-widdx-gradient {
    background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #10b981, #f59e0b);
    background-size: 400% 400%;
    animation: widdxGradient 3s ease-in-out infinite;
}

.animate-widdx-pulse-slow {
    animation: widdxPulse 2s infinite;
}

.animate-widdx-spin {
    animation: widdxSpin 1s linear infinite;
}

.animate-widdx-slide-in-left {
    animation: widdxSlideInLeft 0.3s ease-out;
}

.animate-widdx-slide-in-right {
    animation: widdxSlideInRight 0.3s ease-out;
}

.animate-widdx-scale-in {
    animation: widdxScaleIn 0.2s ease-out;
}

.animate-widdx-message-appear {
    animation: widdxMessageAppear 0.3s ease-out;
}

.animate-widdx-float {
    animation: widdxFloatUp 3s ease-in-out infinite;
}

.animate-widdx-glow {
    animation: widdxGlow 2s ease-in-out infinite;
}

/* Hover Animations */
.widdx-hover-lift {
    transition: transform var(--widdx-transition-normal), box-shadow var(--widdx-transition-normal);
}

.widdx-hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--widdx-shadow-lg);
}

.widdx-hover-scale {
    transition: transform var(--widdx-transition-fast);
}

.widdx-hover-scale:hover {
    transform: scale(1.05);
}

.widdx-hover-rotate {
    transition: transform var(--widdx-transition-normal);
}

.widdx-hover-rotate:hover {
    transform: rotate(5deg);
}

.widdx-hover-glow {
    transition: box-shadow var(--widdx-transition-normal);
}

.widdx-hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

/* Loading Animations */
.widdx-loading-dots {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.widdx-loading-dots::after {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
    animation: widdxPulse 1.5s infinite;
}

.widdx-loading-dots::before {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
    animation: widdxPulse 1.5s infinite 0.5s;
}

.widdx-skeleton {
    background: linear-gradient(90deg, #1e293b 25%, #334155 50%, #1e293b 75%);
    background-size: 200% 100%;
    animation: widdxShimmer 1.5s infinite;
}

/* Ripple Effect */
.widdx-ripple {
    position: relative;
    overflow: hidden;
}

.widdx-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.widdx-ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Stagger Animations */
.widdx-stagger-children > * {
    animation: widdxFadeIn 0.3s ease-out;
}

.widdx-stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
.widdx-stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
.widdx-stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
.widdx-stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
.widdx-stagger-children > *:nth-child(5) { animation-delay: 0.5s; }

/* Entrance Animations */
.widdx-entrance-fade {
    opacity: 0;
    animation: widdxFadeIn 0.5s ease-out forwards;
}

.widdx-entrance-slide {
    opacity: 0;
    transform: translateY(30px);
    animation: widdxSlideUp 0.5s ease-out forwards;
}

.widdx-entrance-scale {
    opacity: 0;
    transform: scale(0.8);
    animation: widdxScaleIn 0.5s ease-out forwards;
}

/* Exit Animations */
.widdx-exit-fade {
    animation: widdxFadeIn 0.3s ease-in reverse;
}

.widdx-exit-slide {
    animation: widdxSlideUp 0.3s ease-in reverse;
}

.widdx-exit-scale {
    animation: widdxScaleIn 0.3s ease-in reverse;
}

/* Performance Optimizations */
.widdx-will-change {
    will-change: transform, opacity;
}

.widdx-gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .animate-widdx-fade-in,
    .animate-widdx-slide-up,
    .animate-widdx-bounce-in,
    .animate-widdx-typing,
    .animate-widdx-gradient,
    .animate-widdx-pulse-slow,
    .animate-widdx-spin,
    .animate-widdx-slide-in-left,
    .animate-widdx-slide-in-right,
    .animate-widdx-scale-in,
    .animate-widdx-message-appear,
    .animate-widdx-float,
    .animate-widdx-glow {
        animation: none !important;
    }
    
    .widdx-hover-lift:hover,
    .widdx-hover-scale:hover,
    .widdx-hover-rotate:hover {
        transform: none !important;
    }
}
