/* WIDDX AI - Components Styles */

/* Header Component */
.widdx-header {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.widdx-select {
    background: var(--widdx-glass);
    border: 1px solid var(--widdx-border);
    transition: all var(--widdx-transition-normal);
}

.widdx-select:focus {
    outline: none;
    ring: 2px solid var(--widdx-primary);
    border-color: var(--widdx-primary);
}

.widdx-btn-icon {
    transition: all var(--widdx-transition-normal);
}

.widdx-btn-icon:hover {
    transform: translateY(-1px);
    box-shadow: var(--widdx-shadow-md);
}

/* Sidebar Component */
.widdx-sidebar {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.widdx-btn-primary {
    background: linear-gradient(135deg, var(--widdx-primary) 0%, var(--widdx-primary-dark) 100%);
    transition: all var(--widdx-transition-normal);
    position: relative;
    overflow: hidden;
}

.widdx-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--widdx-transition-slow);
}

.widdx-btn-primary:hover::before {
    left: 100%;
}

.widdx-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--widdx-shadow-xl);
}

.widdx-input {
    background: var(--widdx-glass);
    border: 1px solid var(--widdx-border);
    transition: all var(--widdx-transition-normal);
}

.widdx-input:focus {
    outline: none;
    ring: 2px solid var(--widdx-primary);
    border-color: var(--widdx-primary);
    background: rgba(30, 41, 59, 0.8);
}

.widdx-filter-tab {
    color: #9ca3af;
    transition: all var(--widdx-transition-normal);
}

.widdx-filter-tab.active {
    background: var(--widdx-primary);
    color: white;
}

.widdx-filter-tab:hover:not(.active) {
    background: var(--widdx-glass-light);
    color: white;
}

.widdx-conversation-item {
    transition: all var(--widdx-transition-normal);
    position: relative;
}

.widdx-conversation-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--widdx-shadow-md);
}

.widdx-conversation-item.active {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
}

.widdx-conversation-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--widdx-primary);
    border-radius: 0 2px 2px 0;
}

/* Chat Area Component */
.widdx-feature-card {
    transition: all var(--widdx-transition-normal);
}

.widdx-feature-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--widdx-shadow-lg);
    border-color: rgba(255, 255, 255, 0.2);
}

.widdx-message-bubble {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--widdx-border);
    position: relative;
}

.widdx-message-container {
    transition: all var(--widdx-transition-normal);
}

.widdx-message-actions {
    opacity: 0;
    transform: translateY(10px);
    transition: all var(--widdx-transition-normal);
}

.widdx-message-container:hover .widdx-message-actions {
    opacity: 1;
    transform: translateY(0);
}

.widdx-action-btn {
    color: #9ca3af;
    transition: all var(--widdx-transition-fast);
}

.widdx-action-btn:hover {
    transform: scale(1.1);
}

.widdx-copy-btn.copied {
    background: rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.3);
    color: var(--widdx-success);
}

/* Input Area Component */
.widdx-input-area {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.widdx-quick-action-btn {
    transition: all var(--widdx-transition-normal);
    position: relative;
    overflow: hidden;
}

.widdx-quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left var(--widdx-transition-slow);
}

.widdx-quick-action-btn:hover::before {
    left: 100%;
}

.widdx-quick-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--widdx-shadow-md);
}

.widdx-textarea {
    background: var(--widdx-glass);
    border: 1px solid var(--widdx-border);
    transition: all var(--widdx-transition-normal);
    resize: none;
}

.widdx-textarea:focus {
    outline: none;
    ring: 2px solid var(--widdx-primary);
    border-color: var(--widdx-primary);
    background: rgba(30, 41, 59, 0.8);
}

.widdx-input-action-btn {
    transition: all var(--widdx-transition-fast);
}

.widdx-input-action-btn:hover {
    transform: scale(1.1);
}

.widdx-send-btn {
    transition: all var(--widdx-transition-normal);
    position: relative;
    overflow: hidden;
}

.widdx-send-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--widdx-transition-slow);
}

.widdx-send-btn:hover::before {
    left: 100%;
}

.widdx-send-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--widdx-shadow-xl);
}

.widdx-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Code Highlighting */
.widdx-code-block {
    background: var(--widdx-secondary) !important;
    border: 1px solid var(--widdx-border);
    border-radius: var(--widdx-radius-lg);
    position: relative;
}

.widdx-code-block::before {
    content: attr(data-language);
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.75rem;
    color: #9ca3af;
    text-transform: uppercase;
    font-weight: 500;
}

/* Typing Indicator */
.widdx-typing-dot {
    background-color: #64748b;
    animation: widdx-typing 1.4s infinite;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .widdx-sidebar {
        transform: translateX(-100%);
        transition: transform var(--widdx-transition-slow);
    }
    
    .widdx-sidebar.open {
        transform: translateX(0);
    }
}

@media (max-width: 640px) {
    .widdx-quick-action-btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }
    
    .widdx-message-bubble {
        padding: 1rem 1.25rem;
    }
    
    .widdx-textarea {
        padding: 0.75rem 1rem;
        padding-right: 3rem;
    }
    
    .widdx-send-btn {
        padding: 0.75rem;
        min-width: 48px;
    }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    .widdx-message-bubble {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: no-preference) {
    .widdx-message-container {
        animation: widdx-message-appear 0.3s ease-out;
    }
}

/* Accessibility */
@media (prefers-contrast: high) {
    .widdx-btn-primary {
        border: 2px solid var(--widdx-primary-light);
    }
    
    .widdx-message-bubble {
        border-width: 2px;
    }
}

/* Focus Visible */
.widdx-btn-primary:focus-visible,
.widdx-input:focus-visible,
.widdx-textarea:focus-visible {
    outline: 2px solid var(--widdx-primary);
    outline-offset: 2px;
}
