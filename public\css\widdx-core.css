/* WIDDX AI - Core Styles */

/* CSS Variables */
:root {
    --widdx-primary: #3b82f6;
    --widdx-primary-dark: #2563eb;
    --widdx-primary-light: #60a5fa;
    --widdx-secondary: #1e293b;
    --widdx-secondary-light: #334155;
    --widdx-accent: #8b5cf6;
    --widdx-accent-dark: #7c3aed;
    --widdx-success: #10b981;
    --widdx-warning: #f59e0b;
    --widdx-error: #ef4444;
    --widdx-dark: #0f172a;
    --widdx-dark-light: #1e293b;
    --widdx-glass: rgba(30, 41, 59, 0.7);
    --widdx-glass-light: rgba(30, 41, 59, 0.5);
    --widdx-border: rgba(255, 255, 255, 0.1);
    --widdx-border-light: rgba(255, 255, 255, 0.05);

    /* Typography */
    --widdx-font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    --widdx-font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Spacing */
    --widdx-spacing-xs: 0.25rem;
    --widdx-spacing-sm: 0.5rem;
    --widdx-spacing-md: 1rem;
    --widdx-spacing-lg: 1.5rem;
    --widdx-spacing-xl: 2rem;
    --widdx-spacing-2xl: 3rem;

    /* Border Radius */
    --widdx-radius-sm: 0.375rem;
    --widdx-radius-md: 0.5rem;
    --widdx-radius-lg: 0.75rem;
    --widdx-radius-xl: 1rem;
    --widdx-radius-2xl: 1.5rem;

    /* Shadows */
    --widdx-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --widdx-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --widdx-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --widdx-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --widdx-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Transitions */
    --widdx-transition-fast: 150ms ease-in-out;
    --widdx-transition-normal: 200ms ease-in-out;
    --widdx-transition-slow: 300ms ease-in-out;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--widdx-font-family);
    background: linear-gradient(135deg, var(--widdx-dark) 0%, var(--widdx-dark-light) 100%);
    color: white;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar Styles */
.widdx-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.widdx-scrollbar::-webkit-scrollbar-track {
    background: var(--widdx-secondary);
    border-radius: 3px;
}

.widdx-scrollbar::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 3px;
    transition: background var(--widdx-transition-fast);
}

.widdx-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* Glass Effect */
.glass {
    background: var(--widdx-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--widdx-border);
}

/* Improved Glass Effect */
.widdx-glass {
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Typography */
.widdx-text-gradient {
    background: linear-gradient(135deg, #ffffff 0%, #d1d5db 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.widdx-text-primary-gradient {
    background: linear-gradient(135deg, var(--widdx-primary) 0%, var(--widdx-accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Focus Styles */
.widdx-focus {
    outline: none;
    ring: 2px solid var(--widdx-primary);
    ring-opacity: 0.5;
    border-color: var(--widdx-primary);
}

/* Selection */
::selection {
    background: var(--widdx-primary);
    color: white;
}

::-moz-selection {
    background: var(--widdx-primary);
    color: white;
}

/* Loading States */
.widdx-loading {
    position: relative;
    overflow: hidden;
}

.widdx-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: widdx-shimmer 1.5s infinite;
}

/* Utilities */
.widdx-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.widdx-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.widdx-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.widdx-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Responsive Design */
@media (max-width: 640px) {
    :root {
        --widdx-spacing-md: 0.75rem;
        --widdx-spacing-lg: 1rem;
        --widdx-spacing-xl: 1.5rem;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --widdx-border: rgba(255, 255, 255, 0.3);
        --widdx-glass: rgba(30, 41, 59, 0.9);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    body {
        background: white !important;
        color: black !important;
    }

    .widdx-sidebar,
    .widdx-header,
    .widdx-input-area {
        display: none !important;
    }
}
