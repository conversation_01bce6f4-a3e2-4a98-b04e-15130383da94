<!DOCTYPE html>
<html>
<head>
    <title>WIDDX AI Debug</title>
    <style>
        body { font-family: Arial; padding: 20px; background: #1a1a1a; color: white; }
        .test { margin: 20px 0; padding: 15px; background: #2a2a2a; border-radius: 8px; }
        button { background: #3b82f6; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .result { background: #000; padding: 10px; margin: 10px 0; border-radius: 5px; white-space: pre-wrap; font-family: monospace; }
        .success { border-left: 4px solid #10b981; }
        .error { border-left: 4px solid #ef4444; }
    </style>
</head>
<body>
    <h1>🔧 WIDDX AI Debug Console</h1>
    
    <div class="test">
        <h3>1. Basic Connectivity Test</h3>
        <button onclick="testBasic()">Test Basic Connection</button>
        <div id="basic-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test">
        <h3>2. API Health Test</h3>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <div id="health-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test">
        <h3>3. Send Message Test</h3>
        <button onclick="testMessage()">Test Send Message</button>
        <div id="message-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test">
        <h3>4. Console Logs</h3>
        <button onclick="showLogs()">Show Console Logs</button>
        <div id="logs-result" class="result">Console logs will appear here...</div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const logs = [];
        
        console.log = function(...args) {
            logs.push({type: 'log', message: args.join(' '), time: new Date().toISOString()});
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logs.push({type: 'error', message: args.join(' '), time: new Date().toISOString()});
            originalError.apply(console, args);
        };
        
        async function testBasic() {
            const result = document.getElementById('basic-result');
            result.textContent = 'Testing basic connectivity...\n';
            result.className = 'result';
            
            try {
                const response = await fetch(window.location.origin);
                result.textContent += `Status: ${response.status}\n`;
                result.textContent += `Status Text: ${response.statusText}\n`;
                result.textContent += `URL: ${response.url}\n`;
                result.textContent += `Headers: ${JSON.stringify([...response.headers.entries()], null, 2)}\n`;
                
                if (response.ok) {
                    result.textContent += '\n✅ Basic connectivity: PASSED';
                    result.className = 'result success';
                } else {
                    result.textContent += '\n❌ Basic connectivity: FAILED';
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent += `\n❌ Error: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        async function testHealth() {
            const result = document.getElementById('health-result');
            result.textContent = 'Testing health endpoint...\n';
            result.className = 'result';
            
            try {
                const start = Date.now();
                const response = await fetch('/api/health');
                const responseTime = Date.now() - start;
                
                result.textContent += `Status: ${response.status}\n`;
                result.textContent += `Response Time: ${responseTime}ms\n`;
                
                if (response.ok) {
                    const data = await response.json();
                    result.textContent += `Data: ${JSON.stringify(data, null, 2)}\n`;
                    result.textContent += '\n✅ Health check: PASSED';
                    result.className = 'result success';
                } else {
                    const text = await response.text();
                    result.textContent += `Response: ${text}\n`;
                    result.textContent += '\n❌ Health check: FAILED';
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent += `\n❌ Error: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        async function testMessage() {
            const result = document.getElementById('message-result');
            result.textContent = 'Testing message endpoint...\n';
            result.className = 'result';
            
            try {
                const payload = {
                    message: 'Hello from debug console!',
                    preferred_service: 'auto',
                    use_knowledge_base: true
                };
                
                result.textContent += `Payload: ${JSON.stringify(payload, null, 2)}\n\n`;
                
                const start = Date.now();
                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(payload)
                });
                const responseTime = Date.now() - start;
                
                result.textContent += `Status: ${response.status}\n`;
                result.textContent += `Response Time: ${responseTime}ms\n`;
                result.textContent += `Headers: ${JSON.stringify([...response.headers.entries()], null, 2)}\n\n`;
                
                const text = await response.text();
                result.textContent += `Raw Response: ${text}\n\n`;
                
                try {
                    const data = JSON.parse(text);
                    result.textContent += `Parsed Data: ${JSON.stringify(data, null, 2)}\n`;
                    
                    if (response.ok && data.success) {
                        result.textContent += '\n✅ Message test: PASSED';
                        result.className = 'result success';
                    } else {
                        result.textContent += '\n❌ Message test: FAILED';
                        result.className = 'result error';
                    }
                } catch (parseError) {
                    result.textContent += `\n❌ JSON Parse Error: ${parseError.message}`;
                    result.className = 'result error';
                }
                
            } catch (error) {
                result.textContent += `\n❌ Network Error: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        function showLogs() {
            const result = document.getElementById('logs-result');
            
            if (logs.length === 0) {
                result.textContent = 'No console logs captured yet.';
                result.className = 'result';
            } else {
                result.textContent = logs.map(log => 
                    `[${log.time}] ${log.type.toUpperCase()}: ${log.message}`
                ).join('\n');
                result.className = 'result';
            }
        }
        
        // Auto-run basic tests on load
        window.onload = function() {
            console.log('Debug page loaded');
            setTimeout(() => {
                testBasic();
                setTimeout(() => {
                    testHealth();
                }, 1000);
            }, 500);
        };
        
        // Log any errors
        window.onerror = function(message, source, lineno, colno, error) {
            console.error(`Global Error: ${message} at ${source}:${lineno}:${colno}`, error);
        };
        
        // Log unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled Promise Rejection:', event.reason);
        });
    </script>
</body>
</html>
