/**
 * WIDDX AI - Frontend Fix Script
 * This script fixes common frontend issues and ensures proper integration
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 WIDDX AI Frontend Fix Script Loading...');
    
    // Fix 1: Ensure CSRF token is available
    fixCSRFToken();
    
    // Fix 2: Initialize API properly
    fixAPIInitialization();
    
    // Fix 3: Fix event listeners
    fixEventListeners();
    
    // Fix 4: Fix UI components
    fixUIComponents();
    
    // Fix 5: Fix chat functionality
    fixChatFunctionality();
    
    console.log('✅ WIDDX AI Frontend Fix Script Completed');
});

// Fix CSRF Token
function fixCSRFToken() {
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        window.csrfToken = csrfToken.getAttribute('content');
        console.log('✅ CSRF Token found and set');
    } else {
        console.warn('⚠️ CSRF Token not found');
    }
}

// Fix API Initialization
function fixAPIInitialization() {
    if (typeof WIDDX !== 'undefined' && WIDDX.API) {
        // Override sendMessage function with proper error handling
        WIDDX.API.sendMessage = async function(data) {
            try {
                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': window.csrfToken || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(data)
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.message || 'Request failed'}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        };
        
        console.log('✅ API sendMessage function fixed');
    }
}

// Fix Event Listeners
function fixEventListeners() {
    // Fix send button
    const sendButton = document.getElementById('widdx-send-btn');
    if (sendButton) {
        sendButton.addEventListener('click', handleSendMessage);
        console.log('✅ Send button event listener fixed');
    }
    
    // Fix message input
    const messageInput = document.getElementById('widdx-message-input');
    if (messageInput) {
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
            }
        });
        console.log('✅ Message input event listener fixed');
    }
    
    // Fix new chat button
    const newChatBtn = document.getElementById('widdx-new-chat-btn');
    if (newChatBtn) {
        newChatBtn.addEventListener('click', handleNewChat);
        console.log('✅ New chat button event listener fixed');
    }
}

// Fix UI Components
function fixUIComponents() {
    // Fix sidebar toggle
    const sidebarToggle = document.getElementById('widdx-sidebar-toggle');
    const sidebar = document.getElementById('widdx-sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('hidden');
            sidebar.classList.toggle('flex');
        });
        console.log('✅ Sidebar toggle fixed');
    }
    
    // Fix model selector
    const modelSelector = document.getElementById('widdx-model-selector');
    if (modelSelector) {
        modelSelector.addEventListener('change', function() {
            if (typeof WIDDX !== 'undefined' && WIDDX.state) {
                WIDDX.state.selectedModel = this.value;
                console.log('Model changed to:', this.value);
            }
        });
        console.log('✅ Model selector fixed');
    }
}

// Fix Chat Functionality
function fixChatFunctionality() {
    // Ensure messages container exists
    const messagesContainer = document.getElementById('widdx-messages-container');
    if (!messagesContainer) {
        console.warn('⚠️ Messages container not found');
        return;
    }
    
    // Load existing conversations
    loadConversations();
    
    console.log('✅ Chat functionality initialized');
}

// Handle Send Message
async function handleSendMessage() {
    const messageInput = document.getElementById('widdx-message-input');
    const sendButton = document.getElementById('widdx-send-btn');
    
    if (!messageInput || !messageInput.value.trim()) {
        return;
    }
    
    const message = messageInput.value.trim();
    
    try {
        // Disable input and button
        messageInput.disabled = true;
        if (sendButton) sendButton.disabled = true;
        
        // Add user message to UI
        addMessageToUI(message, 'user');
        
        // Clear input
        messageInput.value = '';
        
        // Show typing indicator
        showTypingIndicator();
        
        // Send to API
        const response = await fetch('/api/ask', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': window.csrfToken || '',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                message: message,
                preferred_service: getSelectedModel(),
                use_knowledge_base: true
            })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || `HTTP ${response.status}`);
        }
        
        const data = await response.json();
        
        // Hide typing indicator
        hideTypingIndicator();
        
        // Add AI response to UI
        if (data.success && data.data && data.data.message) {
            addMessageToUI(data.data.message.content, 'assistant', {
                model: data.data.model || data.data.service,
                service: data.data.service
            });
        } else {
            throw new Error('Invalid response format');
        }
        
        // Load conversations
        loadConversations();
        
    } catch (error) {
        console.error('Send message error:', error);
        hideTypingIndicator();
        showErrorMessage('Failed to send message: ' + error.message);
    } finally {
        // Re-enable input and button
        messageInput.disabled = false;
        if (sendButton) sendButton.disabled = false;
        messageInput.focus();
    }
}

// Handle New Chat
function handleNewChat() {
    const messagesContainer = document.getElementById('widdx-messages-container');
    if (messagesContainer) {
        messagesContainer.innerHTML = '';
        showWelcomeScreen();
    }
    
    // Reset conversation state
    if (typeof WIDDX !== 'undefined' && WIDDX.state) {
        WIDDX.state.currentConversationId = null;
    }
    
    console.log('New chat started');
}

// Add Message to UI
function addMessageToUI(content, role, metadata = {}) {
    const messagesContainer = document.getElementById('widdx-messages-container');
    if (!messagesContainer) return;
    
    // Hide welcome screen
    const welcomeScreen = document.getElementById('widdx-welcome-screen');
    if (welcomeScreen) {
        welcomeScreen.style.display = 'none';
    }
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role} animate-widdx-slide-up`;
    
    const isUser = role === 'user';
    const bgClass = isUser ? 'bg-blue-600/20 border-blue-500/30' : 'bg-gray-800/50 border-gray-600/30';
    const alignClass = isUser ? 'ml-auto' : 'mr-auto';
    
    messageDiv.innerHTML = `
        <div class="max-w-4xl ${alignClass} ${bgClass} backdrop-blur-sm border rounded-2xl p-4 shadow-lg">
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 rounded-full ${isUser ? 'bg-blue-600' : 'bg-gray-600'} flex items-center justify-center">
                        ${isUser ? '👤' : '🤖'}
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="text-sm text-gray-300 mb-1">
                        ${isUser ? 'You' : (metadata.model || 'AI Assistant')}
                    </div>
                    <div class="text-white prose prose-invert max-w-none">
                        ${formatMessage(content)}
                    </div>
                    ${metadata.service ? `<div class="text-xs text-gray-400 mt-2">Powered by ${metadata.service}</div>` : ''}
                </div>
            </div>
        </div>
    `;
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Format Message
function formatMessage(content) {
    // Simple markdown-like formatting
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code class="bg-gray-700 px-1 rounded">$1</code>')
        .replace(/\n/g, '<br>');
}

// Show/Hide Typing Indicator
function showTypingIndicator() {
    const messagesContainer = document.getElementById('widdx-messages-container');
    if (!messagesContainer) return;
    
    const typingDiv = document.createElement('div');
    typingDiv.id = 'typing-indicator';
    typingDiv.className = 'message assistant animate-widdx-slide-up';
    typingDiv.innerHTML = `
        <div class="max-w-4xl mr-auto bg-gray-800/50 border-gray-600/30 backdrop-blur-sm border rounded-2xl p-4 shadow-lg">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">🤖</div>
                <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
            </div>
        </div>
    `;
    
    messagesContainer.appendChild(typingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Show Error Message
function showErrorMessage(message) {
    const messagesContainer = document.getElementById('widdx-messages-container');
    if (!messagesContainer) return;
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'message error animate-widdx-slide-up';
    errorDiv.innerHTML = `
        <div class="max-w-4xl mx-auto bg-red-900/20 border-red-500/30 backdrop-blur-sm border rounded-2xl p-4 shadow-lg">
            <div class="flex items-center space-x-2 text-red-400">
                <span>⚠️</span>
                <span>${message}</span>
            </div>
        </div>
    `;
    
    messagesContainer.appendChild(errorDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    // Remove error message after 5 seconds
    setTimeout(() => {
        errorDiv.remove();
    }, 5000);
}

// Get Selected Model
function getSelectedModel() {
    const modelSelector = document.getElementById('widdx-model-selector');
    return modelSelector ? modelSelector.value : 'auto';
}

// Load Conversations
async function loadConversations() {
    try {
        const response = await fetch('/api/conversations');
        const data = await response.json();
        
        if (data.success && data.data) {
            updateConversationsList(data.data);
        }
    } catch (error) {
        console.error('Failed to load conversations:', error);
    }
}

// Update Conversations List
function updateConversationsList(conversations) {
    const conversationsList = document.getElementById('widdx-conversations-list');
    if (!conversationsList) return;
    
    conversationsList.innerHTML = '';
    
    conversations.forEach(conversation => {
        const conversationDiv = document.createElement('div');
        conversationDiv.className = 'conversation-item p-3 rounded-lg hover:bg-gray-800/50 cursor-pointer transition-colors';
        conversationDiv.innerHTML = `
            <div class="text-white text-sm font-medium truncate">${conversation.title}</div>
            <div class="text-gray-400 text-xs mt-1">${conversation.messages_count} messages</div>
        `;
        
        conversationDiv.addEventListener('click', () => {
            loadConversation(conversation.id);
        });
        
        conversationsList.appendChild(conversationDiv);
    });
}

// Load Conversation
async function loadConversation(conversationId) {
    try {
        const response = await fetch(`/api/conversations/${conversationId}`);
        const data = await response.json();
        
        if (data.success && data.data) {
            displayConversation(data.data);
        }
    } catch (error) {
        console.error('Failed to load conversation:', error);
    }
}

// Display Conversation
function displayConversation(conversation) {
    const messagesContainer = document.getElementById('widdx-messages-container');
    if (!messagesContainer) return;
    
    messagesContainer.innerHTML = '';
    
    conversation.messages.forEach(message => {
        addMessageToUI(message.content, message.role, {
            model: message.metadata?.model,
            service: message.metadata?.service
        });
    });
    
    // Update current conversation ID
    if (typeof WIDDX !== 'undefined' && WIDDX.state) {
        WIDDX.state.currentConversationId = conversation.id;
    }
}

// Show Welcome Screen
function showWelcomeScreen() {
    const messagesContainer = document.getElementById('widdx-messages-container');
    if (!messagesContainer) return;
    
    const welcomeScreen = document.getElementById('widdx-welcome-screen');
    if (welcomeScreen) {
        welcomeScreen.style.display = 'block';
    }
}
