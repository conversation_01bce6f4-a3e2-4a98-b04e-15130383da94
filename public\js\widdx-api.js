/**
 * WIDDX AI - API Module
 * Handles all API communications
 */

WIDDX.API = {
    // Request interceptors
    interceptors: {
        request: [],
        response: []
    },
    
    // Initialize API module
    init() {
        WIDDX.logger.log('Initializing API module...');
        this.setupCSRFToken();
        this.setupInterceptors();
    },
    
    // Setup CSRF token
    setupCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        if (token) {
            this.csrfToken = token.getAttribute('content');
        }
    },
    
    // Setup default interceptors
    setupInterceptors() {
        // Request interceptor for authentication
        this.addRequestInterceptor((config) => {
            if (this.csrfToken) {
                config.headers = config.headers || {};
                config.headers['X-CSRF-TOKEN'] = this.csrfToken;
            }
            return config;
        });
        
        // Response interceptor for error handling
        this.addResponseInterceptor(
            (response) => response,
            (error) => {
                WIDDX.logger.error('API Error:', error);
                
                // Handle specific error codes
                if (error.status === 401) {
                    WIDDX.events.emit('auth:unauthorized');
                } else if (error.status === 403) {
                    WIDDX.events.emit('auth:forbidden');
                } else if (error.status >= 500) {
                    WIDDX.events.emit('api:server-error', error);
                }
                
                return Promise.reject(error);
            }
        );
    },
    
    // Add request interceptor
    addRequestInterceptor(interceptor) {
        this.interceptors.request.push(interceptor);
    },
    
    // Add response interceptor
    addResponseInterceptor(onFulfilled, onRejected) {
        this.interceptors.response.push({ onFulfilled, onRejected });
    },
    
    // Base request method
    async request(url, options = {}) {
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            ...options
        };
        
        // Apply request interceptors
        let finalConfig = config;
        for (const interceptor of this.interceptors.request) {
            finalConfig = interceptor(finalConfig) || finalConfig;
        }
        
        try {
            const response = await fetch(`${WIDDX.config.apiBase}${url}`, finalConfig);
            
            // Apply response interceptors
            let finalResponse = response;
            for (const { onFulfilled } of this.interceptors.response) {
                if (onFulfilled) {
                    finalResponse = onFulfilled(finalResponse) || finalResponse;
                }
            }
            
            if (!response.ok) {
                const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
                error.status = response.status;
                error.response = response;
                
                // Apply error interceptors
                for (const { onRejected } of this.interceptors.response) {
                    if (onRejected) {
                        await onRejected(error);
                    }
                }
                
                throw error;
            }
            
            const data = await response.json();
            return data;
            
        } catch (error) {
            // Apply error interceptors
            for (const { onRejected } of this.interceptors.response) {
                if (onRejected) {
                    await onRejected(error);
                }
            }
            throw error;
        }
    },
    
    // GET request
    get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl);
    },
    
    // POST request
    post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    
    // PUT request
    put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    
    // DELETE request
    delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    },
    
    // Upload file
    async upload(url, file, onProgress = null) {
        const formData = new FormData();
        formData.append('file', file);
        
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            // Progress tracking
            if (onProgress) {
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        onProgress(percentComplete);
                    }
                });
            }
            
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        reject(new Error('Invalid JSON response'));
                    }
                } else {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            });
            
            xhr.addEventListener('error', () => {
                reject(new Error('Network error'));
            });
            
            xhr.open('POST', `${WIDDX.config.apiBase}${url}`);
            
            // Add CSRF token
            if (this.csrfToken) {
                xhr.setRequestHeader('X-CSRF-TOKEN', this.csrfToken);
            }
            
            xhr.send(formData);
        });
    },
    
    // Chat API methods
    chat: {
        // Send message
        async sendMessage(message, options = {}) {
            const payload = {
                message,
                conversation_id: WIDDX.state.currentConversationId,
                use_knowledge_base: true,
                preferred_service: WIDDX.state.selectedModel === 'auto' ? null : WIDDX.state.selectedModel,
                knowledge_threshold: 0.7,
                max_knowledge_results: 5,
                tags: [],
                source_filter: null,
                context: WIDDX.state.messageHistory.slice(-10),
                ...options
            };
            
            return WIDDX.API.post('/ask', payload);
        },
        
        // Get conversations
        async getConversations(params = {}) {
            return WIDDX.API.get('/conversations', params);
        },
        
        // Get specific conversation
        async getConversation(id) {
            return WIDDX.API.get(`/conversations/${id}`);
        },
        
        // Delete conversation
        async deleteConversation(id) {
            return WIDDX.API.delete(`/conversations/${id}`);
        },
        
        // Update conversation
        async updateConversation(id, data) {
            return WIDDX.API.put(`/conversations/${id}`, data);
        },
        
        // Regenerate response
        async regenerateResponse(messageId) {
            return WIDDX.API.post(`/messages/${messageId}/regenerate`);
        },
        
        // Rate message
        async rateMessage(messageId, rating) {
            return WIDDX.API.post(`/messages/${messageId}/rate`, { rating });
        }
    },
    
    // Knowledge base API methods
    knowledge: {
        // Search knowledge base
        async search(query, options = {}) {
            return WIDDX.API.post('/knowledge/search', {
                query,
                threshold: 0.7,
                max_results: 10,
                ...options
            });
        },
        
        // Add knowledge
        async add(data) {
            return WIDDX.API.post('/knowledge', data);
        },
        
        // Update knowledge
        async update(id, data) {
            return WIDDX.API.put(`/knowledge/${id}`, data);
        },
        
        // Delete knowledge
        async delete(id) {
            return WIDDX.API.delete(`/knowledge/${id}`);
        }
    },
    
    // File API methods
    files: {
        // Upload file
        async upload(file, onProgress = null) {
            return WIDDX.API.upload('/files/upload', file, onProgress);
        },
        
        // Get file info
        async getInfo(id) {
            return WIDDX.API.get(`/files/${id}`);
        },
        
        // Delete file
        async delete(id) {
            return WIDDX.API.delete(`/files/${id}`);
        }
    },
    
    // Settings API methods
    settings: {
        // Get user settings
        async get() {
            return WIDDX.API.get('/settings');
        },
        
        // Update user settings
        async update(settings) {
            return WIDDX.API.put('/settings', settings);
        }
    },
    
    // Health check
    async healthCheck() {
        try {
            const response = await this.get('/health');
            return response.status === 'ok';
        } catch (error) {
            return false;
        }
    },
    
    // Retry mechanism
    async retry(fn, maxRetries = 3, delay = 1000) {
        let lastError;
        
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;
                
                // Don't retry on client errors (4xx)
                if (error.status >= 400 && error.status < 500) {
                    throw error;
                }
                
                if (i < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
                }
            }
        }
        
        throw lastError;
    }
};

// Auto-retry for critical operations
WIDDX.API.sendMessageWithRetry = function(message, options = {}) {
    return this.retry(() => this.chat.sendMessage(message, options));
};

WIDDX.API.getConversationsWithRetry = function(params = {}) {
    return this.retry(() => this.chat.getConversations(params));
};
