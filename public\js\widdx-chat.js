/**
 * WIDDX AI - Chat Module
 * Handles chat functionality and message management
 */

WIDDX.Chat = {
    // Initialize chat module
    init() {
        WIDDX.logger.log('Initializing Chat module...');
        this.setupEventListeners();
        this.loadConversations();
    },
    
    // Setup event listeners
    setupEventListeners() {
        // Chat events
        WIDDX.events.on('chat:new', () => this.startNewChat());
        WIDDX.events.on('message:send', (message) => this.sendMessage(message));
        WIDDX.events.on('conversations:search', (query) => this.searchConversations(query));
        WIDDX.events.on('conversations:clear-all', () => this.clearAllConversations());
        WIDDX.events.on('files:selected', (files) => this.handleFileUpload(files));
        
        // Form submission
        if (WIDDX.UI.elements.messageForm) {
            WIDDX.UI.elements.messageForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const message = WIDDX.UI.elements.messageInput?.value.trim();
                if (message && !WIDDX.state.isLoading) {
                    WIDDX.events.emit('message:send', message);
                }
            });
        }
    },
    
    // Start new chat
    startNewChat() {
        WIDDX.logger.log('Starting new chat...');
        
        // Reset state
        WIDDX.state.currentConversationId = null;
        WIDDX.state.messageHistory = [];
        
        // Clear messages container
        this.clearMessages();
        
        // Show welcome screen
        this.showWelcomeScreen();
        
        // Update sidebar
        this.updateConversationsList();
        
        // Focus input
        if (WIDDX.UI.elements.messageInput) {
            WIDDX.UI.elements.messageInput.focus();
        }
        
        // Close mobile sidebar
        if (WIDDX.state.sidebarOpen) {
            WIDDX.UI.closeMobileSidebar();
        }
        
        WIDDX.events.emit('chat:new-started');
    },
    
    // Send message
    async sendMessage(message) {
        if (!message || WIDDX.state.isLoading) return;
        
        WIDDX.logger.log('Sending message:', message);
        
        try {
            // Set loading state
            this.setLoading(true);
            WIDDX.UI.hideError();
            
            // Add user message to UI
            const userMessageId = this.addMessage(message, 'user');
            WIDDX.state.messageHistory.push({
                role: 'user',
                content: message,
                id: userMessageId
            });
            
            // Clear input
            if (WIDDX.UI.elements.messageInput) {
                WIDDX.UI.elements.messageInput.value = '';
                WIDDX.UI.autoResizeTextarea();
                WIDDX.UI.updateCharCount();
                WIDDX.UI.updateSendButton();
            }
            
            // Show typing indicator
            this.showTypingIndicator();
            
            // Send to API
            const response = await WIDDX.API.sendMessageWithRetry(message);
            
            if (response.success) {
                // Hide typing indicator
                this.hideTypingIndicator();
                
                // Add AI response
                const assistantMessageId = this.addMessage(response.data.message.content, 'assistant', {
                    model: response.data.model || WIDDX.state.selectedModel,
                    timestamp: new Date().toISOString()
                });
                
                WIDDX.state.messageHistory.push({
                    role: 'assistant',
                    content: response.data.message.content,
                    id: assistantMessageId,
                    model: response.data.model
                });
                
                // Update conversation ID
                if (response.data.message.conversation_id) {
                    WIDDX.state.currentConversationId = response.data.message.conversation_id;
                }
                
                // Reload conversations
                this.loadConversations();
                
                // Scroll to bottom
                this.scrollToBottom();
                
                WIDDX.events.emit('message:sent', { message, response });
                
            } else {
                throw new Error(response.message || 'Failed to send message');
            }
            
        } catch (error) {
            WIDDX.logger.error('Error sending message:', error);
            this.hideTypingIndicator();
            WIDDX.UI.showError(error.message || 'Failed to send message. Please try again.');
            WIDDX.events.emit('message:error', error);
        } finally {
            this.setLoading(false);
        }
    },
    
    // Add message to UI
    addMessage(content, role, metadata = {}) {
        const messageId = WIDDX.utils.generateId();
        
        // Hide welcome screen if visible
        if (WIDDX.UI.elements.welcomeScreen && !WIDDX.UI.elements.welcomeScreen.classList.contains('hidden')) {
            WIDDX.UI.elements.welcomeScreen.style.display = 'none';
        }
        
        // Create message element
        const messageElement = this.createMessageElement(messageId, content, role, metadata);
        
        // Add to container
        if (WIDDX.UI.elements.messagesContainer) {
            WIDDX.UI.elements.messagesContainer.appendChild(messageElement);
        }
        
        // Highlight code blocks for AI messages
        if (role === 'assistant' && typeof hljs !== 'undefined') {
            setTimeout(() => {
                messageElement.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightElement(block);
                    block.classList.add('widdx-code-block');
                });
            }, 100);
        }
        
        // Scroll to bottom
        this.scrollToBottom();
        
        return messageId;
    },
    
    // Create message element
    createMessageElement(messageId, content, role, metadata = {}) {
        const template = WIDDX.UI.elements.messageTemplate;
        if (!template) {
            WIDDX.logger.error('Message template not found');
            return document.createElement('div');
        }
        
        const messageElement = template.content.cloneNode(true);
        const container = messageElement.querySelector('.widdx-message-container');
        
        if (!container) return messageElement;
        
        // Set message ID
        container.dataset.messageId = messageId;
        
        // Configure for user or assistant
        const isUser = role === 'user';
        const avatar = container.querySelector('.widdx-message-avatar');
        const bubble = container.querySelector('.widdx-message-bubble');
        const sender = container.querySelector('.widdx-message-sender');
        const model = container.querySelector('.widdx-message-model');
        const time = container.querySelector('.widdx-message-time');
        const messageContent = container.querySelector('.widdx-message-content');
        const actions = container.querySelector('.widdx-message-actions');
        const regenerateBtn = container.querySelector('.widdx-regenerate-btn');
        
        // Set alignment
        if (isUser) {
            container.classList.add('justify-end');
            container.querySelector('.flex').classList.add('flex-row-reverse', 'space-x-reverse');
        }
        
        // Set avatar
        if (avatar) {
            avatar.textContent = isUser ? 'U' : 'W';
            avatar.className = `widdx-message-avatar w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0 ${
                isUser ? 'bg-gradient-to-br from-widdx-primary to-blue-600' : 'bg-gradient-to-br from-widdx-primary to-widdx-accent'
            }`;
        }
        
        // Set bubble styling
        if (bubble) {
            bubble.className = `widdx-message-bubble rounded-2xl px-6 py-4 shadow-lg border border-white/10 ${
                isUser ? 'bg-gradient-to-br from-widdx-primary to-blue-600' : 'bg-widdx-glass'
            }`;
        }
        
        // Set sender
        if (sender) {
            sender.textContent = isUser ? 'You' : 'WIDDX AI';
            sender.className = `text-sm font-medium ${isUser ? 'text-white/90' : 'text-gray-300'}`;
        }
        
        // Set model badge
        if (model && metadata.model && !isUser) {
            model.textContent = WIDDX.config.models[metadata.model]?.name || metadata.model;
            model.classList.remove('hidden');
        }
        
        // Set timestamp
        if (time) {
            time.textContent = new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
            time.className = `text-xs ${isUser ? 'text-white/70' : 'text-gray-400'}`;
        }
        
        // Set content
        if (messageContent) {
            if (isUser) {
                messageContent.textContent = content;
            } else {
                // Parse markdown for AI responses
                if (typeof marked !== 'undefined') {
                    messageContent.innerHTML = marked.parse(content);
                } else {
                    messageContent.textContent = content;
                }
            }
            messageContent.className = `widdx-message-content ${isUser ? 'text-white' : 'text-gray-100'} leading-relaxed`;
        }
        
        // Set actions alignment
        if (actions) {
            actions.className = `widdx-message-actions flex items-center space-x-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${
                isUser ? 'justify-end' : 'justify-start'
            }`;
        }
        
        // Show/hide regenerate button
        if (regenerateBtn) {
            if (isUser) {
                regenerateBtn.remove();
            } else {
                regenerateBtn.classList.remove('hidden');
            }
        }
        
        // Setup action buttons
        this.setupMessageActions(container, messageId);
        
        return messageElement;
    },
    
    // Setup message action buttons
    setupMessageActions(container, messageId) {
        const copyBtn = container.querySelector('.widdx-copy-btn');
        const regenerateBtn = container.querySelector('.widdx-regenerate-btn');
        const likeBtn = container.querySelector('.widdx-like-btn');
        const dislikeBtn = container.querySelector('.widdx-dislike-btn');
        
        if (copyBtn) {
            copyBtn.addEventListener('click', () => this.copyMessage(messageId));
        }
        
        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => this.regenerateMessage(messageId));
        }
        
        if (likeBtn) {
            likeBtn.addEventListener('click', () => this.rateMessage(messageId, 'like'));
        }
        
        if (dislikeBtn) {
            dislikeBtn.addEventListener('click', () => this.rateMessage(messageId, 'dislike'));
        }
    },
    
    // Copy message content
    async copyMessage(messageId) {
        const container = document.querySelector(`[data-message-id="${messageId}"]`);
        if (!container) return;
        
        const content = container.querySelector('.widdx-message-content');
        if (!content) return;
        
        const text = content.textContent || content.innerText;
        const success = await WIDDX.utils.copyToClipboard(text);
        
        if (success) {
            const copyBtn = container.querySelector('.widdx-copy-btn');
            if (copyBtn) {
                copyBtn.classList.add('copied');
                setTimeout(() => {
                    copyBtn.classList.remove('copied');
                }, 2000);
            }
            WIDDX.UI.showToast('Message copied to clipboard', 'success');
        } else {
            WIDDX.UI.showToast('Failed to copy message', 'error');
        }
    },
    
    // Regenerate message
    async regenerateMessage(messageId) {
        // Implementation for regenerating AI response
        WIDDX.logger.log('Regenerating message:', messageId);
        WIDDX.UI.showToast('Regenerate feature coming soon!', 'info');
    },
    
    // Rate message
    async rateMessage(messageId, rating) {
        try {
            // Find the message in history
            const message = WIDDX.state.messageHistory.find(msg => msg.id === messageId);
            if (!message) return;
            
            // Send rating to API (if implemented)
            // await WIDDX.API.chat.rateMessage(messageId, rating);
            
            WIDDX.UI.showToast(`Message ${rating}d`, 'success');
            WIDDX.logger.log(`Message ${messageId} rated as ${rating}`);
            
        } catch (error) {
            WIDDX.logger.error('Error rating message:', error);
            WIDDX.UI.showToast('Failed to rate message', 'error');
        }
    },
    
    // Show typing indicator
    showTypingIndicator() {
        if (WIDDX.UI.elements.typingIndicator) {
            WIDDX.UI.elements.typingIndicator.classList.remove('hidden');
            this.scrollToBottom();
        }
    },
    
    // Hide typing indicator
    hideTypingIndicator() {
        if (WIDDX.UI.elements.typingIndicator) {
            WIDDX.UI.elements.typingIndicator.classList.add('hidden');
        }
    },
    
    // Set loading state
    setLoading(loading) {
        WIDDX.state.isLoading = loading;
        
        if (WIDDX.UI.elements.sendBtn) {
            WIDDX.UI.elements.sendBtn.disabled = loading;
        }
        
        if (WIDDX.UI.elements.messageInput) {
            WIDDX.UI.elements.messageInput.disabled = loading;
        }
        
        if (WIDDX.UI.elements.sendIcon && WIDDX.UI.elements.sendSpinner) {
            if (loading) {
                WIDDX.UI.elements.sendIcon.classList.add('hidden');
                WIDDX.UI.elements.sendSpinner.classList.remove('hidden');
            } else {
                WIDDX.UI.elements.sendIcon.classList.remove('hidden');
                WIDDX.UI.elements.sendSpinner.classList.add('hidden');
            }
        }
        
        WIDDX.events.emit('loading:changed', loading);
    },
    
    // Clear messages
    clearMessages() {
        if (WIDDX.UI.elements.messagesContainer) {
            WIDDX.UI.elements.messagesContainer.innerHTML = '';
        }
    },
    
    // Show welcome screen
    showWelcomeScreen() {
        if (WIDDX.UI.elements.welcomeScreen) {
            WIDDX.UI.elements.welcomeScreen.style.display = 'block';
        }
    },
    
    // Scroll to bottom
    scrollToBottom() {
        if (WIDDX.UI.elements.messagesContainer) {
            setTimeout(() => {
                WIDDX.UI.elements.messagesContainer.scrollTo({
                    top: WIDDX.UI.elements.messagesContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }, 100);
        }
    },
    
    // Load conversations
    async loadConversations() {
        try {
            const response = await WIDDX.API.getConversationsWithRetry();
            if (response.success) {
                this.displayConversations(response.data);
            }
        } catch (error) {
            WIDDX.logger.error('Error loading conversations:', error);
        }
    },
    
    // Display conversations in sidebar
    displayConversations(conversations) {
        if (!WIDDX.UI.elements.conversationsList) return;
        
        // Update conversation count
        if (WIDDX.UI.elements.conversationCount) {
            WIDDX.UI.elements.conversationCount.textContent = `${conversations.length} conversation${conversations.length !== 1 ? 's' : ''}`;
        }
        
        // Show/hide empty state
        if (WIDDX.UI.elements.emptyState) {
            WIDDX.UI.elements.emptyState.style.display = conversations.length === 0 ? 'block' : 'none';
        }
        
        // Clear existing conversations
        const existingConversations = WIDDX.UI.elements.conversationsList.querySelectorAll('.widdx-conversation-item');
        existingConversations.forEach(item => item.remove());
        
        // Add conversations
        conversations.forEach(conversation => {
            const element = this.createConversationElement(conversation);
            WIDDX.UI.elements.conversationsList.appendChild(element);
        });
    },
    
    // Create conversation element
    createConversationElement(conversation) {
        const template = WIDDX.UI.elements.conversationTemplate;
        if (!template) {
            return document.createElement('div');
        }
        
        const element = template.content.cloneNode(true);
        const container = element.querySelector('.widdx-conversation-item');
        
        if (!container) return element;
        
        // Set data
        container.dataset.conversationId = conversation.id;
        
        // Set content
        const title = container.querySelector('.widdx-conversation-title');
        const preview = container.querySelector('.widdx-conversation-preview');
        const messageCount = container.querySelector('.widdx-message-count');
        const time = container.querySelector('.widdx-conversation-time');
        const activeIndicator = container.querySelector('.widdx-active-indicator');
        
        if (title) title.textContent = conversation.title;
        if (preview) preview.textContent = conversation.preview || 'No messages yet';
        if (messageCount) messageCount.textContent = conversation.messages_count || 0;
        if (time) time.textContent = WIDDX.utils.timeAgo(new Date(conversation.created_at));
        
        // Set active state
        if (conversation.id === WIDDX.state.currentConversationId) {
            container.classList.add('active', 'bg-widdx-primary/20', 'border-widdx-primary/30');
            if (activeIndicator) activeIndicator.classList.remove('hidden');
        }
        
        // Add click handler
        container.addEventListener('click', () => {
            this.loadConversation(conversation.id);
        });
        
        return element;
    },
    
    // Load specific conversation
    async loadConversation(conversationId) {
        try {
            const response = await WIDDX.API.chat.getConversation(conversationId);
            if (response.success) {
                WIDDX.state.currentConversationId = conversationId;
                this.displayMessages(response.data.messages);
                this.updateConversationsList();
            }
        } catch (error) {
            WIDDX.logger.error('Error loading conversation:', error);
            WIDDX.UI.showError('Failed to load conversation');
        }
    },
    
    // Display messages from conversation
    displayMessages(messages) {
        this.clearMessages();
        
        messages.forEach(message => {
            this.addMessage(message.content, message.role, {
                model: message.model,
                timestamp: message.created_at
            });
        });
    },
    
    // Update conversations list active state
    updateConversationsList() {
        const items = document.querySelectorAll('.widdx-conversation-item');
        items.forEach(item => {
            const isActive = item.dataset.conversationId === WIDDX.state.currentConversationId;
            const activeIndicator = item.querySelector('.widdx-active-indicator');
            
            item.classList.toggle('active', isActive);
            item.classList.toggle('bg-widdx-primary/20', isActive);
            item.classList.toggle('border-widdx-primary/30', isActive);
            
            if (activeIndicator) {
                activeIndicator.classList.toggle('hidden', !isActive);
            }
        });
    },
    
    // Search conversations
    searchConversations(query) {
        const items = document.querySelectorAll('.widdx-conversation-item');
        const lowerQuery = query.toLowerCase();
        
        items.forEach(item => {
            const title = item.querySelector('.widdx-conversation-title')?.textContent.toLowerCase() || '';
            const preview = item.querySelector('.widdx-conversation-preview')?.textContent.toLowerCase() || '';
            
            const matches = title.includes(lowerQuery) || preview.includes(lowerQuery);
            item.style.display = matches ? 'block' : 'none';
        });
    },
    
    // Clear all conversations
    async clearAllConversations() {
        try {
            // Implementation would depend on API
            WIDDX.UI.showToast('Clear all feature coming soon!', 'info');
        } catch (error) {
            WIDDX.logger.error('Error clearing conversations:', error);
            WIDDX.UI.showError('Failed to clear conversations');
        }
    },
    
    // Handle file upload
    async handleFileUpload(files) {
        WIDDX.logger.log('Handling file upload:', files);
        
        for (const file of files) {
            try {
                WIDDX.UI.showToast(`Uploading ${file.name}...`, 'info');
                
                const response = await WIDDX.API.files.upload(file, (progress) => {
                    // Update progress if needed
                    WIDDX.logger.log(`Upload progress: ${progress}%`);
                });
                
                if (response.success) {
                    WIDDX.UI.showToast(`${file.name} uploaded successfully`, 'success');
                    // Handle uploaded file
                } else {
                    throw new Error(response.message || 'Upload failed');
                }
                
            } catch (error) {
                WIDDX.logger.error('File upload error:', error);
                WIDDX.UI.showError(`Failed to upload ${file.name}: ${error.message}`);
            }
        }
    }
};
