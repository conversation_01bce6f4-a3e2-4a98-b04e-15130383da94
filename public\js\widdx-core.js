/**
 * WIDDX AI - Core JavaScript
 * Advanced Intelligent Assistant
 */

// Global WIDDX namespace
window.WIDDX = window.WIDDX || {};

// Core configuration
WIDDX.config = {
    apiBase: '/api',
    version: '1.0.0',
    debug: false,
    maxMessageLength: 4000,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    supportedFileTypes: ['image/*', '.pdf', '.doc', '.docx', '.txt', '.json', '.csv'],
    models: {
        auto: { name: 'Auto Select', icon: '🤖' },
        deepseek: { name: 'DeepSeek', icon: '🧠' },
        gemini: { name: '<PERSON>', icon: '💎' },
        huggingface: { name: 'Hugging<PERSON><PERSON>', icon: '🤗' }
    },
    animations: {
        fast: 150,
        normal: 200,
        slow: 300
    }
};

// Core state management
WIDDX.state = {
    currentConversationId: null,
    isLoading: false,
    messageHistory: [],
    selectedModel: 'auto',
    theme: 'dark',
    sidebarOpen: false,
    typingTimeout: null,
    user: {
        id: null,
        name: 'User',
        avatar: null
    }
};

// Event system
WIDDX.events = {
    listeners: {},

    on(event, callback) {
        if (!this.listeners[event]) {
            this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
    },

    off(event, callback) {
        if (!this.listeners[event]) return;
        const index = this.listeners[event].indexOf(callback);
        if (index > -1) {
            this.listeners[event].splice(index, 1);
        }
    },

    emit(event, data) {
        if (!this.listeners[event]) return;
        this.listeners[event].forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`Error in event listener for ${event}:`, error);
            }
        });
    }
};

// Utility functions
WIDDX.utils = {
    // Generate unique ID
    generateId() {
        return 'widdx_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Format time ago
    timeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    },

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // Escape HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // Copy to clipboard
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                return true;
            } catch (err) {
                console.error('Failed to copy text:', err);
                return false;
            } finally {
                document.body.removeChild(textArea);
            }
        }
    },

    // Check if element is in viewport
    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    },

    // Smooth scroll to element
    scrollToElement(element, offset = 0) {
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    },

    // Local storage helpers
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(`widdx_${key}`, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Failed to save to localStorage:', error);
                return false;
            }
        },

        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(`widdx_${key}`);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('Failed to read from localStorage:', error);
                return defaultValue;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(`widdx_${key}`);
                return true;
            } catch (error) {
                console.error('Failed to remove from localStorage:', error);
                return false;
            }
        }
    }
};

// Logger
WIDDX.logger = {
    log(message, ...args) {
        if (WIDDX.config.debug) {
            console.log(`[WIDDX] ${message}`, ...args);
        }
    },

    warn(message, ...args) {
        console.warn(`[WIDDX] ${message}`, ...args);
    },

    error(message, ...args) {
        console.error(`[WIDDX] ${message}`, ...args);
    }
};

// Performance monitoring
WIDDX.performance = {
    marks: {},

    mark(name) {
        this.marks[name] = performance.now();
    },

    measure(name, startMark) {
        if (!this.marks[startMark]) {
            WIDDX.logger.warn(`Start mark "${startMark}" not found`);
            return;
        }

        const duration = performance.now() - this.marks[startMark];
        WIDDX.logger.log(`${name}: ${duration.toFixed(2)}ms`);
        return duration;
    }
};

// Initialize core functionality
WIDDX.init = function() {
    WIDDX.logger.log('Initializing WIDDX AI...');
    WIDDX.performance.mark('init_start');

    // Load saved state
    this.loadState();

    // Initialize modules
    this.initializeModules();

    // Setup global event listeners
    this.setupGlobalEvents();

    // Hide loading screen
    this.hideLoadingScreen();

    WIDDX.performance.measure('Initialization', 'init_start');
    WIDDX.logger.log('WIDDX AI initialized successfully');

    // Emit ready event
    WIDDX.events.emit('ready');
};

// Load saved state from localStorage
WIDDX.loadState = function() {
    const savedState = WIDDX.utils.storage.get('state', {});
    Object.assign(WIDDX.state, savedState);
};

// Save state to localStorage
WIDDX.saveState = function() {
    WIDDX.utils.storage.set('state', WIDDX.state);
};

// Initialize all modules
WIDDX.initializeModules = function() {
    // Initialize in order of dependency
    if (WIDDX.UI) WIDDX.UI.init();
    if (WIDDX.API) WIDDX.API.init();
    if (WIDDX.Chat) WIDDX.Chat.init();
};

// Setup global event listeners
WIDDX.setupGlobalEvents = function() {
    // Save state on page unload
    window.addEventListener('beforeunload', () => {
        WIDDX.saveState();
    });

    // Handle visibility change
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            WIDDX.events.emit('app:hidden');
        } else {
            WIDDX.events.emit('app:visible');
        }
    });

    // Handle online/offline status
    window.addEventListener('online', () => {
        WIDDX.events.emit('connection:online');
    });

    window.addEventListener('offline', () => {
        WIDDX.events.emit('connection:offline');
    });

    // Handle keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + / for new chat
        if ((e.ctrlKey || e.metaKey) && e.key === '/') {
            e.preventDefault();
            WIDDX.events.emit('shortcut:new-chat');
        }

        // Escape to close modals/sidebar
        if (e.key === 'Escape') {
            WIDDX.events.emit('shortcut:escape');
        }

        // Shift + ? for help
        if (e.shiftKey && e.key === '?') {
            e.preventDefault();
            WIDDX.events.emit('shortcut:help');
        }
    });
};

// Hide loading screen
WIDDX.hideLoadingScreen = function() {
    const loadingScreen = document.getElementById('widdx-loading');
    const app = document.getElementById('widdx-app');

    if (loadingScreen && app) {
        setTimeout(() => {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                app.classList.remove('hidden');
                app.style.opacity = '0';
                setTimeout(() => {
                    app.style.opacity = '1';
                    app.style.transition = 'opacity 0.3s ease-in-out';
                }, 50);
            }, 300);
        }, 500); // Reduced loading time
    } else {
        // Fallback if elements don't exist
        if (app) {
            app.classList.remove('hidden');
            app.style.opacity = '1';
        }
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WIDDX;
}
