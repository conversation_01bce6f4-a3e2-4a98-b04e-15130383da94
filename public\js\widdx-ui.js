/**
 * WIDDX AI - UI Module
 * Handles all user interface interactions
 */

WIDDX.UI = {
    // DOM elements cache
    elements: {},
    
    // Initialize UI module
    init() {
        WIDDX.logger.log('Initializing UI module...');
        this.cacheElements();
        this.setupEventListeners();
        this.initializeComponents();
        this.setupKeyboardShortcuts();
    },
    
    // <PERSON>ache frequently used DOM elements
    cacheElements() {
        this.elements = {
            // Header elements
            modelSelector: document.getElementById('widdx-model-selector'),
            themeToggle: document.getElementById('widdx-theme-toggle'),
            settingsBtn: document.getElementById('widdx-settings-btn'),
            mobileMenu: document.getElementById('widdx-mobile-menu'),
            mobileNav: document.getElementById('widdx-mobile-nav'),
            
            // Sidebar elements
            sidebar: document.getElementById('widdx-sidebar'),
            newChatBtn: document.getElementById('widdx-new-chat-btn'),
            searchInput: document.getElementById('widdx-search-input'),
            conversationsList: document.getElementById('widdx-conversations-list'),
            emptyState: document.getElementById('widdx-empty-state'),
            conversationCount: document.getElementById('widdx-conversation-count'),
            clearAllBtn: document.getElementById('widdx-clear-all'),
            
            // Mobile sidebar
            sidebarOverlay: document.getElementById('widdx-sidebar-overlay'),
            mobileSidebar: document.getElementById('widdx-mobile-sidebar'),
            closeMobileSidebar: document.getElementById('widdx-close-mobile-sidebar'),
            
            // Chat area elements
            chatArea: document.getElementById('widdx-chat-area'),
            messagesContainer: document.getElementById('widdx-messages-container'),
            welcomeScreen: document.getElementById('widdx-welcome-screen'),
            typingIndicator: document.getElementById('widdx-typing-indicator'),
            
            // Input area elements
            messageForm: document.getElementById('widdx-message-form'),
            messageInput: document.getElementById('widdx-message-input'),
            sendBtn: document.getElementById('widdx-send-btn'),
            sendIcon: document.getElementById('widdx-send-icon'),
            sendSpinner: document.getElementById('widdx-send-spinner'),
            attachBtn: document.getElementById('widdx-attach-btn'),
            voiceBtn: document.getElementById('widdx-voice-btn'),
            fileInput: document.getElementById('widdx-file-input'),
            charCount: document.getElementById('widdx-char-count'),
            wordCount: document.getElementById('widdx-word-count'),
            
            // Message elements
            errorMessage: document.getElementById('widdx-error-message'),
            errorText: document.getElementById('widdx-error-text'),
            successMessage: document.getElementById('widdx-success-message'),
            successText: document.getElementById('widdx-success-text'),
            
            // Templates
            messageTemplate: document.getElementById('widdx-message-template'),
            conversationTemplate: document.getElementById('widdx-conversation-template'),
            
            // Toast container
            toastContainer: document.getElementById('widdx-toast-container')
        };
    },
    
    // Setup event listeners
    setupEventListeners() {
        // Header events
        if (this.elements.modelSelector) {
            this.elements.modelSelector.addEventListener('change', (e) => {
                WIDDX.state.selectedModel = e.target.value;
                WIDDX.events.emit('model:changed', e.target.value);
            });
        }
        
        if (this.elements.themeToggle) {
            this.elements.themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
        
        if (this.elements.settingsBtn) {
            this.elements.settingsBtn.addEventListener('click', () => {
                WIDDX.events.emit('settings:open');
            });
        }
        
        if (this.elements.mobileMenu) {
            this.elements.mobileMenu.addEventListener('click', () => {
                this.toggleMobileSidebar();
            });
        }
        
        // Sidebar events
        if (this.elements.newChatBtn) {
            this.elements.newChatBtn.addEventListener('click', () => {
                WIDDX.events.emit('chat:new');
            });
        }
        
        if (this.elements.searchInput) {
            this.elements.searchInput.addEventListener('input', 
                WIDDX.utils.debounce((e) => {
                    this.searchConversations(e.target.value);
                }, 300)
            );
        }
        
        if (this.elements.clearAllBtn) {
            this.elements.clearAllBtn.addEventListener('click', () => {
                this.confirmClearAll();
            });
        }
        
        // Mobile sidebar events
        if (this.elements.sidebarOverlay) {
            this.elements.sidebarOverlay.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }
        
        if (this.elements.closeMobileSidebar) {
            this.elements.closeMobileSidebar.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }
        
        // Input events
        if (this.elements.messageInput) {
            this.elements.messageInput.addEventListener('input', () => {
                this.handleInputChange();
            });
            
            this.elements.messageInput.addEventListener('keydown', (e) => {
                this.handleKeyDown(e);
            });
        }
        
        if (this.elements.attachBtn) {
            this.elements.attachBtn.addEventListener('click', () => {
                this.elements.fileInput?.click();
            });
        }
        
        if (this.elements.fileInput) {
            this.elements.fileInput.addEventListener('change', (e) => {
                this.handleFileSelect(e);
            });
        }
        
        if (this.elements.voiceBtn) {
            this.elements.voiceBtn.addEventListener('click', () => {
                this.toggleVoiceInput();
            });
        }
        
        // Quick action buttons
        document.querySelectorAll('.widdx-quick-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleQuickAction(e.currentTarget.dataset.action);
            });
        });
        
        // Global events
        WIDDX.events.on('shortcut:new-chat', () => {
            WIDDX.events.emit('chat:new');
        });
        
        WIDDX.events.on('shortcut:escape', () => {
            this.handleEscape();
        });
        
        WIDDX.events.on('shortcut:help', () => {
            this.showKeyboardShortcuts();
        });
    },
    
    // Initialize components
    initializeComponents() {
        // Initialize markdown renderer
        if (typeof marked !== 'undefined') {
            marked.setOptions({
                highlight: function(code, lang) {
                    if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                        return hljs.highlight(code, { language: lang }).value;
                    }
                    return typeof hljs !== 'undefined' ? hljs.highlightAuto(code).value : code;
                },
                breaks: true,
                gfm: true
            });
        }
        
        // Initialize syntax highlighting
        if (typeof hljs !== 'undefined') {
            hljs.configure({
                classPrefix: 'hljs-',
                languages: ['javascript', 'python', 'java', 'cpp', 'html', 'css', 'sql', 'json', 'xml']
            });
        }
        
        // Set initial model
        if (this.elements.modelSelector) {
            this.elements.modelSelector.value = WIDDX.state.selectedModel;
        }
        
        // Auto-resize textarea
        this.autoResizeTextarea();
    },
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts() {
        // Already handled in core.js global events
    },
    
    // Handle input changes
    handleInputChange() {
        this.autoResizeTextarea();
        this.updateCharCount();
        this.updateSendButton();
        
        // Clear typing timeout
        if (WIDDX.state.typingTimeout) {
            clearTimeout(WIDDX.state.typingTimeout);
        }
        
        // Set new typing timeout
        WIDDX.state.typingTimeout = setTimeout(() => {
            // User stopped typing
        }, 1000);
    },
    
    // Handle key down events
    handleKeyDown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (!WIDDX.state.isLoading && this.elements.messageInput.value.trim()) {
                WIDDX.events.emit('message:send', this.elements.messageInput.value.trim());
            }
        }
    },
    
    // Auto-resize textarea
    autoResizeTextarea() {
        if (!this.elements.messageInput) return;
        
        this.elements.messageInput.style.height = 'auto';
        const newHeight = Math.min(
            Math.max(this.elements.messageInput.scrollHeight, 56),
            200
        );
        this.elements.messageInput.style.height = newHeight + 'px';
        
        // Adjust send button height
        if (this.elements.sendBtn) {
            this.elements.sendBtn.style.height = newHeight + 'px';
        }
    },
    
    // Update character count
    updateCharCount() {
        if (!this.elements.charCount || !this.elements.messageInput) return;
        
        const count = this.elements.messageInput.value.length;
        const maxLength = WIDDX.config.maxMessageLength;
        
        this.elements.charCount.textContent = `${count} / ${maxLength}`;
        
        // Update styling based on count
        this.elements.charCount.classList.remove('text-yellow-400', 'text-red-400');
        if (count > maxLength * 0.8) {
            this.elements.charCount.classList.add('text-yellow-400');
        }
        if (count > maxLength * 0.95) {
            this.elements.charCount.classList.add('text-red-400');
        }
        
        // Update word count if element exists
        if (this.elements.wordCount) {
            const words = this.elements.messageInput.value.trim().split(/\s+/).filter(word => word.length > 0);
            this.elements.wordCount.textContent = `${words.length} words`;
        }
    },
    
    // Update send button state
    updateSendButton() {
        if (!this.elements.sendBtn || !this.elements.messageInput) return;
        
        const hasText = this.elements.messageInput.value.trim().length > 0;
        const isNotLoading = !WIDDX.state.isLoading;
        
        this.elements.sendBtn.disabled = !hasText || !isNotLoading;
    },
    
    // Handle quick actions
    handleQuickAction(action) {
        const prompts = {
            explain: 'Please explain this in simple terms: ',
            code: 'Write code for: ',
            translate: 'Translate this to English: ',
            summarize: 'Summarize this: ',
            analyze: 'Analyze this: '
        };
        
        if (prompts[action] && this.elements.messageInput) {
            this.elements.messageInput.value = prompts[action];
            this.elements.messageInput.focus();
            this.autoResizeTextarea();
            this.updateCharCount();
            this.updateSendButton();
        }
    },
    
    // Handle file selection
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        if (files.length === 0) return;
        
        // Validate files
        const validFiles = files.filter(file => {
            if (file.size > WIDDX.config.maxFileSize) {
                this.showError(`File "${file.name}" is too large. Maximum size is ${WIDDX.utils.formatFileSize(WIDDX.config.maxFileSize)}.`);
                return false;
            }
            return true;
        });
        
        if (validFiles.length > 0) {
            WIDDX.events.emit('files:selected', validFiles);
        }
        
        // Clear input
        e.target.value = '';
    },
    
    // Toggle voice input
    toggleVoiceInput() {
        // Voice input implementation would go here
        this.showToast('Voice input coming soon!', 'info');
    },
    
    // Toggle theme
    toggleTheme() {
        WIDDX.state.theme = WIDDX.state.theme === 'dark' ? 'light' : 'dark';
        document.documentElement.classList.toggle('light', WIDDX.state.theme === 'light');
        WIDDX.events.emit('theme:changed', WIDDX.state.theme);
    },
    
    // Toggle mobile sidebar
    toggleMobileSidebar() {
        WIDDX.state.sidebarOpen = !WIDDX.state.sidebarOpen;
        
        if (WIDDX.state.sidebarOpen) {
            this.openMobileSidebar();
        } else {
            this.closeMobileSidebar();
        }
    },
    
    // Open mobile sidebar
    openMobileSidebar() {
        if (this.elements.sidebarOverlay) {
            this.elements.sidebarOverlay.classList.remove('hidden');
        }
        if (this.elements.mobileSidebar) {
            this.elements.mobileSidebar.classList.remove('-translate-x-full');
        }
        WIDDX.state.sidebarOpen = true;
    },
    
    // Close mobile sidebar
    closeMobileSidebar() {
        if (this.elements.sidebarOverlay) {
            this.elements.sidebarOverlay.classList.add('hidden');
        }
        if (this.elements.mobileSidebar) {
            this.elements.mobileSidebar.classList.add('-translate-x-full');
        }
        WIDDX.state.sidebarOpen = false;
    },
    
    // Handle escape key
    handleEscape() {
        if (WIDDX.state.sidebarOpen) {
            this.closeMobileSidebar();
        }
        
        // Close any open modals
        WIDDX.events.emit('modal:close');
        
        // Blur input
        if (this.elements.messageInput) {
            this.elements.messageInput.blur();
        }
    },
    
    // Search conversations
    searchConversations(query) {
        WIDDX.events.emit('conversations:search', query);
    },
    
    // Confirm clear all conversations
    confirmClearAll() {
        if (confirm('Are you sure you want to clear all conversations? This action cannot be undone.')) {
            WIDDX.events.emit('conversations:clear-all');
        }
    },
    
    // Show keyboard shortcuts
    showKeyboardShortcuts() {
        const shortcuts = [
            { key: 'Enter', description: 'Send message' },
            { key: 'Shift + Enter', description: 'New line' },
            { key: 'Ctrl/Cmd + /', description: 'New chat' },
            { key: 'Escape', description: 'Close sidebar/modals' },
            { key: 'Shift + ?', description: 'Show shortcuts' }
        ];
        
        let message = 'Keyboard Shortcuts:\n\n';
        shortcuts.forEach(shortcut => {
            message += `${shortcut.key}: ${shortcut.description}\n`;
        });
        
        alert(message);
    },
    
    // Show error message
    showError(message, duration = 8000) {
        if (this.elements.errorMessage && this.elements.errorText) {
            this.elements.errorText.textContent = message;
            this.elements.errorMessage.classList.remove('hidden');
            
            if (duration > 0) {
                setTimeout(() => this.hideError(), duration);
            }
        }
    },
    
    // Hide error message
    hideError() {
        if (this.elements.errorMessage) {
            this.elements.errorMessage.classList.add('hidden');
        }
    },
    
    // Show success message
    showSuccess(message, duration = 4000) {
        if (this.elements.successMessage && this.elements.successText) {
            this.elements.successText.textContent = message;
            this.elements.successMessage.classList.remove('hidden');
            
            if (duration > 0) {
                setTimeout(() => this.hideSuccess(), duration);
            }
        }
    },
    
    // Hide success message
    hideSuccess() {
        if (this.elements.successMessage) {
            this.elements.successMessage.classList.add('hidden');
        }
    },
    
    // Show toast notification
    showToast(message, type = 'info', duration = 4000) {
        if (!this.elements.toastContainer) return;
        
        const toast = document.createElement('div');
        toast.className = `widdx-toast bg-widdx-glass border border-white/10 rounded-xl p-4 mb-2 animate-widdx-slide-in-right`;
        
        const colors = {
            info: 'border-blue-500/30 text-blue-300',
            success: 'border-green-500/30 text-green-300',
            warning: 'border-yellow-500/30 text-yellow-300',
            error: 'border-red-500/30 text-red-300'
        };
        
        toast.classList.add(...colors[type].split(' '));
        toast.textContent = message;
        
        this.elements.toastContainer.appendChild(toast);
        
        // Auto remove
        setTimeout(() => {
            toast.classList.add('animate-widdx-fade-out');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }
};
