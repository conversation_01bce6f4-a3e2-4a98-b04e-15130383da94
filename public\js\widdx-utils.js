/**
 * WIDDX AI - Utilities Module
 * Additional utility functions and helpers
 */

// Extend WIDDX.utils with additional utilities
Object.assign(WIDDX.utils, {
    
    // DOM utilities
    dom: {
        // Create element with attributes and children
        create(tag, attributes = {}, children = []) {
            const element = document.createElement(tag);
            
            // Set attributes
            Object.entries(attributes).forEach(([key, value]) => {
                if (key === 'className') {
                    element.className = value;
                } else if (key === 'innerHTML') {
                    element.innerHTML = value;
                } else if (key === 'textContent') {
                    element.textContent = value;
                } else if (key.startsWith('data-')) {
                    element.setAttribute(key, value);
                } else {
                    element[key] = value;
                }
            });
            
            // Add children
            children.forEach(child => {
                if (typeof child === 'string') {
                    element.appendChild(document.createTextNode(child));
                } else if (child instanceof Node) {
                    element.appendChild(child);
                }
            });
            
            return element;
        },
        
        // Find element with error handling
        find(selector, context = document) {
            try {
                return context.querySelector(selector);
            } catch (error) {
                WIDDX.logger.error(`Invalid selector: ${selector}`, error);
                return null;
            }
        },
        
        // Find all elements with error handling
        findAll(selector, context = document) {
            try {
                return Array.from(context.querySelectorAll(selector));
            } catch (error) {
                WIDDX.logger.error(`Invalid selector: ${selector}`, error);
                return [];
            }
        },
        
        // Add event listener with cleanup tracking
        on(element, event, handler, options = {}) {
            if (!element || typeof handler !== 'function') return null;
            
            element.addEventListener(event, handler, options);
            
            // Return cleanup function
            return () => {
                element.removeEventListener(event, handler, options);
            };
        },
        
        // Remove all children from element
        empty(element) {
            if (!element) return;
            while (element.firstChild) {
                element.removeChild(element.firstChild);
            }
        },
        
        // Check if element has class
        hasClass(element, className) {
            return element && element.classList && element.classList.contains(className);
        },
        
        // Toggle class on element
        toggleClass(element, className, force) {
            if (!element || !element.classList) return;
            return element.classList.toggle(className, force);
        },
        
        // Get element dimensions
        getDimensions(element) {
            if (!element) return { width: 0, height: 0 };
            const rect = element.getBoundingClientRect();
            return {
                width: rect.width,
                height: rect.height,
                top: rect.top,
                left: rect.left,
                right: rect.right,
                bottom: rect.bottom
            };
        }
    },
    
    // String utilities
    string: {
        // Capitalize first letter
        capitalize(str) {
            if (!str) return '';
            return str.charAt(0).toUpperCase() + str.slice(1);
        },
        
        // Convert to title case
        titleCase(str) {
            if (!str) return '';
            return str.replace(/\w\S*/g, (txt) => 
                txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
            );
        },
        
        // Truncate string with ellipsis
        truncate(str, length = 100, suffix = '...') {
            if (!str || str.length <= length) return str;
            return str.substring(0, length - suffix.length) + suffix;
        },
        
        // Remove HTML tags
        stripHtml(str) {
            if (!str) return '';
            return str.replace(/<[^>]*>/g, '');
        },
        
        // Convert string to slug
        slugify(str) {
            if (!str) return '';
            return str
                .toLowerCase()
                .trim()
                .replace(/[^\w\s-]/g, '')
                .replace(/[\s_-]+/g, '-')
                .replace(/^-+|-+$/g, '');
        },
        
        // Count words in string
        wordCount(str) {
            if (!str) return 0;
            return str.trim().split(/\s+/).filter(word => word.length > 0).length;
        },
        
        // Extract URLs from string
        extractUrls(str) {
            if (!str) return [];
            const urlRegex = /(https?:\/\/[^\s]+)/g;
            return str.match(urlRegex) || [];
        },
        
        // Highlight search terms
        highlight(str, term, className = 'highlight') {
            if (!str || !term) return str;
            const regex = new RegExp(`(${term})`, 'gi');
            return str.replace(regex, `<span class="${className}">$1</span>`);
        }
    },
    
    // Array utilities
    array: {
        // Remove duplicates from array
        unique(arr) {
            return [...new Set(arr)];
        },
        
        // Chunk array into smaller arrays
        chunk(arr, size) {
            const chunks = [];
            for (let i = 0; i < arr.length; i += size) {
                chunks.push(arr.slice(i, i + size));
            }
            return chunks;
        },
        
        // Shuffle array
        shuffle(arr) {
            const shuffled = [...arr];
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            return shuffled;
        },
        
        // Group array by key
        groupBy(arr, key) {
            return arr.reduce((groups, item) => {
                const group = typeof key === 'function' ? key(item) : item[key];
                groups[group] = groups[group] || [];
                groups[group].push(item);
                return groups;
            }, {});
        },
        
        // Sort array by multiple keys
        sortBy(arr, ...keys) {
            return arr.sort((a, b) => {
                for (const key of keys) {
                    const aVal = typeof key === 'function' ? key(a) : a[key];
                    const bVal = typeof key === 'function' ? key(b) : b[key];
                    
                    if (aVal < bVal) return -1;
                    if (aVal > bVal) return 1;
                }
                return 0;
            });
        }
    },
    
    // Object utilities
    object: {
        // Deep clone object
        deepClone(obj) {
            if (obj === null || typeof obj !== 'object') return obj;
            if (obj instanceof Date) return new Date(obj.getTime());
            if (obj instanceof Array) return obj.map(item => this.deepClone(item));
            if (typeof obj === 'object') {
                const cloned = {};
                Object.keys(obj).forEach(key => {
                    cloned[key] = this.deepClone(obj[key]);
                });
                return cloned;
            }
        },
        
        // Deep merge objects
        deepMerge(target, ...sources) {
            if (!sources.length) return target;
            const source = sources.shift();
            
            if (this.isObject(target) && this.isObject(source)) {
                for (const key in source) {
                    if (this.isObject(source[key])) {
                        if (!target[key]) Object.assign(target, { [key]: {} });
                        this.deepMerge(target[key], source[key]);
                    } else {
                        Object.assign(target, { [key]: source[key] });
                    }
                }
            }
            
            return this.deepMerge(target, ...sources);
        },
        
        // Check if value is object
        isObject(item) {
            return item && typeof item === 'object' && !Array.isArray(item);
        },
        
        // Get nested property safely
        get(obj, path, defaultValue = undefined) {
            const keys = path.split('.');
            let result = obj;
            
            for (const key of keys) {
                if (result == null || typeof result !== 'object') {
                    return defaultValue;
                }
                result = result[key];
            }
            
            return result !== undefined ? result : defaultValue;
        },
        
        // Set nested property
        set(obj, path, value) {
            const keys = path.split('.');
            const lastKey = keys.pop();
            let current = obj;
            
            for (const key of keys) {
                if (!(key in current) || typeof current[key] !== 'object') {
                    current[key] = {};
                }
                current = current[key];
            }
            
            current[lastKey] = value;
            return obj;
        },
        
        // Pick specific keys from object
        pick(obj, keys) {
            const result = {};
            keys.forEach(key => {
                if (key in obj) {
                    result[key] = obj[key];
                }
            });
            return result;
        },
        
        // Omit specific keys from object
        omit(obj, keys) {
            const result = { ...obj };
            keys.forEach(key => {
                delete result[key];
            });
            return result;
        }
    },
    
    // Date utilities
    date: {
        // Format date
        format(date, format = 'YYYY-MM-DD') {
            if (!date) return '';
            const d = new Date(date);
            
            const formats = {
                'YYYY': d.getFullYear(),
                'MM': String(d.getMonth() + 1).padStart(2, '0'),
                'DD': String(d.getDate()).padStart(2, '0'),
                'HH': String(d.getHours()).padStart(2, '0'),
                'mm': String(d.getMinutes()).padStart(2, '0'),
                'ss': String(d.getSeconds()).padStart(2, '0')
            };
            
            return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => formats[match]);
        },
        
        // Get relative time
        relative(date) {
            if (!date) return '';
            return WIDDX.utils.timeAgo(new Date(date));
        },
        
        // Check if date is today
        isToday(date) {
            if (!date) return false;
            const today = new Date();
            const checkDate = new Date(date);
            return checkDate.toDateString() === today.toDateString();
        },
        
        // Check if date is this week
        isThisWeek(date) {
            if (!date) return false;
            const now = new Date();
            const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
            const weekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 6));
            const checkDate = new Date(date);
            return checkDate >= weekStart && checkDate <= weekEnd;
        }
    },
    
    // Validation utilities
    validate: {
        // Validate email
        email(email) {
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },
        
        // Validate URL
        url(url) {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        },
        
        // Validate phone number (basic)
        phone(phone) {
            const regex = /^[\+]?[1-9][\d]{0,15}$/;
            return regex.test(phone.replace(/\s/g, ''));
        },
        
        // Check if string is empty or whitespace
        isEmpty(str) {
            return !str || str.trim().length === 0;
        },
        
        // Check if value is numeric
        isNumeric(value) {
            return !isNaN(value) && !isNaN(parseFloat(value));
        }
    },
    
    // Color utilities
    color: {
        // Convert hex to RGB
        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        },
        
        // Convert RGB to hex
        rgbToHex(r, g, b) {
            return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
        },
        
        // Generate random color
        random() {
            return '#' + Math.floor(Math.random() * 16777215).toString(16);
        },
        
        // Check if color is light
        isLight(hex) {
            const rgb = this.hexToRgb(hex);
            if (!rgb) return false;
            const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
            return brightness > 128;
        }
    },
    
    // Animation utilities
    animation: {
        // Animate element with CSS classes
        animate(element, animation, duration = 300) {
            return new Promise((resolve) => {
                if (!element) {
                    resolve();
                    return;
                }
                
                const animationClass = `animate-${animation}`;
                element.classList.add(animationClass);
                
                const cleanup = () => {
                    element.classList.remove(animationClass);
                    resolve();
                };
                
                setTimeout(cleanup, duration);
            });
        },
        
        // Fade in element
        fadeIn(element, duration = 300) {
            return this.animate(element, 'widdx-fade-in', duration);
        },
        
        // Fade out element
        fadeOut(element, duration = 300) {
            return this.animate(element, 'widdx-fade-out', duration);
        },
        
        // Slide up element
        slideUp(element, duration = 300) {
            return this.animate(element, 'widdx-slide-up', duration);
        },
        
        // Slide down element
        slideDown(element, duration = 300) {
            return this.animate(element, 'widdx-slide-down', duration);
        }
    },
    
    // Device detection
    device: {
        // Check if mobile device
        isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        },
        
        // Check if tablet
        isTablet() {
            return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);
        },
        
        // Check if desktop
        isDesktop() {
            return !this.isMobile() && !this.isTablet();
        },
        
        // Check if touch device
        isTouch() {
            return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        },
        
        // Get screen size category
        getScreenSize() {
            const width = window.innerWidth;
            if (width < 640) return 'sm';
            if (width < 768) return 'md';
            if (width < 1024) return 'lg';
            if (width < 1280) return 'xl';
            return '2xl';
        }
    }
});

// Export utilities for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WIDDX.utils;
}
