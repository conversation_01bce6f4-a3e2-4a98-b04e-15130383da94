<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX AI - Multi-Language Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(12px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-card {
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 25px;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        .test-card h3 {
            margin: 0 0 15px 0;
            color: #3b82f6;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2rem;
        }
        .language-flag {
            font-size: 1.5rem;
        }
        .test-message {
            background: rgba(15, 23, 42, 0.6);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #3b82f6;
            font-family: 'Arial', sans-serif;
        }
        .test-message.arabic {
            direction: rtl;
            text-align: right;
            font-family: 'Tahoma', 'Arial Unicode MS', sans-serif;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            margin: 5px;
            width: 100%;
        }
        .btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        .btn:disabled {
            background: #4b5563;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .result.success {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        .result.error {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            margin: 5px 0;
        }
        .status.testing {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .status.success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        .controls .btn {
            width: auto;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🌐 WIDDX AI</div>
            <h1>Multi-Language Support Test</h1>
            <p>Testing AI responses in different languages</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="testAllLanguages()">🚀 Test All Languages</button>
            <button class="btn" onclick="clearResults()">🗑️ Clear Results</button>
            <button class="btn" onclick="window.open('/', '_blank')">🏠 Open WIDDX AI</button>
        </div>

        <div class="test-grid">
            <!-- Arabic Test -->
            <div class="test-card">
                <h3>
                    <span class="language-flag">🇸🇦</span>
                    Arabic (العربية)
                </h3>
                <div class="test-message arabic">
                    مرحباً، كيف حالك؟ أريد مساعدة في البرمجة.
                </div>
                <button class="btn" onclick="testLanguage('ar', 'مرحباً، كيف حالك؟ أريد مساعدة في البرمجة.')">Test Arabic</button>
                <div class="status" id="status-ar">Ready to test</div>
                <div class="result" id="result-ar">Click button to test Arabic response...</div>
            </div>

            <!-- English Test -->
            <div class="test-card">
                <h3>
                    <span class="language-flag">🇺🇸</span>
                    English
                </h3>
                <div class="test-message">
                    Hello, how are you? I need help with programming.
                </div>
                <button class="btn" onclick="testLanguage('en', 'Hello, how are you? I need help with programming.')">Test English</button>
                <div class="status" id="status-en">Ready to test</div>
                <div class="result" id="result-en">Click button to test English response...</div>
            </div>

            <!-- French Test -->
            <div class="test-card">
                <h3>
                    <span class="language-flag">🇫🇷</span>
                    French (Français)
                </h3>
                <div class="test-message">
                    Bonjour, comment allez-vous? J'ai besoin d'aide avec la programmation.
                </div>
                <button class="btn" onclick="testLanguage('fr', 'Bonjour, comment allez-vous? J\'ai besoin d\'aide avec la programmation.')">Test French</button>
                <div class="status" id="status-fr">Ready to test</div>
                <div class="result" id="result-fr">Click button to test French response...</div>
            </div>

            <!-- Spanish Test -->
            <div class="test-card">
                <h3>
                    <span class="language-flag">🇪🇸</span>
                    Spanish (Español)
                </h3>
                <div class="test-message">
                    Hola, ¿cómo estás? Necesito ayuda con programación.
                </div>
                <button class="btn" onclick="testLanguage('es', 'Hola, ¿cómo estás? Necesito ayuda con programación.')">Test Spanish</button>
                <div class="status" id="status-es">Ready to test</div>
                <div class="result" id="result-es">Click button to test Spanish response...</div>
            </div>

            <!-- German Test -->
            <div class="test-card">
                <h3>
                    <span class="language-flag">🇩🇪</span>
                    German (Deutsch)
                </h3>
                <div class="test-message">
                    Hallo, wie geht es dir? Ich brauche Hilfe beim Programmieren.
                </div>
                <button class="btn" onclick="testLanguage('de', 'Hallo, wie geht es dir? Ich brauche Hilfe beim Programmieren.')">Test German</button>
                <div class="status" id="status-de">Ready to test</div>
                <div class="result" id="result-de">Click button to test German response...</div>
            </div>

            <!-- Chinese Test -->
            <div class="test-card">
                <h3>
                    <span class="language-flag">🇨🇳</span>
                    Chinese (中文)
                </h3>
                <div class="test-message">
                    你好，你好吗？我需要编程帮助。
                </div>
                <button class="btn" onclick="testLanguage('zh', '你好，你好吗？我需要编程帮助。')">Test Chinese</button>
                <div class="status" id="status-zh">Ready to test</div>
                <div class="result" id="result-zh">Click button to test Chinese response...</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; color: #94a3b8;">
            <p>🌐 <strong>Multi-Language AI Testing</strong></p>
            <p>WIDDX AI automatically detects user language and responds accordingly</p>
        </div>
    </div>

    <script>
        async function testLanguage(langCode, message) {
            const statusEl = document.getElementById(`status-${langCode}`);
            const resultEl = document.getElementById(`result-${langCode}`);
            const buttonEl = event.target;
            
            // Update status
            statusEl.textContent = 'Testing...';
            statusEl.className = 'status testing';
            buttonEl.disabled = true;
            
            resultEl.textContent = 'Sending request to WIDDX AI...\n';
            resultEl.className = 'result';
            
            try {
                const start = Date.now();
                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        message: message,
                        preferred_service: 'auto',
                        use_knowledge_base: true
                    })
                });
                
                const responseTime = Date.now() - start;
                const data = await response.json();
                
                if (response.ok && data.success) {
                    statusEl.textContent = 'Success';
                    statusEl.className = 'status success';
                    
                    resultEl.textContent = `✅ Language Detection: ${data.data.language || 'Unknown'}\n`;
                    resultEl.textContent += `🤖 Model: ${data.data.model || 'Unknown'}\n`;
                    resultEl.textContent += `⚡ Service: ${data.data.service || 'Unknown'}\n`;
                    resultEl.textContent += `⏱️ Response Time: ${responseTime}ms\n`;
                    resultEl.textContent += `📝 Tokens: ${data.data.tokens_used || 0}\n\n`;
                    resultEl.textContent += `💬 AI Response:\n${data.data.message.content}`;
                    resultEl.className = 'result success';
                    
                    // Check if detected language matches expected
                    if (data.data.language === langCode) {
                        resultEl.textContent += '\n\n🎯 Language detection: CORRECT';
                    } else {
                        resultEl.textContent += `\n\n⚠️ Language detection: Expected ${langCode}, got ${data.data.language}`;
                    }
                    
                } else {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }
                
            } catch (error) {
                statusEl.textContent = 'Error';
                statusEl.className = 'status error';
                
                resultEl.textContent = `❌ Error: ${error.message}\n`;
                resultEl.textContent += `This might indicate an issue with language detection or API processing.`;
                resultEl.className = 'result error';
            } finally {
                buttonEl.disabled = false;
            }
        }

        async function testAllLanguages() {
            const languages = [
                { code: 'ar', message: 'مرحباً، كيف حالك؟ أريد مساعدة في البرمجة.' },
                { code: 'en', message: 'Hello, how are you? I need help with programming.' },
                { code: 'fr', message: 'Bonjour, comment allez-vous? J\'ai besoin d\'aide avec la programmation.' },
                { code: 'es', message: 'Hola, ¿cómo estás? Necesito ayuda con programación.' },
                { code: 'de', message: 'Hallo, wie geht es dir? Ich brauche Hilfe beim Programmieren.' },
                { code: 'zh', message: '你好，你好吗？我需要编程帮助。' }
            ];
            
            for (let i = 0; i < languages.length; i++) {
                const lang = languages[i];
                
                // Simulate button click
                const button = document.querySelector(`[onclick*="${lang.code}"]`);
                if (button) {
                    button.click();
                    
                    // Wait between requests
                    if (i < languages.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                }
            }
        }

        function clearResults() {
            const languages = ['ar', 'en', 'fr', 'es', 'de', 'zh'];
            
            languages.forEach(lang => {
                const statusEl = document.getElementById(`status-${lang}`);
                const resultEl = document.getElementById(`result-${lang}`);
                
                if (statusEl) {
                    statusEl.textContent = 'Ready to test';
                    statusEl.className = 'status';
                }
                
                if (resultEl) {
                    resultEl.textContent = `Click button to test ${lang.toUpperCase()} response...`;
                    resultEl.className = 'result';
                }
            });
        }
    </script>
</body>
</html>
