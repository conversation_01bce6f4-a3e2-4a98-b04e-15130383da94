{"name": "WIDDX AI - Advanced Intelligent Assistant", "short_name": "WIDDX AI", "description": "Your advanced intelligent assistant with multi-model support including DeepSeek, Gemini, and HuggingFace", "start_url": "/", "display": "standalone", "background_color": "#0f172a", "theme_color": "#3b82f6", "orientation": "portrait-primary", "categories": ["productivity", "utilities", "education"], "lang": "en", "dir": "ltr", "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "/screenshots/desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "WIDDX AI Desktop Interface"}, {"src": "/screenshots/mobile.png", "sizes": "375x812", "type": "image/png", "form_factor": "narrow", "label": "WIDDX AI Mobile Interface"}], "shortcuts": [{"name": "New Chat", "short_name": "New Chat", "description": "Start a new conversation", "url": "/?action=new-chat", "icons": [{"src": "/icons/new-chat.png", "sizes": "96x96"}]}, {"name": "History", "short_name": "History", "description": "View conversation history", "url": "/?action=history", "icons": [{"src": "/icons/history.png", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "scope": "/", "id": "widdx-ai", "launch_handler": {"client_mode": "navigate-existing"}, "edge_side_panel": {"preferred_width": 400}, "protocol_handlers": [{"protocol": "web+widdx", "url": "/?action=chat&text=%s"}]}