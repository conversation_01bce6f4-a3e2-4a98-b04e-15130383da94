<!DOCTYPE html>
<html>
<head>
    <title>Simple API Test</title>
    <style>
        body { font-family: Arial; padding: 20px; background: #1a1a1a; color: white; }
        button { background: #3b82f6; color: white; border: none; padding: 10px 20px; margin: 10px; border-radius: 5px; cursor: pointer; }
        .result { background: #2a2a2a; padding: 15px; margin: 10px 0; border-radius: 5px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>WIDDX AI - Simple Test</h1>
    
    <button onclick="testAPI()">Test API</button>
    <div id="result" class="result">Click button to test...</div>

    <script>
        async function testAPI() {
            const result = document.getElementById('result');
            result.innerHTML = 'Testing...';
            
            try {
                // Test 1: Health
                result.innerHTML += '\n1. Testing health endpoint...';
                const healthResponse = await fetch('/api/health');
                const healthData = await healthResponse.json();
                result.innerHTML += '\n✅ Health: ' + JSON.stringify(healthData);
                
                // Test 2: Conversations
                result.innerHTML += '\n\n2. Testing conversations endpoint...';
                const convResponse = await fetch('/api/conversations');
                const convData = await convResponse.json();
                result.innerHTML += '\n✅ Conversations: ' + JSON.stringify(convData);
                
                // Test 3: Send message
                result.innerHTML += '\n\n3. Testing send message...';
                const msgResponse = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'Hello test'
                    })
                });
                
                const msgData = await msgResponse.json();
                result.innerHTML += '\n✅ Message Response: ' + JSON.stringify(msgData);
                
            } catch (error) {
                result.innerHTML += '\n❌ Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
