<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX AI - Status Check</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px;
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        .card h3 {
            margin: 0 0 16px 0;
            color: #3b82f6;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 12px;
        }
        .status.success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .status.warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }
        .status.loading {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .details {
            font-size: 0.875rem;
            color: #94a3b8;
            line-height: 1.5;
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            margin: 8px 0;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            margin: 8px 8px 8px 0;
        }
        .btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        .btn:disabled {
            background: #4b5563;
            cursor: not-allowed;
            transform: none;
        }
        .progress {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin: 16px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            width: 0%;
            transition: width 0.3s ease;
        }
        .icon {
            width: 20px;
            height: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🤖 WIDDX AI</div>
            <h1>System Status Check</h1>
            <p>Comprehensive health check for all system components</p>
        </div>

        <div class="grid">
            <!-- Server Status -->
            <div class="card">
                <h3>
                    <span class="icon">🖥️</span>
                    Server Status
                </h3>
                <div id="server-status" class="status loading">Checking...</div>
                <div id="server-details" class="details">Verifying server connectivity and response time...</div>
            </div>

            <!-- API Endpoints -->
            <div class="card">
                <h3>
                    <span class="icon">🔌</span>
                    API Endpoints
                </h3>
                <div id="api-status" class="status loading">Checking...</div>
                <div id="api-details" class="details">Testing all API endpoints...</div>
            </div>

            <!-- Database -->
            <div class="card">
                <h3>
                    <span class="icon">🗄️</span>
                    Database
                </h3>
                <div id="db-status" class="status loading">Checking...</div>
                <div id="db-details" class="details">Verifying database connectivity...</div>
            </div>

            <!-- Frontend Assets -->
            <div class="card">
                <h3>
                    <span class="icon">🎨</span>
                    Frontend Assets
                </h3>
                <div id="assets-status" class="status loading">Checking...</div>
                <div id="assets-details" class="details">Loading CSS and JavaScript files...</div>
            </div>

            <!-- AI Services -->
            <div class="card">
                <h3>
                    <span class="icon">🧠</span>
                    AI Services
                </h3>
                <div id="ai-status" class="status loading">Checking...</div>
                <div id="ai-details" class="details">Testing AI model integration...</div>
            </div>

            <!-- Security -->
            <div class="card">
                <h3>
                    <span class="icon">🔒</span>
                    Security
                </h3>
                <div id="security-status" class="status loading">Checking...</div>
                <div id="security-details" class="details">Verifying CSRF tokens and security headers...</div>
            </div>
        </div>

        <div class="card">
            <h3>
                <span class="icon">📊</span>
                Overall Progress
            </h3>
            <div class="progress">
                <div id="overall-progress" class="progress-bar"></div>
            </div>
            <div id="overall-status" class="details">Starting system check...</div>
            
            <div style="margin-top: 20px;">
                <button class="btn" onclick="runFullCheck()">🔄 Run Full Check</button>
                <button class="btn" onclick="testMessage()">💬 Test Message</button>
                <button class="btn" onclick="window.open('/', '_blank')">🚀 Open WIDDX AI</button>
            </div>
        </div>

        <div id="test-results" class="card" style="display: none;">
            <h3>
                <span class="icon">🧪</span>
                Test Results
            </h3>
            <div id="test-output" class="code"></div>
        </div>
    </div>

    <script>
        let checkProgress = 0;
        const totalChecks = 6;

        // Run all checks on page load
        window.onload = function() {
            runFullCheck();
        };

        async function runFullCheck() {
            checkProgress = 0;
            updateProgress();
            
            document.getElementById('overall-status').textContent = 'Running comprehensive system check...';
            
            await checkServer();
            await checkAPI();
            await checkDatabase();
            await checkAssets();
            await checkAI();
            await checkSecurity();
            
            document.getElementById('overall-status').textContent = 'System check completed!';
        }

        function updateProgress() {
            const percentage = (checkProgress / totalChecks) * 100;
            document.getElementById('overall-progress').style.width = percentage + '%';
        }

        async function checkServer() {
            try {
                const start = Date.now();
                const response = await fetch('/api/health');
                const responseTime = Date.now() - start;
                
                if (response.ok) {
                    const data = await response.json();
                    setStatus('server', 'success', `Server is running (${responseTime}ms)`);
                    setDetails('server', `Status: ${data.status}\nVersion: ${data.version}\nResponse Time: ${responseTime}ms`);
                } else {
                    setStatus('server', 'error', `Server error (${response.status})`);
                    setDetails('server', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                setStatus('server', 'error', 'Server unreachable');
                setDetails('server', `Error: ${error.message}`);
            }
            
            checkProgress++;
            updateProgress();
        }

        async function checkAPI() {
            const endpoints = [
                { url: '/api/health', name: 'Health' },
                { url: '/api/conversations', name: 'Conversations' }
            ];
            
            let successCount = 0;
            let details = '';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url);
                    if (response.ok) {
                        successCount++;
                        details += `✅ ${endpoint.name}: OK\n`;
                    } else {
                        details += `❌ ${endpoint.name}: ${response.status}\n`;
                    }
                } catch (error) {
                    details += `❌ ${endpoint.name}: ${error.message}\n`;
                }
            }
            
            if (successCount === endpoints.length) {
                setStatus('api', 'success', 'All endpoints working');
            } else if (successCount > 0) {
                setStatus('api', 'warning', `${successCount}/${endpoints.length} endpoints working`);
            } else {
                setStatus('api', 'error', 'API endpoints failing');
            }
            
            setDetails('api', details);
            checkProgress++;
            updateProgress();
        }

        async function checkDatabase() {
            try {
                const response = await fetch('/api/conversations');
                if (response.ok) {
                    const data = await response.json();
                    setStatus('db', 'success', 'Database connected');
                    setDetails('db', `Connection: OK\nConversations: ${data.data ? data.data.length : 0}`);
                } else {
                    setStatus('db', 'error', 'Database connection failed');
                    setDetails('db', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                setStatus('db', 'error', 'Database unreachable');
                setDetails('db', `Error: ${error.message}`);
            }
            
            checkProgress++;
            updateProgress();
        }

        async function checkAssets() {
            const assets = [
                '/css/widdx-core.css',
                '/css/widdx-components.css',
                '/js/widdx-core.js',
                '/js/widdx-api.js'
            ];
            
            let loadedCount = 0;
            let details = '';
            
            for (const asset of assets) {
                try {
                    const response = await fetch(asset);
                    if (response.ok) {
                        loadedCount++;
                        details += `✅ ${asset}: Loaded\n`;
                    } else {
                        details += `❌ ${asset}: ${response.status}\n`;
                    }
                } catch (error) {
                    details += `❌ ${asset}: ${error.message}\n`;
                }
            }
            
            if (loadedCount === assets.length) {
                setStatus('assets', 'success', 'All assets loaded');
            } else if (loadedCount > 0) {
                setStatus('assets', 'warning', `${loadedCount}/${assets.length} assets loaded`);
            } else {
                setStatus('assets', 'error', 'Assets failed to load');
            }
            
            setDetails('assets', details);
            checkProgress++;
            updateProgress();
        }

        async function checkAI() {
            try {
                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'Test message for system check'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        setStatus('ai', 'success', 'AI services working');
                        setDetails('ai', `Model: ${data.data?.model || 'Unknown'}\nService: ${data.data?.service || 'Unknown'}\nResponse: Generated successfully`);
                    } else {
                        setStatus('ai', 'error', 'AI response failed');
                        setDetails('ai', `Error: ${data.message || 'Unknown error'}`);
                    }
                } else {
                    setStatus('ai', 'error', `AI service error (${response.status})`);
                    setDetails('ai', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                setStatus('ai', 'error', 'AI service unreachable');
                setDetails('ai', `Error: ${error.message}`);
            }
            
            checkProgress++;
            updateProgress();
        }

        async function checkSecurity() {
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            let details = '';
            let issues = 0;
            
            if (csrfToken) {
                details += '✅ CSRF Token: Present\n';
            } else {
                details += '❌ CSRF Token: Missing\n';
                issues++;
            }
            
            // Check HTTPS (in production)
            if (location.protocol === 'https:') {
                details += '✅ HTTPS: Enabled\n';
            } else {
                details += '⚠️ HTTPS: Disabled (development)\n';
            }
            
            // Check security headers
            try {
                const response = await fetch('/api/health');
                const headers = response.headers;
                
                if (headers.get('X-Frame-Options')) {
                    details += '✅ X-Frame-Options: Set\n';
                } else {
                    details += '⚠️ X-Frame-Options: Not set\n';
                }
            } catch (error) {
                details += '❌ Security headers: Could not check\n';
                issues++;
            }
            
            if (issues === 0) {
                setStatus('security', 'success', 'Security checks passed');
            } else if (issues === 1) {
                setStatus('security', 'warning', 'Minor security issues');
            } else {
                setStatus('security', 'error', 'Security issues found');
            }
            
            setDetails('security', details);
            checkProgress++;
            updateProgress();
        }

        async function testMessage() {
            const testResults = document.getElementById('test-results');
            const testOutput = document.getElementById('test-output');
            
            testResults.style.display = 'block';
            testOutput.textContent = 'Testing message functionality...\n';
            
            try {
                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: 'Hello! This is a test message from the status check system.',
                        preferred_service: 'auto',
                        use_knowledge_base: true
                    })
                });
                
                const data = await response.json();
                testOutput.textContent += `\nResponse Status: ${response.status}\n`;
                testOutput.textContent += `Response Data:\n${JSON.stringify(data, null, 2)}`;
                
                if (data.success) {
                    testOutput.textContent += '\n\n✅ Message test PASSED!';
                } else {
                    testOutput.textContent += '\n\n❌ Message test FAILED!';
                }
                
            } catch (error) {
                testOutput.textContent += `\n\n❌ Message test ERROR: ${error.message}`;
            }
        }

        function setStatus(component, type, message) {
            const statusEl = document.getElementById(`${component}-status`);
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        function setDetails(component, details) {
            const detailsEl = document.getElementById(`${component}-details`);
            detailsEl.textContent = details;
        }
    </script>
</body>
</html>
