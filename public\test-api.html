<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="">
    <title>WIDDX AI - API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .result {
            background: #1a1a1a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #555;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { border-color: #10b981; }
        .error { border-color: #ef4444; }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            background: #1a1a1a;
            border: 1px solid #555;
            border-radius: 5px;
            color: white;
        }
    </style>
</head>
<body>
    <h1>🤖 WIDDX AI - API Test Interface</h1>
    
    <div class="test-section">
        <h2>1. Health Check</h2>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Conversations List</h2>
        <button onclick="testConversations()">Get Conversations</button>
        <div id="conversations-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Send Message</h2>
        <textarea id="test-message" placeholder="Enter your test message..." rows="3">Hello, how are you?</textarea>
        <button onclick="testSendMessage()">Send Message</button>
        <div id="message-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. CSRF Token</h2>
        <button onclick="getCSRFToken()">Get CSRF Token</button>
        <div id="csrf-result" class="result"></div>
    </div>

    <script>
        // Get CSRF token from Laravel
        async function getCSRFToken() {
            try {
                const response = await fetch('/sanctum/csrf-cookie');
                const cookies = document.cookie;
                document.getElementById('csrf-result').innerHTML = `Cookies: ${cookies}`;
                document.getElementById('csrf-result').className = 'result success';
            } catch (error) {
                document.getElementById('csrf-result').innerHTML = `Error: ${error.message}`;
                document.getElementById('csrf-result').className = 'result error';
            }
        }

        // Test health endpoint
        async function testHealth() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                document.getElementById('health-result').innerHTML = JSON.stringify(data, null, 2);
                document.getElementById('health-result').className = 'result success';
            } catch (error) {
                document.getElementById('health-result').innerHTML = `Error: ${error.message}`;
                document.getElementById('health-result').className = 'result error';
            }
        }

        // Test conversations endpoint
        async function testConversations() {
            try {
                const response = await fetch('/api/conversations');
                const data = await response.json();
                document.getElementById('conversations-result').innerHTML = JSON.stringify(data, null, 2);
                document.getElementById('conversations-result').className = 'result success';
            } catch (error) {
                document.getElementById('conversations-result').innerHTML = `Error: ${error.message}`;
                document.getElementById('conversations-result').className = 'result error';
            }
        }

        // Test send message endpoint
        async function testSendMessage() {
            const message = document.getElementById('test-message').value;
            if (!message.trim()) {
                alert('Please enter a message');
                return;
            }

            try {
                const response = await fetch('/api/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        message: message,
                        preferred_service: 'auto',
                        use_knowledge_base: true
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('message-result').innerHTML = JSON.stringify(data, null, 2);
                    document.getElementById('message-result').className = 'result success';
                } else {
                    document.getElementById('message-result').innerHTML = `Error ${response.status}: ${JSON.stringify(data, null, 2)}`;
                    document.getElementById('message-result').className = 'result error';
                }
            } catch (error) {
                document.getElementById('message-result').innerHTML = `Network Error: ${error.message}`;
                document.getElementById('message-result').className = 'result error';
            }
        }

        // Auto-test on load
        window.onload = function() {
            testHealth();
            testConversations();
        };
    </script>
</body>
</html>
