<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX AI Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #fff;
        }
        .container {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        input, textarea, button {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #444;
            border-radius: 5px;
            background-color: #333;
            color: #fff;
        }
        button {
            background-color: #007bff;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            background-color: #333;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .error {
            background-color: #dc3545;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success {
            background-color: #28a745;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🤖 WIDDX AI Test Interface</h1>
    
    <div class="container">
        <h2>Test API Connection</h2>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <button onclick="testConversations()">Test Conversations</button>
        <div id="healthResult"></div>
    </div>

    <div class="container">
        <h2>Send Message</h2>
        <textarea id="messageInput" placeholder="Type your message here..." rows="3"></textarea>
        <button onclick="sendMessage()">Send Message</button>
        <div id="messageResult"></div>
    </div>

    <div class="container">
        <h2>Conversations List</h2>
        <button onclick="loadConversations()">Load Conversations</button>
        <div id="conversationsResult"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';

        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                resultDiv.innerHTML = `<div class="success">✅ Health Check: OK<br>${JSON.stringify(data, null, 2)}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Health Check Failed: ${error.message}</div>`;
            }
        }

        async function testConversations() {
            const resultDiv = document.getElementById('healthResult');
            try {
                const response = await fetch(`${API_BASE}/conversations`);
                const data = await response.json();
                resultDiv.innerHTML = `<div class="success">✅ Conversations: OK<br>Found ${data.data.length} conversations</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Conversations Failed: ${error.message}</div>`;
            }
        }

        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const resultDiv = document.getElementById('messageResult');
            const message = messageInput.value.trim();

            if (!message) {
                resultDiv.innerHTML = '<div class="error">Please enter a message</div>';
                return;
            }

            resultDiv.innerHTML = '<div>Sending message...</div>';

            try {
                const response = await fetch(`${API_BASE}/ask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        use_knowledge_base: true,
                        preferred_service: null,
                        knowledge_threshold: 0.7,
                        max_knowledge_results: 5,
                        tags: [],
                        source_filter: null,
                        context: []
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Message sent successfully!</div>
                        <div class="response"><strong>AI Response:</strong><br>${data.data.message.content}</div>
                    `;
                    messageInput.value = '';
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${data.message || 'Unknown error'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }

        async function loadConversations() {
            const resultDiv = document.getElementById('conversationsResult');
            resultDiv.innerHTML = '<div>Loading conversations...</div>';

            try {
                const response = await fetch(`${API_BASE}/conversations`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    let html = `<div class="success">✅ Found ${data.data.length} conversations</div>`;
                    
                    data.data.forEach(conv => {
                        html += `
                            <div class="response">
                                <strong>ID:</strong> ${conv.id}<br>
                                <strong>Title:</strong> ${conv.title}<br>
                                <strong>Messages:</strong> ${conv.messages_count}<br>
                                <strong>Created:</strong> ${new Date(conv.created_at).toLocaleString()}
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Error loading conversations</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }

        // Auto-test on page load
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
