<template>
  <div id="app" class="min-h-screen bg-gray-900 text-white">
    <!-- Navigation Header -->
    <nav class="bg-gray-800 border-b border-gray-700 px-4 py-3">
      <div class="max-w-7xl mx-auto flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">W</span>
            </div>
            <h1 class="text-xl font-bold text-white">WIDDX AI</h1>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <router-link
            to="/"
            class="px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="$route.name === 'chat' ? 'bg-gray-700 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-700'"
          >
            Chat
          </router-link>
          <router-link
            to="/admin"
            class="px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="$route.name === 'admin' ? 'bg-gray-700 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-700'"
          >
            Admin
          </router-link>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1">
      <router-view />
    </main>
  </div>
</template>

<script setup>
// No additional logic needed for now
</script>

<style>
/* Global styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #374151;
}

::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
