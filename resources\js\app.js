import './bootstrap';
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createRouter, createWebHistory } from 'vue-router';
import App from './App.vue';
import ChatView from './views/ChatView.vue';
import AdminView from './views/AdminView.vue';

// Create Pinia store
const pinia = createPinia();

// Create router
const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '/',
            name: 'chat',
            component: ChatView,
        },
        {
            path: '/admin',
            name: 'admin',
            component: AdminView,
        },
    ],
});

// Create and mount the app
const app = createApp(App);
app.use(pinia);
app.use(router);
app.mount('#app');
