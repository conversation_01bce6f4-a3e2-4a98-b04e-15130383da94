<template>
  <div class="relative">
    <div class="flex items-end space-x-3">
      <!-- Text Input -->
      <div class="flex-1 relative">
        <textarea
          ref="textareaRef"
          v-model="message"
          :placeholder="placeholder"
          :disabled="disabled"
          class="w-full bg-gray-700 text-white rounded-lg px-4 py-3 pr-12 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          rows="1"
          @keydown="handleKeydown"
          @input="adjustHeight"
        ></textarea>
        
        <!-- Send Button -->
        <button
          @click="sendMessage"
          :disabled="!canSend"
          class="absolute right-2 bottom-2 p-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          :class="canSend 
            ? 'bg-blue-600 hover:bg-blue-700 text-white' 
            : 'bg-gray-600 text-gray-400'"
        >
          <PaperAirplaneIcon class="w-5 h-5" />
        </button>
      </div>

      <!-- Options Button -->
      <div class="relative">
        <button
          @click="showOptions = !showOptions"
          class="p-3 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-lg transition-colors"
          title="Message options"
        >
          <Cog6ToothIcon class="w-5 h-5" />
        </button>

        <!-- Options Dropdown -->
        <div
          v-if="showOptions"
          class="absolute bottom-full right-0 mb-2 w-64 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10"
        >
          <div class="p-4 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">
                Preferred Model
              </label>
              <select
                v-model="options.preferred_service"
                class="w-full bg-gray-700 text-white rounded border border-gray-600 px-3 py-2 focus:outline-none focus:border-blue-500"
              >
                <option value="">Auto-select</option>
                <option value="deepseek">🧠 DeepSeek (Code)</option>
                <option value="gemini">💎 Gemini (Research)</option>
                <option value="huggingface">🤗 HuggingFace (Creative)</option>
              </select>
            </div>

            <div>
              <label class="flex items-center space-x-2">
                <input
                  v-model="options.use_knowledge_base"
                  type="checkbox"
                  class="rounded bg-gray-700 border-gray-600 text-blue-600 focus:ring-blue-500"
                >
                <span class="text-sm text-gray-300">Use knowledge base</span>
              </label>
            </div>

            <div v-if="options.use_knowledge_base">
              <label class="block text-sm font-medium text-gray-300 mb-2">
                Knowledge Threshold: {{ options.knowledge_threshold }}
              </label>
              <input
                v-model.number="options.knowledge_threshold"
                type="range"
                min="0.1"
                max="1.0"
                step="0.1"
                class="w-full"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">
                Tags (comma-separated)
              </label>
              <input
                v-model="tagsInput"
                type="text"
                placeholder="e.g., code, laravel, php"
                class="w-full bg-gray-700 text-white rounded border border-gray-600 px-3 py-2 focus:outline-none focus:border-blue-500"
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Character Count -->
    <div v-if="message.length > 0" class="text-xs text-gray-500 mt-1 text-right">
      {{ message.length }} characters
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue';
import { PaperAirplaneIcon, Cog6ToothIcon } from '@heroicons/vue/24/outline';

const props = defineProps({
  placeholder: {
    type: String,
    default: 'Type your message...'
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['send']);

const message = ref('');
const showOptions = ref(false);
const textareaRef = ref(null);
const tagsInput = ref('');

const options = ref({
  preferred_service: '',
  use_knowledge_base: true,
  knowledge_threshold: 0.7,
  max_knowledge_results: 5
});

// Computed
const canSend = computed(() => {
  return message.value.trim().length > 0 && !props.disabled;
});

const parsedTags = computed(() => {
  return tagsInput.value
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);
});

// Methods
const sendMessage = () => {
  if (!canSend.value) return;

  const messageOptions = {
    ...options.value,
    tags: parsedTags.value
  };

  emit('send', message.value.trim(), messageOptions);
  message.value = '';
  showOptions.value = false;
  adjustHeight();
};

const handleKeydown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

const adjustHeight = () => {
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.style.height = 'auto';
      const scrollHeight = textareaRef.value.scrollHeight;
      const maxHeight = 120; // Max height in pixels
      textareaRef.value.style.height = Math.min(scrollHeight, maxHeight) + 'px';
    }
  });
};

// Close options when clicking outside
const handleClickOutside = (event) => {
  if (showOptions.value && !event.target.closest('.relative')) {
    showOptions.value = false;
  }
};

// Watch for message changes to adjust height
watch(message, adjustHeight);

// Add click outside listener
document.addEventListener('click', handleClickOutside);
</script>
