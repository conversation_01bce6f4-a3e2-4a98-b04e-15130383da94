<template>
  <div class="flex space-x-4" :class="message.role === 'user' ? 'justify-end' : 'justify-start'">
    <!-- Avatar -->
    <div v-if="message.role === 'assistant'" class="flex-shrink-0">
      <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
        <span class="text-white font-bold text-sm">W</span>
      </div>
    </div>

    <!-- Message Content -->
    <div class="flex-1 max-w-3xl">
      <div
        class="rounded-lg px-4 py-3 relative group"
        :class="message.role === 'user' 
          ? 'bg-blue-600 text-white ml-auto max-w-md' 
          : 'bg-gray-700 text-gray-100'"
      >
        <!-- Edit Mode -->
        <div v-if="isEditing" class="space-y-3">
          <textarea
            v-model="editContent"
            class="w-full bg-gray-800 text-white rounded border border-gray-600 px-3 py-2 resize-none focus:outline-none focus:border-blue-500"
            rows="3"
            @keydown.ctrl.enter="saveEdit"
            @keydown.esc="cancelEdit"
          ></textarea>
          <div class="flex space-x-2">
            <button
              @click="saveEdit"
              class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors"
            >
              Save & Resend
            </button>
            <button
              @click="cancelEdit"
              class="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>

        <!-- Display Mode -->
        <div v-else>
          <!-- Message Content -->
          <div class="prose prose-invert max-w-none" v-html="formattedContent"></div>

          <!-- Model Badge for Assistant Messages -->
          <div v-if="message.role === 'assistant'" class="flex items-center justify-between mt-3 pt-2 border-t border-gray-600">
            <div class="flex items-center space-x-2">
              <ModelBadge :model="message.model_used || message.service_used" />
              
              <!-- Knowledge Base Indicator -->
              <div v-if="message.knowledge_base_used" class="flex items-center space-x-1">
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <span class="text-xs text-green-400">Local Knowledge</span>
              </div>
              
              <!-- Fallback Indicator -->
              <div v-if="message.fallback_used" class="flex items-center space-x-1">
                <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <span class="text-xs text-yellow-400">Fallback</span>
              </div>
            </div>

            <!-- Processing Time -->
            <div v-if="message.metadata?.processing_time" class="text-xs text-gray-400">
              {{ formatProcessingTime(message.metadata.processing_time) }}
            </div>
          </div>

          <!-- Message Actions -->
          <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <div class="flex space-x-1">
              <button
                v-if="message.role === 'user'"
                @click="startEdit"
                class="p-1 bg-gray-800 hover:bg-gray-700 rounded text-gray-400 hover:text-white transition-colors"
                title="Edit message"
              >
                <PencilIcon class="w-4 h-4" />
              </button>
              <button
                @click="copyMessage"
                class="p-1 bg-gray-800 hover:bg-gray-700 rounded text-gray-400 hover:text-white transition-colors"
                title="Copy message"
              >
                <ClipboardIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Timestamp -->
      <div class="text-xs text-gray-500 mt-1" :class="message.role === 'user' ? 'text-right' : 'text-left'">
        {{ formatTime(message.created_at) }}
      </div>
    </div>

    <!-- User Avatar -->
    <div v-if="message.role === 'user'" class="flex-shrink-0">
      <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
        <span class="text-white font-bold text-sm">U</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { PencilIcon, ClipboardIcon } from '@heroicons/vue/24/outline';
import ModelBadge from './ModelBadge.vue';

const props = defineProps({
  message: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['edit']);

const isEditing = ref(false);
const editContent = ref('');

// Computed
const formattedContent = computed(() => {
  // Simple markdown-like formatting
  let content = props.message.content;
  
  // Convert code blocks
  content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre class="bg-gray-800 p-3 rounded mt-2 mb-2 overflow-x-auto"><code>$2</code></pre>');
  
  // Convert inline code
  content = content.replace(/`([^`]+)`/g, '<code class="bg-gray-800 px-1 py-0.5 rounded text-sm">$1</code>');
  
  // Convert line breaks
  content = content.replace(/\n/g, '<br>');
  
  return content;
});

// Methods
const startEdit = () => {
  editContent.value = props.message.content;
  isEditing.value = true;
};

const cancelEdit = () => {
  isEditing.value = false;
  editContent.value = '';
};

const saveEdit = () => {
  if (editContent.value.trim() && editContent.value !== props.message.content) {
    emit('edit', props.message.id, editContent.value.trim());
  }
  isEditing.value = false;
  editContent.value = '';
};

const copyMessage = async () => {
  try {
    await navigator.clipboard.writeText(props.message.content);
    // Could add a toast notification here
  } catch (err) {
    console.error('Failed to copy message:', err);
  }
};

const formatTime = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const formatProcessingTime = (time) => {
  return `${time.toFixed(2)}s`;
};
</script>
