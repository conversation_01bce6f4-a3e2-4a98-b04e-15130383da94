<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-white">
          {{ entry ? 'Edit Knowledge Entry' : 'Create Knowledge Entry' }}
        </h2>
        <button
          @click="$emit('cancel')"
          class="text-gray-400 hover:text-white transition-colors"
        >
          <XMarkIcon class="w-6 h-6" />
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Content -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Content *
          </label>
          <textarea
            v-model="form.content"
            required
            rows="6"
            class="w-full bg-gray-700 text-white rounded border border-gray-600 px-3 py-2 focus:outline-none focus:border-blue-500"
            placeholder="Enter the knowledge content..."
          ></textarea>
        </div>

        <!-- Source -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Source
          </label>
          <select
            v-model="form.source"
            class="w-full bg-gray-700 text-white rounded border border-gray-600 px-3 py-2 focus:outline-none focus:border-blue-500"
          >
            <option value="">Select source...</option>
            <option value="user_input">User Input</option>
            <option value="documentation">Documentation</option>
            <option value="api_response">API Response</option>
            <option value="manual">Manual Entry</option>
          </select>
        </div>

        <!-- Tags -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Tags
          </label>
          <div class="space-y-2">
            <input
              v-model="tagInput"
              type="text"
              class="w-full bg-gray-700 text-white rounded border border-gray-600 px-3 py-2 focus:outline-none focus:border-blue-500"
              placeholder="Enter tags separated by commas..."
              @keydown.enter.prevent="addTag"
            >
            <div v-if="form.tags.length > 0" class="flex flex-wrap gap-2">
              <span
                v-for="(tag, index) in form.tags"
                :key="index"
                class="inline-flex items-center px-2 py-1 bg-blue-900 text-blue-200 text-sm rounded-full"
              >
                {{ tag }}
                <button
                  type="button"
                  @click="removeTag(index)"
                  class="ml-1 text-blue-400 hover:text-blue-300"
                >
                  <XMarkIcon class="w-3 h-3" />
                </button>
              </span>
            </div>
          </div>
        </div>

        <!-- Confidence Score -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Confidence Score: {{ form.confidence_score.toFixed(2) }}
          </label>
          <input
            v-model.number="form.confidence_score"
            type="range"
            min="0"
            max="1"
            step="0.01"
            class="w-full"
          >
          <div class="flex justify-between text-xs text-gray-400 mt-1">
            <span>Low (0.0)</span>
            <span>High (1.0)</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
          <button
            type="button"
            @click="$emit('cancel')"
            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="!isValid"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ entry ? 'Update' : 'Create' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { XMarkIcon } from '@heroicons/vue/24/outline';

const props = defineProps({
  entry: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['save', 'cancel']);

const form = ref({
  content: '',
  source: '',
  tags: [],
  confidence_score: 0.8
});

const tagInput = ref('');

// Computed
const isValid = computed(() => {
  return form.value.content.trim().length > 0;
});

// Methods
const handleSubmit = () => {
  if (!isValid.value) return;

  emit('save', {
    content: form.value.content.trim(),
    source: form.value.source || null,
    tags: form.value.tags,
    confidence_score: form.value.confidence_score
  });
};

const addTag = () => {
  const tags = tagInput.value
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0 && !form.value.tags.includes(tag));
  
  form.value.tags.push(...tags);
  tagInput.value = '';
};

const removeTag = (index) => {
  form.value.tags.splice(index, 1);
};

// Watch for tag input changes to auto-add on comma
watch(tagInput, (newValue) => {
  if (newValue.includes(',')) {
    addTag();
  }
});

// Initialize form with entry data if editing
onMounted(() => {
  if (props.entry) {
    form.value = {
      content: props.entry.content || '',
      source: props.entry.source || '',
      tags: [...(props.entry.tags || [])],
      confidence_score: props.entry.confidence_score || 0.8
    };
  }
});
</script>
