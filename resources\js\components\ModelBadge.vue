<template>
  <div
    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium transition-all duration-300"
    :class="badgeClasses"
  >
    <component :is="modelIcon" class="w-3 h-3 mr-1" />
    {{ modelName }}
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { 
  CodeBracketIcon, 
  MagnifyingGlassIcon, 
  SparklesIcon,
  CpuChipIcon 
} from '@heroicons/vue/24/outline';

const props = defineProps({
  model: {
    type: String,
    required: true
  }
});

const modelConfig = {
  deepseek: {
    name: '🧠 DeepSeek',
    icon: CodeBracketIcon,
    classes: 'bg-blue-900/50 text-blue-300 border border-blue-700'
  },
  gemini: {
    name: '💎 Gemini',
    icon: MagnifyingGlassIcon,
    classes: 'bg-purple-900/50 text-purple-300 border border-purple-700'
  },
  huggingface: {
    name: '🤗 HuggingFace',
    icon: SparklesIcon,
    classes: 'bg-green-900/50 text-green-300 border border-green-700'
  },
  default: {
    name: '🤖 AI',
    icon: CpuChipIcon,
    classes: 'bg-gray-900/50 text-gray-300 border border-gray-700'
  }
};

const modelInfo = computed(() => {
  const model = props.model?.toLowerCase();
  return modelConfig[model] || modelConfig.default;
});

const modelName = computed(() => modelInfo.value.name);
const modelIcon = computed(() => modelInfo.value.icon);
const badgeClasses = computed(() => modelInfo.value.classes);
</script>
