import axios from 'axios';

class ChatService {
  constructor() {
    this.baseURL = '/api';
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    // Add CSRF token if available
    const token = document.querySelector('meta[name="csrf-token"]');
    if (token) {
      this.client.defaults.headers.common['X-CSRF-TOKEN'] = token.getAttribute('content');
    }

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Send a message to the AI assistant
   * @param {string} message - The user's message
   * @param {object} options - Additional options
   * @returns {Promise} API response
   */
  async sendMessage(message, options = {}) {
    const payload = {
      message,
      conversation_id: options.conversation_id || null,
      use_knowledge_base: options.use_knowledge_base !== false,
      preferred_service: options.preferred_service || null,
      knowledge_threshold: options.knowledge_threshold || 0.7,
      max_knowledge_results: options.max_knowledge_results || 5,
      tags: options.tags || [],
      source_filter: options.source_filter || null,
      context: options.context || []
    };

    return await this.client.post('/ask', payload);
  }

  /**
   * Get a specific conversation with its messages
   * @param {number} conversationId - The conversation ID
   * @returns {Promise} API response
   */
  async getConversation(conversationId) {
    return await this.client.get(`/conversations/${conversationId}`);
  }

  /**
   * Get all conversations for the user
   * @param {object} options - Query options
   * @returns {Promise} API response
   */
  async getConversations(options = {}) {
    const params = {
      page: options.page || 1,
      per_page: options.per_page || 20,
      sort: options.sort || 'created_at',
      order: options.order || 'desc'
    };

    return await this.client.get('/conversations', { params });
  }

  /**
   * Delete a conversation
   * @param {number} conversationId - The conversation ID
   * @returns {Promise} API response
   */
  async deleteConversation(conversationId) {
    return await this.client.delete(`/conversations/${conversationId}`);
  }

  /**
   * Update conversation title
   * @param {number} conversationId - The conversation ID
   * @param {string} title - New title
   * @returns {Promise} API response
   */
  async updateConversationTitle(conversationId, title) {
    return await this.client.patch(`/conversations/${conversationId}`, { title });
  }

  /**
   * Get API health status
   * @returns {Promise} API response
   */
  async getHealth() {
    return await this.client.get('/health');
  }

  /**
   * Get API information
   * @returns {Promise} API response
   */
  async getInfo() {
    return await this.client.get('/info');
  }

  /**
   * Search knowledge base entries
   * @param {string} query - Search query
   * @param {object} options - Search options
   * @returns {Promise} API response
   */
  async searchKnowledge(query, options = {}) {
    const params = {
      q: query,
      threshold: options.threshold || 0.7,
      limit: options.limit || 10,
      tags: options.tags || [],
      source: options.source || null
    };

    return await this.client.get('/knowledge/search', { params });
  }

  /**
   * Get knowledge base entries (admin)
   * @param {object} options - Query options
   * @returns {Promise} API response
   */
  async getKnowledgeEntries(options = {}) {
    const params = {
      page: options.page || 1,
      per_page: options.per_page || 20,
      sort: options.sort || 'created_at',
      order: options.order || 'desc',
      source: options.source || null,
      tags: options.tags || []
    };

    return await this.client.get('/knowledge', { params });
  }

  /**
   * Create a knowledge entry (admin)
   * @param {object} entry - Knowledge entry data
   * @returns {Promise} API response
   */
  async createKnowledgeEntry(entry) {
    return await this.client.post('/knowledge', entry);
  }

  /**
   * Update a knowledge entry (admin)
   * @param {number} entryId - Entry ID
   * @param {object} entry - Updated entry data
   * @returns {Promise} API response
   */
  async updateKnowledgeEntry(entryId, entry) {
    return await this.client.patch(`/knowledge/${entryId}`, entry);
  }

  /**
   * Delete a knowledge entry (admin)
   * @param {number} entryId - Entry ID
   * @returns {Promise} API response
   */
  async deleteKnowledgeEntry(entryId) {
    return await this.client.delete(`/knowledge/${entryId}`);
  }
}

// Create and export a singleton instance
export const chatService = new ChatService();
