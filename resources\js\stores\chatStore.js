import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { chatService } from '../services/chatService';

export const useChatStore = defineStore('chat', () => {
  // State
  const conversations = ref([]);
  const currentConversation = ref(null);
  const messages = ref([]);
  const isLoading = ref(false);
  const isTyping = ref(false);
  const error = ref(null);
  const typingModel = ref('');

  // Computed
  const currentMessages = computed(() => {
    return messages.value.filter(msg => 
      msg.conversation_id === currentConversation.value?.id
    );
  });

  const hasMessages = computed(() => currentMessages.value.length > 0);

  // Actions
  const sendMessage = async (content, options = {}) => {
    if (!content.trim()) return;

    error.value = null;
    isLoading.value = true;
    isTyping.value = true;

    try {
      // Add user message immediately
      const userMessage = {
        id: Date.now(),
        conversation_id: currentConversation.value?.id,
        role: 'user',
        content: content.trim(),
        created_at: new Date().toISOString(),
        temporary: true
      };
      
      messages.value.push(userMessage);

      // Send to API
      const response = await chatService.sendMessage(content, {
        conversation_id: currentConversation.value?.id,
        ...options
      });

      // Update conversation if new
      if (response.data.conversation && !currentConversation.value) {
        currentConversation.value = response.data.conversation;
        conversations.value.unshift(response.data.conversation);
      }

      // Remove temporary user message and add real messages
      messages.value = messages.value.filter(msg => !msg.temporary);
      
      // Add user message from response
      if (response.data.message && response.data.message.role === 'user') {
        messages.value.push({
          ...response.data.message,
          created_at: new Date(response.data.message.created_at).toISOString()
        });
      }

      // Add assistant message
      const assistantMessage = {
        id: Date.now() + 1,
        conversation_id: response.data.conversation.id,
        role: 'assistant',
        content: response.data.response.content,
        model_used: response.data.response.model_used,
        service_used: response.data.response.service_used,
        fallback_used: response.data.response.fallback_used,
        knowledge_base_used: response.data.knowledge_base.used,
        knowledge_entries_found: response.data.knowledge_base.entries_found,
        created_at: new Date().toISOString(),
        metadata: {
          processing_time: response.data.response.processing_time,
          knowledge_base: response.data.knowledge_base
        }
      };

      messages.value.push(assistantMessage);

      return response;
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to send message';
      // Remove temporary message on error
      messages.value = messages.value.filter(msg => !msg.temporary);
      throw err;
    } finally {
      isLoading.value = false;
      isTyping.value = false;
      typingModel.value = '';
    }
  };

  const editAndResendMessage = async (messageId, newContent) => {
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return;

    // Remove all messages after the edited message
    messages.value = messages.value.slice(0, messageIndex);
    
    // Send the new message
    return await sendMessage(newContent);
  };

  const createNewConversation = () => {
    currentConversation.value = null;
    messages.value = [];
    error.value = null;
  };

  const loadConversation = async (conversationId) => {
    try {
      isLoading.value = true;
      const response = await chatService.getConversation(conversationId);
      
      currentConversation.value = response.data.conversation;
      messages.value = response.data.messages.map(msg => ({
        ...msg,
        created_at: new Date(msg.created_at).toISOString()
      }));
    } catch (err) {
      error.value = 'Failed to load conversation';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const loadConversations = async () => {
    try {
      const response = await chatService.getConversations();
      conversations.value = response.data.conversations;
    } catch (err) {
      error.value = 'Failed to load conversations';
      throw err;
    }
  };

  const setTypingModel = (model) => {
    typingModel.value = model;
    isTyping.value = !!model;
  };

  const clearError = () => {
    error.value = null;
  };

  return {
    // State
    conversations,
    currentConversation,
    messages,
    isLoading,
    isTyping,
    error,
    typingModel,
    
    // Computed
    currentMessages,
    hasMessages,
    
    // Actions
    sendMessage,
    editAndResendMessage,
    createNewConversation,
    loadConversation,
    loadConversations,
    setTypingModel,
    clearError
  };
});
