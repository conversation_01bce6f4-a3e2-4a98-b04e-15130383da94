<template>
  <div class="min-h-screen bg-gray-900 p-6">
    <div class="max-w-7xl mx-auto">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">Knowledge Base Admin</h1>
        <p class="text-gray-400">Manage your AI assistant's knowledge entries</p>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-gray-800 rounded-lg p-6">
          <div class="flex items-center">
            <div class="p-2 bg-blue-600 rounded-lg">
              <DocumentTextIcon class="w-6 h-6 text-white" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-400">Total Entries</p>
              <p class="text-2xl font-bold text-white">{{ stats.total || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-600 rounded-lg">
              <CheckCircleIcon class="w-6 h-6 text-white" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-400">High Confidence</p>
              <p class="text-2xl font-bold text-white">{{ stats.high_confidence || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
          <div class="flex items-center">
            <div class="p-2 bg-purple-600 rounded-lg">
              <TagIcon class="w-6 h-6 text-white" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-400">Unique Tags</p>
              <p class="text-2xl font-bold text-white">{{ stats.unique_tags || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-600 rounded-lg">
              <ClockIcon class="w-6 h-6 text-white" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-400">Recent Entries</p>
              <p class="text-2xl font-bold text-white">{{ stats.recent || 0 }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions Bar -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
          <button
            @click="showCreateModal = true"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <PlusIcon class="w-5 h-5 inline mr-2" />
            Add Entry
          </button>
          
          <button
            @click="refreshEntries"
            :disabled="loading"
            class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            <ArrowPathIcon class="w-5 h-5 inline mr-2" :class="{ 'animate-spin': loading }" />
            Refresh
          </button>
        </div>

        <div class="flex items-center space-x-4">
          <!-- Search -->
          <div class="relative">
            <MagnifyingGlassIcon class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search entries..."
              class="pl-10 pr-4 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:border-blue-500"
            >
          </div>

          <!-- Filter by Source -->
          <select
            v-model="selectedSource"
            class="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:border-blue-500"
          >
            <option value="">All Sources</option>
            <option value="user_input">User Input</option>
            <option value="documentation">Documentation</option>
            <option value="api_response">API Response</option>
          </select>
        </div>
      </div>

      <!-- Knowledge Entries Table -->
      <div class="bg-gray-800 rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-700">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Content
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Source
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Tags
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Confidence
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Created
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-700">
              <tr v-if="loading" class="text-center">
                <td colspan="6" class="px-6 py-8 text-gray-400">
                  <div class="flex items-center justify-center">
                    <ArrowPathIcon class="w-6 h-6 animate-spin mr-2" />
                    Loading entries...
                  </div>
                </td>
              </tr>
              
              <tr v-else-if="filteredEntries.length === 0" class="text-center">
                <td colspan="6" class="px-6 py-8 text-gray-400">
                  No knowledge entries found
                </td>
              </tr>

              <tr
                v-for="entry in filteredEntries"
                :key="entry.id"
                class="hover:bg-gray-700 transition-colors"
              >
                <td class="px-6 py-4">
                  <div class="text-sm text-white max-w-md">
                    <p class="truncate">{{ entry.content }}</p>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                    :class="getSourceBadgeClass(entry.source)">
                    {{ entry.source || 'Unknown' }}
                  </span>
                </td>
                <td class="px-6 py-4">
                  <div class="flex flex-wrap gap-1">
                    <span
                      v-for="tag in (entry.tags || [])"
                      :key="tag"
                      class="inline-flex px-2 py-1 text-xs font-medium bg-blue-900 text-blue-200 rounded-full"
                    >
                      {{ tag }}
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div class="w-16 bg-gray-600 rounded-full h-2 mr-2">
                      <div
                        class="h-2 rounded-full"
                        :class="getConfidenceColor(entry.confidence_score)"
                        :style="{ width: `${(entry.confidence_score || 0) * 100}%` }"
                      ></div>
                    </div>
                    <span class="text-sm text-gray-300">
                      {{ ((entry.confidence_score || 0) * 100).toFixed(0) }}%
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4 text-sm text-gray-400">
                  {{ formatDate(entry.created_at) }}
                </td>
                <td class="px-6 py-4">
                  <div class="flex space-x-2">
                    <button
                      @click="editEntry(entry)"
                      class="p-1 text-blue-400 hover:text-blue-300 transition-colors"
                      title="Edit"
                    >
                      <PencilIcon class="w-4 h-4" />
                    </button>
                    <button
                      @click="deleteEntry(entry)"
                      class="p-1 text-red-400 hover:text-red-300 transition-colors"
                      title="Delete"
                    >
                      <TrashIcon class="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="pagination.total > pagination.per_page" class="mt-6 flex items-center justify-between">
        <div class="text-sm text-gray-400">
          Showing {{ pagination.from }} to {{ pagination.to }} of {{ pagination.total }} entries
        </div>
        <div class="flex space-x-2">
          <button
            @click="changePage(pagination.current_page - 1)"
            :disabled="pagination.current_page <= 1"
            class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            @click="changePage(pagination.current_page + 1)"
            :disabled="pagination.current_page >= pagination.last_page"
            class="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <KnowledgeEntryModal
      v-if="showCreateModal || editingEntry"
      :entry="editingEntry"
      @save="handleSaveEntry"
      @cancel="handleCancelEdit"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import {
  DocumentTextIcon,
  CheckCircleIcon,
  TagIcon,
  ClockIcon,
  PlusIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/vue/24/outline';
import { chatService } from '../services/chatService';
import KnowledgeEntryModal from '../components/KnowledgeEntryModal.vue';

// State
const entries = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const selectedSource = ref('');
const showCreateModal = ref(false);
const editingEntry = ref(null);

const stats = ref({
  total: 0,
  high_confidence: 0,
  unique_tags: 0,
  recent: 0
});

const pagination = ref({
  current_page: 1,
  per_page: 20,
  total: 0,
  last_page: 1,
  from: 0,
  to: 0
});

// Computed
const filteredEntries = computed(() => {
  let filtered = entries.value;

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(entry =>
      entry.content.toLowerCase().includes(query) ||
      (entry.tags || []).some(tag => tag.toLowerCase().includes(query))
    );
  }

  if (selectedSource.value) {
    filtered = filtered.filter(entry => entry.source === selectedSource.value);
  }

  return filtered;
});

// Methods
const loadEntries = async (page = 1) => {
  loading.value = true;
  try {
    const response = await chatService.getKnowledgeEntries({
      page,
      per_page: pagination.value.per_page
    });

    entries.value = response.data.entries || [];
    pagination.value = response.data.pagination || pagination.value;
    
    // Update stats
    stats.value = {
      total: response.data.stats?.total || entries.value.length,
      high_confidence: response.data.stats?.high_confidence || 0,
      unique_tags: response.data.stats?.unique_tags || 0,
      recent: response.data.stats?.recent || 0
    };
  } catch (error) {
    console.error('Failed to load knowledge entries:', error);
  } finally {
    loading.value = false;
  }
};

const refreshEntries = () => {
  loadEntries(pagination.value.current_page);
};

const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.last_page) {
    loadEntries(page);
  }
};

const editEntry = (entry) => {
  editingEntry.value = { ...entry };
};

const deleteEntry = async (entry) => {
  if (!confirm('Are you sure you want to delete this knowledge entry?')) {
    return;
  }

  try {
    await chatService.deleteKnowledgeEntry(entry.id);
    await refreshEntries();
  } catch (error) {
    console.error('Failed to delete entry:', error);
    alert('Failed to delete entry. Please try again.');
  }
};

const handleSaveEntry = async (entryData) => {
  try {
    if (editingEntry.value) {
      await chatService.updateKnowledgeEntry(editingEntry.value.id, entryData);
    } else {
      await chatService.createKnowledgeEntry(entryData);
    }
    
    showCreateModal.value = false;
    editingEntry.value = null;
    await refreshEntries();
  } catch (error) {
    console.error('Failed to save entry:', error);
    alert('Failed to save entry. Please try again.');
  }
};

const handleCancelEdit = () => {
  showCreateModal.value = false;
  editingEntry.value = null;
};

const getSourceBadgeClass = (source) => {
  const classes = {
    'user_input': 'bg-blue-900 text-blue-200',
    'documentation': 'bg-green-900 text-green-200',
    'api_response': 'bg-purple-900 text-purple-200'
  };
  return classes[source] || 'bg-gray-900 text-gray-200';
};

const getConfidenceColor = (score) => {
  if (score >= 0.8) return 'bg-green-500';
  if (score >= 0.6) return 'bg-yellow-500';
  return 'bg-red-500';
};

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString();
};

// Watch for search changes
watch([searchQuery, selectedSource], () => {
  // Could implement debounced search here
});

// Load entries on mount
onMounted(() => {
  loadEntries();
});
</script>
