<template>
  <div class="flex h-screen bg-gray-900">
    <!-- Sidebar -->
    <div class="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
      <!-- New Chat Button -->
      <div class="p-4 border-b border-gray-700">
        <button
          @click="startNewChat"
          class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <PlusIcon class="w-5 h-5 mr-2" />
          New Chat
        </button>
      </div>

      <!-- Conversations List -->
      <div class="flex-1 overflow-y-auto">
        <div class="p-2">
          <div v-if="conversations.length === 0" class="text-gray-400 text-center py-8">
            No conversations yet
          </div>
          <div
            v-for="conversation in conversations"
            :key="conversation.id"
            @click="selectConversation(conversation)"
            class="p-3 mb-2 rounded-lg cursor-pointer transition-colors"
            :class="currentConversation?.id === conversation.id 
              ? 'bg-gray-700 text-white' 
              : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
          >
            <div class="font-medium truncate">{{ conversation.title || 'New Conversation' }}</div>
            <div class="text-sm text-gray-400 mt-1">
              {{ formatDate(conversation.created_at) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Chat Area -->
    <div class="flex-1 flex flex-col">
      <!-- Chat Header -->
      <div class="bg-gray-800 border-b border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-semibold text-white">
              {{ currentConversation?.title || 'WIDDX AI Assistant' }}
            </h2>
            <p class="text-sm text-gray-400">
              Intelligent AI assistant with multi-model routing
            </p>
          </div>
          <div v-if="isTyping" class="flex items-center text-blue-400">
            <div class="flex space-x-1 mr-2">
              <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
            <span class="text-sm">WIDDX is thinking...</span>
          </div>
        </div>
      </div>

      <!-- Messages Area -->
      <div class="flex-1 overflow-y-auto p-6" ref="messagesContainer">
        <div v-if="!hasMessages" class="flex items-center justify-center h-full">
          <div class="text-center text-gray-400">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-white font-bold text-2xl">W</span>
            </div>
            <h3 class="text-xl font-semibold mb-2">Welcome to WIDDX AI</h3>
            <p class="text-gray-500">Start a conversation by typing a message below</p>
          </div>
        </div>

        <div v-else class="space-y-6">
          <ChatMessage
            v-for="message in currentMessages"
            :key="message.id"
            :message="message"
            @edit="handleEditMessage"
          />
        </div>
      </div>

      <!-- Input Area -->
      <div class="bg-gray-800 border-t border-gray-700 p-4">
        <ChatInput
          @send="handleSendMessage"
          :disabled="isLoading"
          :placeholder="isLoading ? 'WIDDX is processing...' : 'Type your message...'"
        />
        
        <!-- Error Display -->
        <div v-if="error" class="mt-2 p-3 bg-red-900/50 border border-red-700 rounded-lg">
          <div class="flex items-center justify-between">
            <span class="text-red-200 text-sm">{{ error }}</span>
            <button @click="clearError" class="text-red-400 hover:text-red-300">
              <XMarkIcon class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { PlusIcon, XMarkIcon } from '@heroicons/vue/24/outline';
import { useChatStore } from '../stores/chatStore';
import ChatMessage from '../components/ChatMessage.vue';
import ChatInput from '../components/ChatInput.vue';

const chatStore = useChatStore();
const {
  conversations,
  currentConversation,
  currentMessages,
  hasMessages,
  isLoading,
  isTyping,
  error
} = storeToRefs(chatStore);

const messagesContainer = ref(null);

// Methods
const startNewChat = () => {
  chatStore.createNewConversation();
};

const selectConversation = async (conversation) => {
  if (currentConversation.value?.id === conversation.id) return;
  
  try {
    await chatStore.loadConversation(conversation.id);
    scrollToBottom();
  } catch (err) {
    console.error('Failed to load conversation:', err);
  }
};

const handleSendMessage = async (content) => {
  try {
    await chatStore.sendMessage(content);
    scrollToBottom();
  } catch (err) {
    console.error('Failed to send message:', err);
  }
};

const handleEditMessage = async (messageId, newContent) => {
  try {
    await chatStore.editAndResendMessage(messageId, newContent);
    scrollToBottom();
  } catch (err) {
    console.error('Failed to edit message:', err);
  }
};

const clearError = () => {
  chatStore.clearError();
};

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return 'Today';
  if (diffDays === 2) return 'Yesterday';
  if (diffDays <= 7) return `${diffDays} days ago`;
  
  return date.toLocaleDateString();
};

// Watch for new messages to auto-scroll
watch(currentMessages, () => {
  scrollToBottom();
}, { deep: true });

// Load conversations on mount
onMounted(async () => {
  try {
    await chatStore.loadConversations();
  } catch (err) {
    console.error('Failed to load conversations:', err);
  }
});
</script>
