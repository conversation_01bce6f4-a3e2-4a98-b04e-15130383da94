<template>
  <div class="flex h-screen bg-gray-900 relative">
    <!-- Mobile Menu Overlay -->
    <div
      v-if="showMobileSidebar"
      class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
      @click="showMobileSidebar = false"
    ></div>

    <!-- Sidebar -->
    <div
      class="w-80 bg-gray-800 border-r border-gray-700 flex flex-col transform transition-transform duration-300 ease-in-out z-50"
      :class="showMobileSidebar ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'"
    >
      <!-- New Chat Button -->
      <div class="p-4 border-b border-gray-700">
        <button
          @click="startNewChat"
          class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-105 active:scale-95"
        >
          <PlusIcon class="w-5 h-5 mr-2" />
          New Chat
        </button>
      </div>

      <!-- Conversations List -->
      <div class="flex-1 overflow-y-auto">
        <div class="p-2">
          <div v-if="conversations.length === 0" class="text-gray-400 text-center py-8">
            No conversations yet
          </div>
          <div
            v-for="conversation in conversations"
            :key="conversation.id"
            @click="selectConversation(conversation)"
            class="p-3 mb-2 rounded-lg cursor-pointer transition-colors"
            :class="currentConversation?.id === conversation.id
              ? 'bg-gray-700 text-white'
              : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
          >
            <div class="font-medium truncate">{{ conversation.title || 'New Conversation' }}</div>
            <div class="text-sm text-gray-400 mt-1">
              {{ formatDate(conversation.created_at) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Chat Area -->
    <div class="flex-1 flex flex-col">
      <!-- Chat Header -->
      <div class="bg-gray-800 border-b border-gray-700 px-4 lg:px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <!-- Mobile Menu Button -->
            <button
              @click="showMobileSidebar = !showMobileSidebar"
              class="lg:hidden p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>

            <div>
              <h2 class="text-lg font-semibold text-white">
                {{ currentConversation?.title || 'WIDDX AI Assistant' }}
              </h2>
              <p class="text-sm text-gray-400 hidden sm:block">
                Intelligent AI assistant with multi-model routing
              </p>
            </div>
          </div>

          <div v-if="isTyping" class="flex items-center text-blue-400 animate-pulse">
            <div class="flex space-x-1 mr-2">
              <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
            <span class="text-sm hidden sm:inline">WIDDX is thinking...</span>
            <span class="text-sm sm:hidden">...</span>
          </div>
        </div>
      </div>

      <!-- Messages Area -->
      <div class="flex-1 overflow-y-auto p-4 lg:p-6" ref="messagesContainer">
        <div v-if="!hasMessages" class="flex items-center justify-center h-full">
          <div class="text-center text-gray-400 fade-in">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
              <span class="text-white font-bold text-2xl">W</span>
            </div>
            <h3 class="text-xl font-semibold mb-2">Welcome to WIDDX AI</h3>
            <p class="text-gray-500 text-sm sm:text-base px-4">Start a conversation by typing a message below</p>
          </div>
        </div>

        <div v-else class="space-y-4 lg:space-y-6 max-w-4xl mx-auto">
          <TransitionGroup name="message" tag="div">
            <ChatMessage
              v-for="message in currentMessages"
              :key="message.id"
              :message="message"
              @edit="handleEditMessage"
              class="fade-in"
            />
          </TransitionGroup>
        </div>
      </div>

      <!-- Input Area -->
      <div class="bg-gray-800 border-t border-gray-700 p-4 lg:p-6">
        <div class="max-w-4xl mx-auto">
          <ChatInput
            @send="handleSendMessage"
            :disabled="isLoading"
            :placeholder="isLoading ? 'WIDDX is processing...' : 'Type your message...'"
          />

          <!-- Error Display -->
          <Transition name="error">
            <div v-if="error" class="mt-3 p-3 bg-red-900/50 border border-red-700 rounded-lg">
              <div class="flex items-center justify-between">
                <span class="text-red-200 text-sm">{{ error }}</span>
                <button @click="clearError" class="text-red-400 hover:text-red-300 transition-colors">
                  <XMarkIcon class="w-4 h-4" />
                </button>
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { PlusIcon, XMarkIcon } from '@heroicons/vue/24/outline';
import { useChatStore } from '../stores/chatStore';
import ChatMessage from '../components/ChatMessage.vue';
import ChatInput from '../components/ChatInput.vue';

const chatStore = useChatStore();
const {
  conversations,
  currentConversation,
  currentMessages,
  hasMessages,
  isLoading,
  isTyping,
  error
} = storeToRefs(chatStore);

const messagesContainer = ref(null);
const showMobileSidebar = ref(false);

// Methods
const startNewChat = () => {
  chatStore.createNewConversation();
};

const selectConversation = async (conversation) => {
  if (currentConversation.value?.id === conversation.id) return;

  try {
    await chatStore.loadConversation(conversation.id);
    scrollToBottom();
  } catch (err) {
    console.error('Failed to load conversation:', err);
  }
};

const handleSendMessage = async (content) => {
  try {
    await chatStore.sendMessage(content);
    scrollToBottom();
  } catch (err) {
    console.error('Failed to send message:', err);
  }
};

const handleEditMessage = async (messageId, newContent) => {
  try {
    await chatStore.editAndResendMessage(messageId, newContent);
    scrollToBottom();
  } catch (err) {
    console.error('Failed to edit message:', err);
  }
};

const clearError = () => {
  chatStore.clearError();
};

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) return 'Today';
  if (diffDays === 2) return 'Yesterday';
  if (diffDays <= 7) return `${diffDays} days ago`;

  return date.toLocaleDateString();
};

// Watch for new messages to auto-scroll
watch(currentMessages, () => {
  scrollToBottom();
}, { deep: true });

// Load conversations on mount
onMounted(async () => {
  try {
    await chatStore.loadConversations();
  } catch (err) {
    console.error('Failed to load conversations:', err);
  }
});
</script>

<style scoped>
/* Message transitions */
.message-enter-active,
.message-leave-active {
  transition: all 0.5s ease;
}

.message-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.message-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.message-move {
  transition: transform 0.5s ease;
}

/* Error transitions */
.error-enter-active,
.error-leave-active {
  transition: all 0.3s ease;
}

.error-enter-from,
.error-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Mobile sidebar positioning */
@media (max-width: 1023px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
  }
}

/* Responsive conversation items */
@media (max-width: 640px) {
  .conversation-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .conversation-title {
    font-size: 0.875rem;
  }

  .conversation-date {
    font-size: 0.75rem;
  }
}
</style>
