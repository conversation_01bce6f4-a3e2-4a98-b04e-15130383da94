<!-- WIDDX AI Chat Area Component -->
<main id="widdx-chat-area" class="flex-1 flex flex-col relative">
    <!-- Messages Container -->
    <div id="widdx-messages-container" class="flex-1 overflow-y-auto widdx-scrollbar p-6 space-y-6">
        <!-- Welcome Screen -->
        <div id="widdx-welcome-screen" class="text-center py-16 animate-widdx-fade-in">
            <div class="relative mb-8">
                <!-- WIDDX AI Logo -->
                <div class="w-24 h-24 bg-gradient-to-br from-widdx-primary to-widdx-accent rounded-2xl flex items-center justify-center mx-auto shadow-2xl animate-widdx-gradient">
                    <span class="text-white text-4xl font-bold">W</span>
                </div>
                <!-- Status Badge -->
                <div class="absolute -top-2 -right-2 w-6 h-6 bg-widdx-success rounded-full border-4 border-widdx-dark animate-widdx-bounce-in flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>

            <!-- Welcome Text -->
            <h2 class="text-3xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                Welcome to WIDDX AI
            </h2>
            <p class="text-gray-400 text-lg mb-6">Your Advanced Intelligent Assistant</p>

            <!-- Model Badges -->
            <div class="flex flex-wrap justify-center gap-3 max-w-md mx-auto mb-8">
                <span class="px-3 py-1 bg-widdx-primary/20 text-blue-300 rounded-full text-sm font-medium">🧠 DeepSeek</span>
                <span class="px-3 py-1 bg-widdx-accent/20 text-purple-300 rounded-full text-sm font-medium">💎 Gemini</span>
                <span class="px-3 py-1 bg-widdx-success/20 text-green-300 rounded-full text-sm font-medium">🤗 HuggingFace</span>
            </div>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto mb-8">
                <div class="widdx-feature-card p-4 rounded-xl bg-widdx-glass border border-white/10">
                    <div class="w-8 h-8 bg-widdx-primary/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <svg class="w-4 h-4 text-widdx-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-sm font-medium text-white mb-1">Lightning Fast</h3>
                    <p class="text-xs text-gray-400">Instant responses powered by advanced AI</p>
                </div>
                <div class="widdx-feature-card p-4 rounded-xl bg-widdx-glass border border-white/10">
                    <div class="w-8 h-8 bg-widdx-accent/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <svg class="w-4 h-4 text-widdx-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-sm font-medium text-white mb-1">Smart & Adaptive</h3>
                    <p class="text-xs text-gray-400">Learns from context and preferences</p>
                </div>
                <div class="widdx-feature-card p-4 rounded-xl bg-widdx-glass border border-white/10">
                    <div class="w-8 h-8 bg-widdx-success/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <svg class="w-4 h-4 text-widdx-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-sm font-medium text-white mb-1">Secure & Private</h3>
                    <p class="text-xs text-gray-400">Your data stays protected and private</p>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-gray-500 text-sm">
                <p>Start a conversation by typing your message below</p>
                <p class="text-xs mt-1 opacity-75">Press <kbd class="px-1 py-0.5 bg-widdx-glass rounded text-xs">Enter</kbd> to send, <kbd class="px-1 py-0.5 bg-widdx-glass rounded text-xs">Shift+Enter</kbd> for new line</p>
            </div>
        </div>
    </div>

    <!-- Typing Indicator -->
    <div id="widdx-typing-indicator" class="hidden px-6 py-2 animate-widdx-fade-in">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-widdx-primary to-widdx-accent rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-bold">W</span>
            </div>
            <div class="widdx-message-bubble bg-widdx-glass px-4 py-3 rounded-2xl border border-white/10">
                <div class="flex space-x-1">
                    <div class="widdx-typing-dot w-2 h-2 bg-gray-400 rounded-full animate-widdx-typing"></div>
                    <div class="widdx-typing-dot w-2 h-2 bg-gray-400 rounded-full animate-widdx-typing" style="animation-delay: 0.2s;"></div>
                    <div class="widdx-typing-dot w-2 h-2 bg-gray-400 rounded-full animate-widdx-typing" style="animation-delay: 0.4s;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Input Area -->
    @include('components.input-area')
</main>

<!-- Message Template -->
<template id="widdx-message-template">
    <div class="widdx-message-container animate-widdx-fade-in flex mb-6 group" data-message-id="">
        <div class="flex items-start space-x-3 max-w-4xl w-full">
            <!-- Avatar -->
            <div class="widdx-message-avatar w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0"></div>
            
            <!-- Message Content -->
            <div class="flex-1 min-w-0">
                <!-- Message Bubble -->
                <div class="widdx-message-bubble rounded-2xl px-6 py-4 shadow-lg border border-white/10">
                    <!-- Header -->
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-sm font-medium flex items-center space-x-2">
                            <span class="widdx-message-sender"></span>
                            <span class="widdx-message-model hidden px-2 py-1 bg-black/20 rounded-full text-xs"></span>
                        </div>
                        <div class="text-xs text-gray-400 widdx-message-time"></div>
                    </div>
                    
                    <!-- Content -->
                    <div class="widdx-message-content leading-relaxed"></div>
                </div>

                <!-- Message Actions -->
                <div class="widdx-message-actions flex items-center space-x-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <button class="widdx-action-btn widdx-copy-btn p-1.5 rounded-lg hover:bg-widdx-primary/20 hover:text-widdx-primary transition-colors" title="Copy">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                    <button class="widdx-action-btn widdx-regenerate-btn hidden p-1.5 rounded-lg hover:bg-widdx-accent/20 hover:text-widdx-accent transition-colors" title="Regenerate">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </button>
                    <button class="widdx-action-btn widdx-like-btn p-1.5 rounded-lg hover:bg-widdx-success/20 hover:text-widdx-success transition-colors" title="Like">
                        👍
                    </button>
                    <button class="widdx-action-btn widdx-dislike-btn p-1.5 rounded-lg hover:bg-widdx-error/20 hover:text-widdx-error transition-colors" title="Dislike">
                        👎
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
