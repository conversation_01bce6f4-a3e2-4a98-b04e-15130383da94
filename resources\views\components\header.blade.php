<!-- WIDDX AI Header Component -->
<header class="widdx-header glass border-b border-white/10 p-4 sticky top-0 z-50">
    <div class="flex items-center justify-between">
        <!-- Brand Section -->
        <div class="flex items-center space-x-4">
            <div class="relative">
                <!-- WIDDX AI Logo -->
                <div class="w-12 h-12 bg-gradient-to-br from-widdx-primary to-widdx-accent rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <span class="text-white font-bold text-xl">W</span>
                </div>
                <!-- Status Indicator -->
                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-widdx-success rounded-full border-2 border-widdx-dark animate-widdx-pulse-slow"></div>
            </div>
            <div>
                <h1 class="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    WIDDX AI
                </h1>
                <p class="text-gray-400 text-sm flex items-center">
                    <span class="w-2 h-2 bg-widdx-success rounded-full mr-2 animate-pulse"></span>
                    Advanced Intelligent Assistant
                </p>
            </div>
        </div>

        <!-- Controls Section -->
        <div class="flex items-center space-x-3">
            <!-- Model Selector -->
            <div class="relative">
                <select id="widdx-model-selector" class="widdx-select bg-gray-800/70 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 backdrop-blur-sm">
                    <option value="auto">🤖 Auto Select</option>
                    <option value="deepseek">🧠 DeepSeek</option>
                    <option value="gemini">💎 Gemini</option>
                    <option value="huggingface">🤗 HuggingFace</option>
                </select>
            </div>

            <!-- Theme Toggle -->
            <button id="widdx-theme-toggle" class="widdx-btn-icon p-2 rounded-lg bg-widdx-glass hover:bg-widdx-secondary/70 transition-colors" title="Toggle Theme">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                </svg>
            </button>

            <!-- Settings Button -->
            <button id="widdx-settings-btn" class="widdx-btn-icon p-2 rounded-lg bg-widdx-glass hover:bg-widdx-secondary/70 transition-colors" title="Settings">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </button>

            <!-- Mobile Menu Toggle -->
            <button id="widdx-mobile-menu" class="widdx-btn-icon p-2 rounded-lg bg-widdx-glass hover:bg-widdx-secondary/70 transition-colors lg:hidden" title="Menu">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Mobile Navigation (Hidden by default) -->
    <div id="widdx-mobile-nav" class="hidden lg:hidden mt-4 pt-4 border-t border-white/10">
        <div class="flex flex-col space-y-2">
            <button class="widdx-mobile-nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-widdx-secondary/50 transition-colors">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <span class="text-white">New Chat</span>
            </button>
            <button class="widdx-mobile-nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-widdx-secondary/50 transition-colors">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-white">History</span>
            </button>
        </div>
    </div>
</header>

<!-- Status Bar -->
<div id="widdx-status-bar" class="hidden bg-widdx-success/20 border-b border-widdx-success/30 px-4 py-2">
    <div class="flex items-center justify-center space-x-2 text-widdx-success text-sm">
        <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        <span id="widdx-status-text">Connected to WIDDX AI</span>
    </div>
</div>
