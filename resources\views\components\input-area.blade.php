<!-- WIDDX AI Input Area Component -->
<div id="widdx-input-area" class="widdx-input-area bg-gray-900/70 backdrop-blur-xl border-t border-white/10 p-6">
    <!-- Quick Actions Bar -->
    <div id="widdx-quick-actions" class="flex space-x-2 mb-4 overflow-x-auto pb-2 scrollbar-hide">
        <button class="widdx-quick-action-btn flex items-center space-x-2 px-3 py-2 bg-widdx-glass border border-white/10 rounded-xl text-gray-300 hover:text-widdx-primary hover:border-widdx-primary/30 transition-all duration-200 whitespace-nowrap" data-action="explain">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm font-medium">Explain</span>
        </button>

        <button class="widdx-quick-action-btn flex items-center space-x-2 px-3 py-2 bg-widdx-glass border border-white/10 rounded-xl text-gray-300 hover:text-widdx-primary hover:border-widdx-primary/30 transition-all duration-200 whitespace-nowrap" data-action="code">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
            </svg>
            <span class="text-sm font-medium">Code</span>
        </button>

        <button class="widdx-quick-action-btn flex items-center space-x-2 px-3 py-2 bg-widdx-glass border border-white/10 rounded-xl text-gray-300 hover:text-widdx-primary hover:border-widdx-primary/30 transition-all duration-200 whitespace-nowrap" data-action="translate">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
            </svg>
            <span class="text-sm font-medium">Translate</span>
        </button>

        <button class="widdx-quick-action-btn flex items-center space-x-2 px-3 py-2 bg-widdx-glass border border-white/10 rounded-xl text-gray-300 hover:text-widdx-primary hover:border-widdx-primary/30 transition-all duration-200 whitespace-nowrap" data-action="summarize">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span class="text-sm font-medium">Summarize</span>
        </button>

        <button class="widdx-quick-action-btn flex items-center space-x-2 px-3 py-2 bg-widdx-glass border border-white/10 rounded-xl text-gray-300 hover:text-widdx-primary hover:border-widdx-primary/30 transition-all duration-200 whitespace-nowrap" data-action="analyze">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <span class="text-sm font-medium">Analyze</span>
        </button>
    </div>

    <!-- Main Input Form -->
    <form id="widdx-message-form" class="relative">
        <div class="flex space-x-3">
            <!-- Text Input Container -->
            <div class="flex-1 relative">
                <!-- File Upload Area (Hidden) -->
                <div id="widdx-file-upload-area" class="hidden mb-3 p-4 border-2 border-dashed border-gray-600 rounded-xl bg-widdx-glass">
                    <div class="text-center">
                        <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <p class="text-sm text-gray-400">Drop files here or click to upload</p>
                    </div>
                </div>

                <!-- Textarea -->
                <textarea
                    id="widdx-message-input"
                    placeholder="Type your message here... (Shift+Enter for new line)"
                    class="w-full widdx-textarea bg-widdx-glass border border-gray-600 rounded-2xl px-6 py-4 pr-16 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-widdx-primary focus:border-transparent transition-all duration-200"
                    rows="1"
                    style="min-height: 56px; max-height: 200px;"
                    maxlength="4000"
                ></textarea>

                <!-- Input Actions -->
                <div class="absolute right-3 bottom-3 flex items-center space-x-2">
                    <!-- File Attachment -->
                    <button type="button" id="widdx-attach-btn" class="widdx-input-action-btn p-2 text-gray-400 hover:text-white hover:bg-widdx-secondary/50 rounded-lg transition-colors" title="Attach File">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                        </svg>
                    </button>

                    <!-- Voice Input -->
                    <button type="button" id="widdx-voice-btn" class="widdx-input-action-btn p-2 text-gray-400 hover:text-white hover:bg-widdx-secondary/50 rounded-lg transition-colors" title="Voice Input">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                        </svg>
                    </button>
                </div>

                <!-- Hidden File Input -->
                <input type="file" id="widdx-file-input" class="hidden" multiple accept="image/*,.pdf,.doc,.docx,.txt">
            </div>

            <!-- Send Button -->
            <button
                type="submit"
                id="widdx-send-btn"
                class="widdx-send-btn bg-gradient-to-r from-widdx-primary to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white p-4 rounded-2xl transition-all duration-200 flex items-center justify-center min-w-[56px] shadow-lg hover:shadow-xl"
                disabled
            >
                <!-- Send Icon -->
                <span id="widdx-send-icon">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </span>

                <!-- Loading Spinner -->
                <div id="widdx-send-spinner" class="hidden animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full"></div>
            </button>
        </div>

        <!-- Input Footer -->
        <div class="flex justify-between items-center mt-3 text-xs text-gray-500">
            <!-- Character Count -->
            <div class="flex items-center space-x-4">
                <span id="widdx-char-count">0 / 4000</span>
                <span id="widdx-word-count" class="hidden">0 words</span>
            </div>

            <!-- Keyboard Shortcuts -->
            <div class="flex items-center space-x-2">
                <span class="hidden sm:flex items-center space-x-1">
                    <kbd class="px-2 py-1 bg-widdx-glass rounded text-xs border border-white/10">Enter</kbd>
                    <span>send</span>
                </span>
                <span class="hidden sm:flex items-center space-x-1">
                    <kbd class="px-2 py-1 bg-widdx-glass rounded text-xs border border-white/10">Shift</kbd>
                    <span>+</span>
                    <kbd class="px-2 py-1 bg-widdx-glass rounded text-xs border border-white/10">Enter</kbd>
                    <span>new line</span>
                </span>
            </div>
        </div>
    </form>

    <!-- Error Message -->
    <div id="widdx-error-message" class="hidden mt-4 p-4 bg-widdx-error/20 border border-widdx-error/50 rounded-xl text-widdx-error text-sm animate-widdx-slide-up">
        <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span id="widdx-error-text"></span>
            <button onclick="WIDDX.UI.hideError()" class="ml-auto text-widdx-error hover:text-red-300 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Success Message -->
    <div id="widdx-success-message" class="hidden mt-4 p-4 bg-widdx-success/20 border border-widdx-success/50 rounded-xl text-widdx-success text-sm animate-widdx-slide-up">
        <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span id="widdx-success-text"></span>
        </div>
    </div>
</div>
