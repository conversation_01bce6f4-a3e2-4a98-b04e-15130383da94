@extends('layouts.app')

@section('title', 'WIDDX AI - Advanced Intelligent Assistant')

@section('content')
<!-- WIDDX AI Chat Interface -->
<div class="widdx-chat-container h-screen flex flex-col">
    <!-- Header Component -->
    @include('components.header')

    <!-- Main Chat Layout -->
    <div class="flex flex-1 overflow-hidden">
        <!-- Sidebar Component -->
        @include('components.sidebar')

        <!-- Chat Area Component -->
        @include('components.chat-area')
    </div>
</div>

@endsection

@push('scripts')
<script>
    // Initialize WIDDX AI with enhanced features
    document.addEventListener('DOMContentLoaded', function() {
        // Enable debug mode in development
        WIDDX.config.debug = {{ config('app.debug') ? 'true' : 'false' }};

        // Set API base URL
        WIDDX.config.apiBase = '{{ url('/api') }}';

        // Initialize the application
        WIDDX.init();
    });
</script>
@endpush


