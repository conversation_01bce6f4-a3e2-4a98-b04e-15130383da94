<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>WIDDX AI Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.6/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        secondary: '#1e293b',
                        dark: '#0f172a',
                        accent: '#8b5cf6',
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'bounce-in': 'bounceIn 0.5s ease-out',
                        'typing': 'typing 1.4s infinite',
                        'pulse-slow': 'pulse 2s infinite',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }

        /* Advanced Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        /* Custom Scrollbar */
        .custom-scrollbar::-webkit-scrollbar { width: 6px; }
        .custom-scrollbar::-webkit-scrollbar-track { background: #1e293b; border-radius: 3px; }
        .custom-scrollbar::-webkit-scrollbar-thumb { background: #475569; border-radius: 3px; }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover { background: #64748b; }

        /* Message Bubble Effects */
        .message-bubble {
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Typing Indicator */
        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #64748b;
            animation: typing 1.4s infinite;
        }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        /* Code Block Styling */
        .code-block {
            background: #1e293b !important;
            border: 1px solid #334155;
            border-radius: 8px;
        }

        /* Glassmorphism Effect */
        .glass {
            background: rgba(30, 41, 59, 0.7);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Hover Effects */
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        /* Message Actions */
        .message-actions {
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.2s ease;
        }
        .message-container:hover .message-actions {
            opacity: 1;
            transform: translateY(0);
        }

        /* Quick Action Buttons */
        .quick-action-btn {
            display: flex;
            align-items: center;
            space-x: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(30, 41, 59, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            color: #94a3b8;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        .quick-action-btn:hover {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.3);
            color: #3b82f6;
            transform: translateY(-1px);
        }

        /* Message Reactions */
        .reaction-btn {
            padding: 0.25rem 0.5rem;
            background: rgba(30, 41, 59, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            font-size: 0.75rem;
            transition: all 0.2s ease;
        }
        .reaction-btn:hover {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.3);
            transform: scale(1.1);
        }

        /* Copy Button Animation */
        .copy-success {
            background: rgba(16, 185, 129, 0.2) !important;
            border-color: rgba(16, 185, 129, 0.3) !important;
            color: #10b981 !important;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .quick-action-btn {
                padding: 0.375rem 0.75rem;
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body class="bg-dark text-white min-h-screen">
    <div class="container mx-auto max-w-7xl h-screen flex flex-col">
        <!-- Advanced Header -->
        <header class="glass border-b border-white/10 p-4 sticky top-0 z-50">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-accent rounded-xl flex items-center justify-center shadow-lg">
                            <span class="text-white font-bold text-xl">W</span>
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-success rounded-full border-2 border-dark animate-pulse"></div>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                            WIDDX AI Assistant
                        </h1>
                        <p class="text-gray-400 text-sm flex items-center">
                            <span class="w-2 h-2 bg-success rounded-full mr-2 animate-pulse"></span>
                            Intelligent Multi-Model Assistant
                        </p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Model Selector -->
                    <select id="modelSelector" class="bg-secondary/50 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-primary-500">
                        <option value="auto">Auto Select</option>
                        <option value="deepseek">DeepSeek</option>
                        <option value="gemini">Gemini</option>
                        <option value="huggingface">HuggingFace</option>
                    </select>
                    <!-- Settings Button -->
                    <button id="settingsBtn" class="p-2 rounded-lg bg-secondary/50 hover:bg-secondary/70 transition-colors">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Advanced Layout -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Enhanced Sidebar -->
            <aside id="sidebar" class="w-80 glass border-r border-white/10 hidden lg:flex flex-col">
                <div class="p-4 border-b border-white/10">
                    <button id="newChatBtn" class="w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-4 py-3 rounded-xl transition-all duration-200 hover-lift flex items-center justify-center space-x-2 group">
                        <svg class="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        <span class="font-medium">New Chat</span>
                    </button>
                </div>

                <!-- Search Bar -->
                <div class="p-4 border-b border-white/10">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="Search conversations..."
                               class="w-full bg-secondary/50 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <div class="flex-1 overflow-y-auto custom-scrollbar">
                    <div id="conversationsList" class="p-2">
                        <div class="text-center text-gray-400 py-12 animate-fade-in">
                            <div class="w-16 h-16 bg-secondary/50 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                            <p class="text-sm">No conversations yet</p>
                            <p class="text-xs text-gray-500 mt-1">Start a new chat to begin</p>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Enhanced Main Chat Area -->
            <main class="flex-1 flex flex-col relative">
                <!-- Messages Container -->
                <div id="messagesContainer" class="flex-1 overflow-y-auto custom-scrollbar p-6 space-y-6">
                    <!-- Enhanced Welcome Message -->
                    <div class="text-center py-16 animate-fade-in">
                        <div class="relative mb-8">
                            <div class="w-24 h-24 bg-gradient-to-br from-primary-500 to-accent rounded-2xl flex items-center justify-center mx-auto shadow-2xl">
                                <span class="text-white text-4xl font-bold">W</span>
                            </div>
                            <div class="absolute -top-2 -right-2 w-6 h-6 bg-success rounded-full border-4 border-dark animate-bounce-in"></div>
                        </div>
                        <h2 class="text-3xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                            Welcome to WIDDX AI
                        </h2>
                        <p class="text-gray-400 text-lg mb-6">Your intelligent multi-model assistant</p>
                        <div class="flex flex-wrap justify-center gap-3 max-w-md mx-auto">
                            <span class="px-3 py-1 bg-primary-500/20 text-primary-300 rounded-full text-sm">DeepSeek</span>
                            <span class="px-3 py-1 bg-accent/20 text-purple-300 rounded-full text-sm">Gemini</span>
                            <span class="px-3 py-1 bg-success/20 text-green-300 rounded-full text-sm">HuggingFace</span>
                        </div>
                        <div class="mt-8 text-gray-500 text-sm">
                            <p>Start a conversation by typing your message below</p>
                        </div>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div id="typingIndicator" class="hidden px-6 py-2">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">W</span>
                        </div>
                        <div class="message-bubble bg-secondary/50 px-4 py-3 rounded-2xl">
                            <div class="flex space-x-1">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Input Area -->
                <div class="glass border-t border-white/10 p-6">
                    <!-- Quick Actions -->
                    <div id="quickActions" class="flex space-x-2 mb-4 overflow-x-auto pb-2">
                        <button class="quick-action-btn" data-action="explain">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Explain
                        </button>
                        <button class="quick-action-btn" data-action="code">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                            </svg>
                            Code
                        </button>
                        <button class="quick-action-btn" data-action="translate">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                            </svg>
                            Translate
                        </button>
                        <button class="quick-action-btn" data-action="summarize">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Summarize
                        </button>
                    </div>

                    <form id="messageForm" class="relative">
                        <div class="flex space-x-3">
                            <div class="flex-1 relative">
                                <textarea
                                    id="messageInput"
                                    placeholder="Type your message here... (Shift+Enter for new line)"
                                    class="w-full bg-secondary/50 border border-gray-600 rounded-2xl px-6 py-4 pr-12 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                                    rows="1"
                                    style="min-height: 56px; max-height: 200px;"
                                ></textarea>

                                <!-- Attachment Button -->
                                <button type="button" id="attachBtn" class="absolute right-3 bottom-3 p-2 text-gray-400 hover:text-white transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                    </svg>
                                </button>
                            </div>

                            <button
                                type="submit"
                                id="sendBtn"
                                class="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white p-4 rounded-2xl transition-all duration-200 hover-lift flex items-center justify-center min-w-[56px]"
                            >
                                <span id="sendBtnIcon">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                </span>
                                <div id="sendBtnSpinner" class="hidden animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full"></div>
                            </button>
                        </div>

                        <!-- Character Count -->
                        <div class="flex justify-between items-center mt-2 text-xs text-gray-500">
                            <span id="charCount">0 characters</span>
                            <span class="flex items-center space-x-1">
                                <kbd class="px-2 py-1 bg-secondary/50 rounded text-xs">Shift</kbd>
                                <span>+</span>
                                <kbd class="px-2 py-1 bg-secondary/50 rounded text-xs">Enter</kbd>
                                <span>for new line</span>
                            </span>
                        </div>
                    </form>

                    <div id="errorMessage" class="hidden mt-4 p-4 bg-error/20 border border-error/50 rounded-xl text-error text-sm animate-slide-up"></div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Global variables
        let currentConversationId = null;
        let isLoading = false;
        let messageHistory = [];
        let typingTimeout = null;

        // DOM elements
        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const sendBtnIcon = document.getElementById('sendBtnIcon');
        const sendBtnSpinner = document.getElementById('sendBtnSpinner');
        const messagesContainer = document.getElementById('messagesContainer');
        const conversationsList = document.getElementById('conversationsList');
        const errorMessage = document.getElementById('errorMessage');
        const newChatBtn = document.getElementById('newChatBtn');
        const typingIndicator = document.getElementById('typingIndicator');
        const charCount = document.getElementById('charCount');
        const modelSelector = document.getElementById('modelSelector');
        const quickActionBtns = document.querySelectorAll('.quick-action-btn');

        // API Base URL
        const API_BASE = '/api';

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadConversations();
            setupEventListeners();
            autoResizeTextarea();
            initializeMarkdown();
            initializeHighlighting();
        });

        // Initialize Markdown and Code Highlighting
        function initializeMarkdown() {
            marked.setOptions({
                highlight: function(code, lang) {
                    if (lang && hljs.getLanguage(lang)) {
                        return hljs.highlight(code, { language: lang }).value;
                    }
                    return hljs.highlightAuto(code).value;
                },
                breaks: true,
                gfm: true
            });
        }

        function initializeHighlighting() {
            hljs.configure({
                classPrefix: 'hljs-',
                languages: ['javascript', 'python', 'java', 'cpp', 'html', 'css', 'sql', 'json', 'xml']
            });
        }

        // Enhanced Event listeners
        function setupEventListeners() {
            messageForm.addEventListener('submit', handleSubmit);
            newChatBtn.addEventListener('click', startNewChat);
            messageInput.addEventListener('keydown', handleKeyDown);
            messageInput.addEventListener('input', handleInputChange);
            modelSelector.addEventListener('change', handleModelChange);

            // Quick action buttons
            quickActionBtns.forEach(btn => {
                btn.addEventListener('click', handleQuickAction);
            });

            // Search functionality
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', handleSearch);
            }
        }

        // Handle input changes
        function handleInputChange() {
            autoResizeTextarea();
            updateCharCount();

            // Show typing indicator (simulated)
            if (messageInput.value.trim()) {
                clearTimeout(typingTimeout);
                typingTimeout = setTimeout(() => {
                    // Hide typing indicator after user stops typing
                }, 1000);
            }
        }

        // Update character count
        function updateCharCount() {
            const count = messageInput.value.length;
            charCount.textContent = `${count} characters`;

            if (count > 2000) {
                charCount.classList.add('text-warning');
            } else if (count > 4000) {
                charCount.classList.add('text-error');
            } else {
                charCount.classList.remove('text-warning', 'text-error');
            }
        }

        // Handle model selection
        function handleModelChange() {
            const selectedModel = modelSelector.value;
            console.log('Model changed to:', selectedModel);
            // You can add model-specific logic here
        }

        // Handle quick actions
        function handleQuickAction(e) {
            const action = e.currentTarget.dataset.action;
            const prompts = {
                explain: 'Please explain this in simple terms: ',
                code: 'Write code for: ',
                translate: 'Translate this to English: ',
                summarize: 'Summarize this: '
            };

            if (prompts[action]) {
                messageInput.value = prompts[action];
                messageInput.focus();
                autoResizeTextarea();
                updateCharCount();
            }
        }

        // Handle search
        function handleSearch(e) {
            const query = e.target.value.toLowerCase();
            const conversations = document.querySelectorAll('.conversation-item');

            conversations.forEach(conv => {
                const title = conv.querySelector('.conversation-title')?.textContent.toLowerCase() || '';
                if (title.includes(query)) {
                    conv.style.display = 'block';
                } else {
                    conv.style.display = 'none';
                }
            });
        }

        // Enhanced form submission
        async function handleSubmit(e) {
            e.preventDefault();

            const message = messageInput.value.trim();
            if (!message || isLoading) return;

            setLoading(true);
            hideError();

            // Add user message to UI with animation
            const userMessageId = addMessage(message, 'user');
            messageHistory.push({ role: 'user', content: message, id: userMessageId });

            // Clear input and reset
            messageInput.value = '';
            autoResizeTextarea();
            updateCharCount();

            // Show typing indicator
            showTypingIndicator();

            try {
                const selectedModel = modelSelector.value;
                const response = await fetch(`${API_BASE}/ask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        message: message,
                        conversation_id: currentConversationId,
                        use_knowledge_base: true,
                        preferred_service: selectedModel === 'auto' ? null : selectedModel,
                        knowledge_threshold: 0.7,
                        max_knowledge_results: 5,
                        tags: [],
                        source_filter: null,
                        context: messageHistory.slice(-10) // Last 10 messages for context
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // Hide typing indicator
                    hideTypingIndicator();

                    // Add AI response to UI with markdown support
                    const assistantMessageId = addMessage(data.data.message.content, 'assistant', {
                        model: data.data.model || selectedModel,
                        timestamp: new Date().toISOString()
                    });

                    messageHistory.push({
                        role: 'assistant',
                        content: data.data.message.content,
                        id: assistantMessageId
                    });

                    // Update conversation ID
                    if (data.data.message.conversation_id) {
                        currentConversationId = data.data.message.conversation_id;
                    }

                    // Reload conversations list
                    loadConversations();

                    // Scroll to bottom
                    scrollToBottom();
                } else {
                    hideTypingIndicator();
                    showError(data.message || 'Error sending message');
                }
            } catch (error) {
                console.error('Error:', error);
                hideTypingIndicator();
                showError('Network connection error');
            } finally {
                setLoading(false);
            }
        }

        // Show typing indicator
        function showTypingIndicator() {
            typingIndicator.classList.remove('hidden');
            scrollToBottom();
        }

        // Hide typing indicator
        function hideTypingIndicator() {
            typingIndicator.classList.add('hidden');
        }

        // Scroll to bottom with smooth animation
        function scrollToBottom() {
            setTimeout(() => {
                messagesContainer.scrollTo({
                    top: messagesContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }, 100);
        }
        // Handle keyboard shortcuts
        function handleKeyDown(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
            }
        }

        // Auto-resize textarea
        function autoResizeTextarea() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // Set loading state
        function setLoading(loading) {
            isLoading = loading;
            sendBtn.disabled = loading;

            if (loading) {
                sendBtnText.classList.add('hidden');
                sendBtnSpinner.classList.remove('hidden');
            } else {
                sendBtnText.classList.remove('hidden');
                sendBtnSpinner.classList.add('hidden');
            }
        }

        // Enhanced message addition with markdown support
        function addMessage(content, role, metadata = {}) {
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-container animate-fade-in flex ${role === 'user' ? 'justify-end' : 'justify-start'} mb-6`;
            messageDiv.dataset.messageId = messageId;

            const isUser = role === 'user';
            const avatar = isUser ?
                `<div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-sm font-bold">U</div>` :
                `<div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent rounded-full flex items-center justify-center text-white text-sm font-bold">W</div>`;

            const timestamp = new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            // Process content based on role
            let processedContent;
            if (isUser) {
                processedContent = escapeHtml(content);
            } else {
                // Parse markdown for AI responses
                processedContent = marked.parse(content);
            }

            messageDiv.innerHTML = `
                <div class="flex ${isUser ? 'flex-row-reverse' : 'flex-row'} items-start space-x-3 ${isUser ? 'space-x-reverse' : ''} max-w-4xl">
                    ${avatar}
                    <div class="flex-1 min-w-0">
                        <div class="message-bubble ${isUser ? 'bg-gradient-to-br from-primary-500 to-primary-600' : 'bg-secondary/70'} rounded-2xl px-6 py-4 shadow-lg">
                            <div class="flex items-center justify-between mb-2">
                                <div class="text-sm font-medium ${isUser ? 'text-white/90' : 'text-gray-300'}">
                                    ${isUser ? 'You' : 'WIDDX AI'}
                                    ${metadata.model ? `<span class="ml-2 px-2 py-1 bg-black/20 rounded-full text-xs">${metadata.model}</span>` : ''}
                                </div>
                                <div class="text-xs ${isUser ? 'text-white/70' : 'text-gray-400'}">${timestamp}</div>
                            </div>
                            <div class="message-content ${isUser ? 'text-white' : 'text-gray-100'} leading-relaxed">
                                ${processedContent}
                            </div>
                        </div>

                        <!-- Message Actions -->
                        <div class="message-actions flex items-center space-x-2 mt-2 ${isUser ? 'justify-end' : 'justify-start'}">
                            <button onclick="copyMessage('${messageId}')" class="reaction-btn hover:bg-primary-500/20 hover:text-primary-400" title="Copy">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                            ${!isUser ? `
                                <button onclick="regenerateMessage('${messageId}')" class="reaction-btn hover:bg-accent/20 hover:text-purple-400" title="Regenerate">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </button>
                            ` : ''}
                            <button onclick="addReaction('${messageId}', '👍')" class="reaction-btn hover:bg-success/20 hover:text-green-400" title="Like">👍</button>
                            <button onclick="addReaction('${messageId}', '👎')" class="reaction-btn hover:bg-error/20 hover:text-red-400" title="Dislike">👎</button>
                        </div>
                    </div>
                </div>
            `;

            // Remove welcome message if exists
            const welcomeMsg = messagesContainer.querySelector('.text-center.py-16');
            if (welcomeMsg) {
                welcomeMsg.remove();
            }

            messagesContainer.appendChild(messageDiv);

            // Highlight code blocks
            if (!isUser) {
                setTimeout(() => {
                    messageDiv.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                        block.classList.add('code-block');
                    });
                }, 100);
            }

            scrollToBottom();
            return messageId;
        }

        // Utility function to escape HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Copy message content
        function copyMessage(messageId) {
            const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
            const content = messageDiv.querySelector('.message-content').textContent;

            navigator.clipboard.writeText(content).then(() => {
                const copyBtn = messageDiv.querySelector('button[onclick*="copyMessage"]');
                copyBtn.classList.add('copy-success');
                setTimeout(() => {
                    copyBtn.classList.remove('copy-success');
                }, 2000);
            });
        }

        // Add reaction to message
        function addReaction(messageId, emoji) {
            const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
            const actionsDiv = messageDiv.querySelector('.message-actions');

            // Check if reaction already exists
            let reactionBtn = actionsDiv.querySelector(`[data-reaction="${emoji}"]`);
            if (!reactionBtn) {
                reactionBtn = document.createElement('span');
                reactionBtn.className = 'reaction-btn';
                reactionBtn.dataset.reaction = emoji;
                reactionBtn.dataset.count = '0';
                reactionBtn.innerHTML = `${emoji} <span class="count">0</span>`;
                actionsDiv.appendChild(reactionBtn);
            }

            // Increment count
            const countSpan = reactionBtn.querySelector('.count');
            const currentCount = parseInt(reactionBtn.dataset.count) + 1;
            reactionBtn.dataset.count = currentCount;
            countSpan.textContent = currentCount;
        }

        // Regenerate AI message
        function regenerateMessage(messageId) {
            // Implementation for regenerating AI response
            console.log('Regenerating message:', messageId);
            // You can implement this to resend the last user message
        }

        // Load conversations
        async function loadConversations() {
            try {
                const response = await fetch(`${API_BASE}/conversations`);
                const data = await response.json();

                if (response.ok && data.success) {
                    displayConversations(data.data);
                }
            } catch (error) {
                console.error('Error loading conversations:', error);
            }
        }

        // Enhanced conversations display
        function displayConversations(conversations) {
            if (conversations.length === 0) {
                conversationsList.innerHTML = `
                    <div class="text-center text-gray-400 py-12 animate-fade-in">
                        <div class="w-16 h-16 bg-secondary/50 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <p class="text-sm">No conversations yet</p>
                        <p class="text-xs text-gray-500 mt-1">Start a new chat to begin</p>
                    </div>
                `;
                return;
            }

            conversationsList.innerHTML = conversations.map(conv => {
                const date = new Date(conv.created_at);
                const timeAgo = getTimeAgo(date);
                const isActive = conv.id === currentConversationId;

                return `
                    <div class="conversation-item p-4 rounded-xl hover:bg-secondary/50 cursor-pointer transition-all duration-200 hover-lift ${isActive ? 'bg-primary-500/20 border border-primary-500/30' : 'border border-transparent'} mb-2"
                         onclick="loadConversation(${conv.id})">
                        <div class="flex items-start justify-between">
                            <div class="flex-1 min-w-0">
                                <div class="conversation-title font-medium text-white truncate mb-1">${conv.title}</div>
                                <div class="flex items-center space-x-2 text-xs text-gray-400">
                                    <span class="flex items-center">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        ${conv.messages_count} messages
                                    </span>
                                    <span>•</span>
                                    <span>${timeAgo}</span>
                                </div>
                            </div>
                            ${isActive ? `
                                <div class="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Get time ago string
        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }

        // Load specific conversation
        async function loadConversation(conversationId) {
            try {
                const response = await fetch(`${API_BASE}/conversations/${conversationId}`);
                const data = await response.json();

                if (response.ok && data.success) {
                    currentConversationId = conversationId;
                    displayMessages(data.data.messages);

                    // Update active conversation in sidebar
                    document.querySelectorAll('.conversation-item').forEach(item => {
                        item.classList.remove('bg-secondary/70');
                    });
                    event.target.closest('.conversation-item').classList.add('bg-secondary/70');
                }
            } catch (error) {
                console.error('Error loading conversation:', error);
            }
        }

        // Display messages
        function displayMessages(messages) {
            messagesContainer.innerHTML = '';

            messages.forEach(message => {
                addMessage(message.content, message.role);
            });
        }

        // Enhanced new chat function
        function startNewChat() {
            currentConversationId = null;
            messageHistory = [];

            messagesContainer.innerHTML = `
                <div class="text-center py-16 animate-fade-in">
                    <div class="relative mb-8">
                        <div class="w-24 h-24 bg-gradient-to-br from-primary-500 to-accent rounded-2xl flex items-center justify-center mx-auto shadow-2xl">
                            <span class="text-white text-4xl font-bold">W</span>
                        </div>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-success rounded-full border-4 border-dark animate-bounce-in"></div>
                    </div>
                    <h2 class="text-3xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                        New Conversation
                    </h2>
                    <p class="text-gray-400 text-lg mb-6">Your intelligent multi-model assistant</p>
                    <div class="flex flex-wrap justify-center gap-3 max-w-md mx-auto">
                        <span class="px-3 py-1 bg-primary-500/20 text-primary-300 rounded-full text-sm">DeepSeek</span>
                        <span class="px-3 py-1 bg-accent/20 text-purple-300 rounded-full text-sm">Gemini</span>
                        <span class="px-3 py-1 bg-success/20 text-green-300 rounded-full text-sm">HuggingFace</span>
                    </div>
                    <div class="mt-8 text-gray-500 text-sm">
                        <p>Start a conversation by typing your message below</p>
                    </div>
                </div>
            `;

            // Remove active state from conversations
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('bg-primary-500/20', 'border-primary-500/30');
                item.classList.add('border-transparent');
            });

            // Focus on input
            messageInput.focus();
        }

        // Enhanced error handling
        function showError(message) {
            errorMessage.innerHTML = `
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-error flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>${message}</span>
                    <button onclick="hideError()" class="ml-auto text-error hover:text-red-300">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;
            errorMessage.classList.remove('hidden');

            // Auto hide after 8 seconds
            setTimeout(() => hideError(), 8000);
        }

        // Hide error message
        function hideError() {
            errorMessage.classList.add('hidden');
        }

        // Enhanced loading state
        function setLoading(loading) {
            isLoading = loading;
            sendBtn.disabled = loading;
            messageInput.disabled = loading;

            if (loading) {
                sendBtnIcon.classList.add('hidden');
                sendBtnSpinner.classList.remove('hidden');
                sendBtn.classList.add('cursor-not-allowed');
            } else {
                sendBtnIcon.classList.remove('hidden');
                sendBtnSpinner.classList.add('hidden');
                sendBtn.classList.remove('cursor-not-allowed');
            }
        }

        // Enhanced keyboard handling
        function handleKeyDown(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (!isLoading && messageInput.value.trim()) {
                    handleSubmit(e);
                }
            } else if (e.key === 'Escape') {
                messageInput.blur();
            } else if (e.key === '/' && e.ctrlKey) {
                e.preventDefault();
                startNewChat();
            }
        }

        // Auto-resize textarea with smooth animation
        function autoResizeTextarea() {
            messageInput.style.height = 'auto';
            const newHeight = Math.min(Math.max(messageInput.scrollHeight, 56), 200);
            messageInput.style.height = newHeight + 'px';

            // Adjust send button height to match
            sendBtn.style.height = newHeight + 'px';
        }

        // Initialize tooltips and other UI enhancements
        function initializeUI() {
            // Add keyboard shortcuts info
            document.addEventListener('keydown', (e) => {
                if (e.key === '?' && e.shiftKey) {
                    showKeyboardShortcuts();
                }
            });
        }

        // Show keyboard shortcuts modal (placeholder)
        function showKeyboardShortcuts() {
            console.log('Keyboard shortcuts:');
            console.log('Enter - Send message');
            console.log('Shift+Enter - New line');
            console.log('Ctrl+/ - New chat');
            console.log('Escape - Unfocus input');
            console.log('Shift+? - Show shortcuts');
        }

        // Call initialization
        initializeUI();
    </script>
    </script>
</body>
</html>
