@extends('layouts.app')

@section('title', 'WIDDX AI - Chat Interface')

@section('content')
<!-- WIDDX AI Chat Interface -->
<div class="widdx-chat-container h-screen flex flex-col">
    <!-- Header Component -->
    @include('components.header')

    <!-- Main Chat Layout -->
    <div class="flex flex-1 overflow-hidden">
        <!-- Sidebar Component -->
        @include('components.sidebar')

        <!-- Chat Area Component -->
        @include('components.chat-area')
    </div>
</div>

@endsection

@push('scripts')
<script>
    // Initialize chat-specific functionality
    document.addEventListener('DOMContentLoaded', function() {
        WIDDX.Chat.init();
    });
</script>
@endpush


