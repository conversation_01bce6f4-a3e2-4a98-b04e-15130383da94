<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>WIDDX AI Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E293B',
                        dark: '#0F172A',
                    }
                }
            }
        }
    </script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .typing-indicator { animation: pulse 1.5s ease-in-out infinite; }
        .message-fade-in { animation: fadeIn 0.3s ease-in; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
        .scrollbar-hide { -ms-overflow-style: none; scrollbar-width: none; }
        .scrollbar-hide::-webkit-scrollbar { display: none; }
    </style>
</head>
<body class="bg-dark text-white min-h-screen">
    <div class="container mx-auto max-w-4xl h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-secondary/50 backdrop-blur-sm border-b border-gray-700 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 rtl:space-x-reverse">
                    <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-lg">W</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">WIDDX AI Assistant</h1>
                        <p class="text-gray-400 text-sm">مساعد ذكي متعدد النماذج</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                    <span id="status" class="text-green-400 text-sm">متصل</span>
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>
            </div>
        </header>

        <!-- Conversations Sidebar (Hidden on mobile) -->
        <div class="flex flex-1 overflow-hidden">
            <aside id="sidebar" class="w-80 bg-secondary/30 border-r border-gray-700 hidden lg:flex flex-col">
                <div class="p-4 border-b border-gray-700">
                    <button id="newChatBtn" class="w-full bg-primary hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        محادثة جديدة
                    </button>
                </div>
                <div class="flex-1 overflow-y-auto scrollbar-hide">
                    <div id="conversationsList" class="p-2">
                        <div class="text-center text-gray-400 py-8">
                            <p>لا توجد محادثات بعد</p>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Main Chat Area -->
            <main class="flex-1 flex flex-col">
                <!-- Messages Container -->
                <div id="messagesContainer" class="flex-1 overflow-y-auto scrollbar-hide p-4 space-y-4">
                    <!-- Welcome Message -->
                    <div class="text-center py-12">
                        <div class="w-20 h-20 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-primary text-3xl font-bold">W</span>
                        </div>
                        <h2 class="text-2xl font-bold mb-2">مرحباً بك في WIDDX AI</h2>
                        <p class="text-gray-400">ابدأ محادثة بكتابة رسالتك أدناه</p>
                    </div>
                </div>

                <!-- Input Area -->
                <div class="border-t border-gray-700 p-4">
                    <form id="messageForm" class="flex space-x-2 rtl:space-x-reverse">
                        <div class="flex-1 relative">
                            <textarea
                                id="messageInput"
                                placeholder="اكتب رسالتك هنا..."
                                class="w-full bg-secondary border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                rows="1"
                                style="min-height: 44px; max-height: 120px;"
                            ></textarea>
                        </div>
                        <button
                            type="submit"
                            id="sendBtn"
                            class="bg-primary hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg transition-colors flex items-center justify-center"
                        >
                            <span id="sendBtnText">إرسال</span>
                            <div id="sendBtnSpinner" class="hidden animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                        </button>
                    </form>
                    <div id="errorMessage" class="hidden mt-2 p-3 bg-red-500/20 border border-red-500 rounded-lg text-red-400 text-sm"></div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Global variables
        let currentConversationId = null;
        let isLoading = false;

        // DOM elements
        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const sendBtnText = document.getElementById('sendBtnText');
        const sendBtnSpinner = document.getElementById('sendBtnSpinner');
        const messagesContainer = document.getElementById('messagesContainer');
        const conversationsList = document.getElementById('conversationsList');
        const errorMessage = document.getElementById('errorMessage');
        const newChatBtn = document.getElementById('newChatBtn');

        // API Base URL
        const API_BASE = '/api';

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadConversations();
            setupEventListeners();
            autoResizeTextarea();
        });

        // Event listeners
        function setupEventListeners() {
            messageForm.addEventListener('submit', handleSubmit);
            newChatBtn.addEventListener('click', startNewChat);
            messageInput.addEventListener('keydown', handleKeyDown);
            messageInput.addEventListener('input', autoResizeTextarea);
        }

        // Handle form submission
        async function handleSubmit(e) {
            e.preventDefault();

            const message = messageInput.value.trim();
            if (!message || isLoading) return;

            setLoading(true);
            hideError();

            // Add user message to UI
            addMessage(message, 'user');
            messageInput.value = '';
            autoResizeTextarea();

            try {
                const response = await fetch(`${API_BASE}/ask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        message: message,
                        conversation_id: currentConversationId,
                        use_knowledge_base: true,
                        preferred_service: null,
                        knowledge_threshold: 0.7,
                        max_knowledge_results: 5,
                        tags: [],
                        source_filter: null,
                        context: []
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // Add AI response to UI
                    addMessage(data.data.message.content, 'assistant');

                    // Update conversation ID
                    if (data.data.message.conversation_id) {
                        currentConversationId = data.data.message.conversation_id;
                    }

                    // Reload conversations list
                    loadConversations();
                } else {
                    showError(data.message || 'حدث خطأ أثناء إرسال الرسالة');
                }
            } catch (error) {
                console.error('Error:', error);
                showError('خطأ في الاتصال بالخادم');
            } finally {
                setLoading(false);
            }
        }
        // Handle keyboard shortcuts
        function handleKeyDown(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
            }
        }

        // Auto-resize textarea
        function autoResizeTextarea() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // Set loading state
        function setLoading(loading) {
            isLoading = loading;
            sendBtn.disabled = loading;

            if (loading) {
                sendBtnText.classList.add('hidden');
                sendBtnSpinner.classList.remove('hidden');
            } else {
                sendBtnText.classList.remove('hidden');
                sendBtnSpinner.classList.add('hidden');
            }
        }

        // Add message to UI
        function addMessage(content, role) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-fade-in flex ${role === 'user' ? 'justify-end' : 'justify-start'}`;

            const isUser = role === 'user';
            const bgColor = isUser ? 'bg-primary' : 'bg-secondary';
            const textAlign = isUser ? 'text-right' : 'text-left';

            messageDiv.innerHTML = `
                <div class="max-w-xs lg:max-w-md xl:max-w-lg ${bgColor} rounded-lg px-4 py-3 ${textAlign}">
                    <div class="text-sm text-gray-300 mb-1">${isUser ? 'أنت' : 'WIDDX AI'}</div>
                    <div class="text-white whitespace-pre-wrap">${content}</div>
                    <div class="text-xs text-gray-400 mt-1">${new Date().toLocaleTimeString('ar-SA')}</div>
                </div>
            `;

            // Remove welcome message if exists
            const welcomeMsg = messagesContainer.querySelector('.text-center.py-12');
            if (welcomeMsg) {
                welcomeMsg.remove();
            }

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Load conversations
        async function loadConversations() {
            try {
                const response = await fetch(`${API_BASE}/conversations`);
                const data = await response.json();

                if (response.ok && data.success) {
                    displayConversations(data.data);
                }
            } catch (error) {
                console.error('Error loading conversations:', error);
            }
        }

        // Display conversations in sidebar
        function displayConversations(conversations) {
            if (conversations.length === 0) {
                conversationsList.innerHTML = `
                    <div class="text-center text-gray-400 py-8">
                        <p>لا توجد محادثات بعد</p>
                    </div>
                `;
                return;
            }

            conversationsList.innerHTML = conversations.map(conv => `
                <div class="conversation-item p-3 rounded-lg hover:bg-secondary/50 cursor-pointer transition-colors ${conv.id === currentConversationId ? 'bg-secondary/70' : ''}"
                     onclick="loadConversation(${conv.id})">
                    <div class="font-medium text-white truncate">${conv.title}</div>
                    <div class="text-sm text-gray-400">${conv.messages_count} رسالة</div>
                    <div class="text-xs text-gray-500">${new Date(conv.created_at).toLocaleDateString('ar-SA')}</div>
                </div>
            `).join('');
        }

        // Load specific conversation
        async function loadConversation(conversationId) {
            try {
                const response = await fetch(`${API_BASE}/conversations/${conversationId}`);
                const data = await response.json();

                if (response.ok && data.success) {
                    currentConversationId = conversationId;
                    displayMessages(data.data.messages);

                    // Update active conversation in sidebar
                    document.querySelectorAll('.conversation-item').forEach(item => {
                        item.classList.remove('bg-secondary/70');
                    });
                    event.target.closest('.conversation-item').classList.add('bg-secondary/70');
                }
            } catch (error) {
                console.error('Error loading conversation:', error);
            }
        }

        // Display messages
        function displayMessages(messages) {
            messagesContainer.innerHTML = '';

            messages.forEach(message => {
                addMessage(message.content, message.role);
            });
        }

        // Start new chat
        function startNewChat() {
            currentConversationId = null;
            messagesContainer.innerHTML = `
                <div class="text-center py-12">
                    <div class="w-20 h-20 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-primary text-3xl font-bold">W</span>
                    </div>
                    <h2 class="text-2xl font-bold mb-2">محادثة جديدة</h2>
                    <p class="text-gray-400">ابدأ محادثة بكتابة رسالتك أدناه</p>
                </div>
            `;

            // Remove active state from conversations
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('bg-secondary/70');
            });
        }

        // Show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.classList.remove('hidden');
            setTimeout(() => hideError(), 5000);
        }

        // Hide error message
        function hideError() {
            errorMessage.classList.add('hidden');
        }
    </script>
</body>
</html>
<?php /**PATH D:\WIDDX-AI\widdx-ai\resources\views/welcome.blade.php ENDPATH**/ ?>