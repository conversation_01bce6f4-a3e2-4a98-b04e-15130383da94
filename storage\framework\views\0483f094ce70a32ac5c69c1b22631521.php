<!-- WIDDX AI Sidebar Component -->
<aside id="widdx-sidebar" class="w-80 widdx-sidebar bg-gray-900/70 backdrop-blur-xl border-r border-white/10 hidden lg:flex flex-col">
    <!-- New Chat Section -->
    <div class="p-4 border-b border-white/10">
        <button id="widdx-new-chat-btn" class="w-full widdx-btn-primary bg-gradient-to-r from-widdx-primary to-widdx-primary hover:from-blue-600 hover:to-blue-700 text-white px-4 py-3 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 group shadow-lg hover:shadow-xl">
            <svg class="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            <span class="font-medium">New Chat</span>
        </button>
    </div>

    <!-- Search Section -->
    <div class="p-4 border-b border-white/10">
        <div class="relative">
            <input
                type="text"
                id="widdx-search-input"
                placeholder="Search conversations..."
                class="w-full widdx-input bg-widdx-glass border border-gray-600 rounded-lg px-4 py-2 pl-10 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-widdx-primary transition-all duration-200"
            >
            <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="px-4 py-2 border-b border-white/10">
        <div class="flex space-x-1 bg-widdx-glass rounded-lg p-1">
            <button class="widdx-filter-tab active flex-1 px-3 py-2 text-xs font-medium rounded-md transition-colors" data-filter="all">
                All
            </button>
            <button class="widdx-filter-tab flex-1 px-3 py-2 text-xs font-medium rounded-md transition-colors" data-filter="today">
                Today
            </button>
            <button class="widdx-filter-tab flex-1 px-3 py-2 text-xs font-medium rounded-md transition-colors" data-filter="week">
                Week
            </button>
        </div>
    </div>

    <!-- Conversations List -->
    <div class="flex-1 overflow-y-auto widdx-scrollbar">
        <div id="widdx-conversations-list" class="p-2">
            <!-- Empty State -->
            <div id="widdx-empty-state" class="text-center text-gray-400 py-12 animate-widdx-fade-in">
                <div class="w-16 h-16 bg-widdx-glass rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <p class="text-sm font-medium">No conversations yet</p>
                <p class="text-xs text-gray-500 mt-1">Start a new chat to begin</p>
            </div>

            <!-- Conversation Items Template (Hidden) -->
            <template id="widdx-conversation-template">
                <div class="widdx-conversation-item p-4 rounded-xl hover:bg-widdx-secondary/50 cursor-pointer transition-all duration-200 mb-2 border border-transparent group" data-conversation-id="">
                    <div class="flex items-start justify-between">
                        <div class="flex-1 min-w-0">
                            <div class="widdx-conversation-title font-medium text-white truncate mb-1"></div>
                            <div class="widdx-conversation-preview text-xs text-gray-400 truncate mb-2"></div>
                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                    <span class="widdx-message-count">0</span> messages
                                </span>
                                <span>•</span>
                                <span class="widdx-conversation-time"></span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <!-- Active Indicator -->
                            <div class="widdx-active-indicator hidden w-2 h-2 bg-widdx-primary rounded-full animate-pulse"></div>
                            <!-- Menu Button -->
                            <button class="widdx-conversation-menu opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-widdx-secondary/50 transition-all duration-200" title="Options">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Footer Section -->
    <div class="p-4 border-t border-white/10">
        <div class="flex items-center justify-between text-xs text-gray-500">
            <span id="widdx-conversation-count">0 conversations</span>
            <button id="widdx-clear-all" class="hover:text-widdx-error transition-colors" title="Clear All">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>
    </div>
</aside>

<!-- Mobile Sidebar Overlay -->
<div id="widdx-sidebar-overlay" class="fixed inset-0 bg-black/50 z-40 lg:hidden hidden"></div>

<!-- Mobile Sidebar -->
<aside id="widdx-mobile-sidebar" class="fixed left-0 top-0 h-full w-80 widdx-sidebar glass border-r border-white/10 z-50 lg:hidden transform -translate-x-full transition-transform duration-300">
    <!-- Mobile Header -->
    <div class="flex items-center justify-between p-4 border-b border-white/10">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-widdx-primary to-widdx-accent rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
            </div>
            <span class="text-white font-medium">WIDDX AI</span>
        </div>
        <button id="widdx-close-mobile-sidebar" class="p-2 rounded-lg hover:bg-widdx-secondary/50 transition-colors">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>

    <!-- Mobile Content (Same as desktop) -->
    <div class="flex-1 flex flex-col">
        <!-- Content will be synced with desktop sidebar -->
    </div>
</aside>
<?php /**PATH D:\WIDDX-AI\widdx-ai\resources\views/components/sidebar.blade.php ENDPATH**/ ?>