<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="description" content="WIDDX AI - Advanced Intelligent Assistant with Multi-Model Support">
    <meta name="keywords" content="AI, Assistant, DeepSeek, Gemini, HuggingFace, Chat, WIDDX">
    <meta name="author" content="WIDDX AI">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0f172a">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="WIDDX AI">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    <link rel="apple-touch-icon" href="<?php echo e(asset('icons/apple-touch-icon.png')); ?>">
    
    <title><?php echo $__env->yieldContent('title', 'WIDDX AI - Advanced Intelligent Assistant'); ?></title>
    
    <!-- External Libraries -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.6/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css">
    
    <!-- WIDDX AI Styles -->
    <link rel="stylesheet" href="<?php echo e(asset('css/widdx-core.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/widdx-components.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/widdx-animations.css')); ?>">
    
    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        widdx: {
                            primary: '#3b82f6',
                            secondary: '#1e293b',
                            accent: '#8b5cf6',
                            success: '#10b981',
                            warning: '#f59e0b',
                            error: '#ef4444',
                            dark: '#0f172a',
                            'dark-light': '#1e293b',
                            'glass': 'rgba(30, 41, 59, 0.7)',
                        }
                    },
                    fontFamily: {
                        'widdx': ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
                    },
                    animation: {
                        'widdx-fade-in': 'widdxFadeIn 0.3s ease-out',
                        'widdx-slide-up': 'widdxSlideUp 0.3s ease-out',
                        'widdx-bounce-in': 'widdxBounceIn 0.5s ease-out',
                        'widdx-typing': 'widdxTyping 1.4s infinite',
                        'widdx-pulse-slow': 'pulse 2s infinite',
                        'widdx-gradient': 'widdxGradient 3s ease-in-out infinite',
                    }
                }
            }
        }
    </script>
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="bg-gradient-to-br from-widdx-dark to-widdx-dark-light text-white font-widdx antialiased">
    <!-- Loading Screen -->
    <div id="widdx-loading" class="fixed inset-0 bg-widdx-dark z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="widdx-logo-container mb-6">
                <div class="w-20 h-20 bg-gradient-to-br from-widdx-primary to-widdx-accent rounded-2xl flex items-center justify-center mx-auto shadow-2xl animate-widdx-bounce-in">
                    <span class="text-white text-3xl font-bold">W</span>
                </div>
            </div>
            <h1 class="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-2">
                WIDDX AI
            </h1>
            <p class="text-gray-400 text-sm mb-6">Advanced Intelligent Assistant</p>
            <div class="widdx-loading-spinner">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-widdx-primary mx-auto"></div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="widdx-app" class="hidden">
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Toast Notifications -->
    <div id="widdx-toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Core JavaScript -->
    <script src="<?php echo e(asset('js/widdx-core.js')); ?>"></script>
    <script src="<?php echo e(asset('js/widdx-utils.js')); ?>"></script>
    <script src="<?php echo e(asset('js/widdx-api.js')); ?>"></script>
    <script src="<?php echo e(asset('js/widdx-ui.js')); ?>"></script>
    <script src="<?php echo e(asset('js/widdx-chat.js')); ?>"></script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Initialize WIDDX AI -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            WIDDX.init();
        });
    </script>
</body>
</html>
<?php /**PATH D:\WIDDX-AI\widdx-ai\resources\views/layouts/app.blade.php ENDPATH**/ ?>