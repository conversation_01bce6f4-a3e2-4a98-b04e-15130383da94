<?php

// Simple API test script
$baseUrl = 'http://127.0.0.1:8000/api';

echo "Testing WIDDX AI API...\n\n";

// Test 1: Health check
echo "1. Testing health endpoint...\n";
$response = file_get_contents($baseUrl . '/health');
if ($response) {
    echo "✅ Health check: OK\n";
    echo "Response: " . $response . "\n\n";
} else {
    echo "❌ Health check: Failed\n\n";
}

// Test 2: Conversations endpoint
echo "2. Testing conversations endpoint...\n";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Content-Type: application/json',
    ]
]);

$response = file_get_contents($baseUrl . '/conversations', false, $context);
if ($response) {
    echo "✅ Conversations: OK\n";
    echo "Response: " . $response . "\n\n";
} else {
    echo "❌ Conversations: Failed\n\n";
}

// Test 3: Ask endpoint with simple message
echo "3. Testing ask endpoint...\n";
$data = json_encode([
    'message' => 'Hello, how are you?',
    'use_knowledge_base' => false,
    'preferred_service' => null,
    'knowledge_threshold' => 0.7,
    'max_knowledge_results' => 5,
    'tags' => [],
    'source_filter' => null,
    'context' => []
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ],
        'content' => $data
    ]
]);

$response = file_get_contents($baseUrl . '/ask', false, $context);
if ($response) {
    echo "✅ Ask endpoint: OK\n";
    echo "Response: " . substr($response, 0, 200) . "...\n\n";
} else {
    echo "❌ Ask endpoint: Failed\n";
    $error = error_get_last();
    echo "Error: " . ($error['message'] ?? 'Unknown error') . "\n\n";
}

echo "API test completed.\n";
