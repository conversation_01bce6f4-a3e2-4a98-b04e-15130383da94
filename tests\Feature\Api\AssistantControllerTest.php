<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\KnowledgeEntry;
use App\Services\DecisionEngine;
use App\Services\KnowledgeBaseService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;

class AssistantControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up test configuration
        Config::set('ai.services.deepseek.enabled', true);
        Config::set('ai.services.gemini.enabled', true);
        Config::set('ai.services.huggingface.enabled', true);
        Config::set('ai.knowledge_base.enabled', true);
        Config::set('ai.knowledge_base.use_mock_embeddings', true);
    }

    public function test_ask_endpoint_creates_new_conversation()
    {
        $response = $this->postJson('/api/ask', [
            'message' => 'Hello, how can you help me with <PERSON><PERSON>?',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'message' => [
                        'id',
                        'conversation_id',
                        'role',
                        'content',
                        'model_used',
                        'created_at',
                    ],
                    'conversation' => [
                        'id',
                        'title',
                        'created_at',
                    ],
                    'response' => [
                        'content',
                        'model_used',
                        'service_used',
                        'fallback_used',
                        'processing_time',
                    ],
                    'knowledge_base' => [
                        'used',
                        'entries_found',
                        'threshold_used',
                    ],
                    'metadata',
                ],
                'timestamp',
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('assistant', $response->json('data.message.role'));

        // Check that conversation and messages were created
        $this->assertDatabaseHas('conversations', [
            'id' => $response->json('data.conversation.id'),
        ]);

        $this->assertDatabaseHas('messages', [
            'conversation_id' => $response->json('data.conversation.id'),
            'role' => 'user',
            'content' => 'Hello, how can you help me with Laravel?',
        ]);

        $this->assertDatabaseHas('messages', [
            'conversation_id' => $response->json('data.conversation.id'),
            'role' => 'assistant',
        ]);
    }

    public function test_ask_endpoint_uses_existing_conversation()
    {
        $conversation = Conversation::create(['title' => 'Existing Conversation']);

        $response = $this->postJson('/api/ask', [
            'message' => 'Continue our conversation',
            'conversation_id' => $conversation->id,
        ]);

        $response->assertStatus(200);
        $this->assertEquals($conversation->id, $response->json('data.conversation.id'));

        // Should have 2 messages now (user + assistant)
        $this->assertEquals(2, $conversation->messages()->count());
    }

    public function test_ask_endpoint_with_knowledge_base()
    {
        // Create a knowledge entry
        KnowledgeEntry::create([
            'content' => 'Laravel is a PHP web application framework with expressive, elegant syntax.',
            'embedding_vector' => array_fill(0, 384, 0.1),
            'source' => 'docs',
            'tags' => ['laravel', 'php'],
            'confidence_score' => 0.9,
        ]);

        $response = $this->postJson('/api/ask', [
            'message' => 'What is Laravel?',
            'use_knowledge_base' => true,
            'knowledge_threshold' => 0.1,
        ]);

        $response->assertStatus(200);

        $knowledgeBase = $response->json('data.knowledge_base');
        $this->assertTrue($knowledgeBase['used']);
        $this->assertGreaterThan(0, $knowledgeBase['entries_found']);
    }

    public function test_ask_endpoint_with_preferred_service()
    {
        $response = $this->postJson('/api/ask', [
            'message' => 'Write a simple function',
            'preferred_service' => 'deepseek',
        ]);

        $response->assertStatus(200);
        $this->assertEquals('deepseek', $response->json('data.response.service_used'));
    }

    public function test_ask_endpoint_with_context()
    {
        $response = $this->postJson('/api/ask', [
            'message' => 'Explain this code',
            'context' => [
                'function hello() { return "world"; }',
                'This is a JavaScript function',
            ],
        ]);

        $response->assertStatus(200);

        // Check that context was stored in user message metadata
        $userMessage = Message::where('role', 'user')->first();
        $this->assertNotNull($userMessage->metadata);
        $this->assertArrayHasKey('context', $userMessage->metadata);
        $this->assertCount(2, $userMessage->metadata['context']);
    }

    public function test_ask_endpoint_with_tags_and_source_filter()
    {
        // Create knowledge entries with different tags and sources
        KnowledgeEntry::create([
            'content' => 'Laravel routing information',
            'embedding_vector' => array_fill(0, 384, 0.1),
            'source' => 'docs',
            'tags' => ['laravel', 'routing'],
            'confidence_score' => 0.9,
        ]);

        KnowledgeEntry::create([
            'content' => 'Python routing information',
            'embedding_vector' => array_fill(0, 384, 0.1),
            'source' => 'tutorial',
            'tags' => ['python', 'routing'],
            'confidence_score' => 0.8,
        ]);

        $response = $this->postJson('/api/ask', [
            'message' => 'How does routing work?',
            'tags' => ['laravel'],
            'source_filter' => 'docs',
            'use_knowledge_base' => true,
            'knowledge_threshold' => 0.1,
        ]);

        $response->assertStatus(200);

        // Should find the Laravel entry but not the Python one
        $knowledgeBase = $response->json('data.knowledge_base');
        $this->assertTrue($knowledgeBase['used']);
        $this->assertEquals(1, $knowledgeBase['entries_found']);
    }

    public function test_ask_endpoint_validation_errors()
    {
        // Test missing message
        $response = $this->postJson('/api/ask', []);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['message']);

        // Test invalid conversation_id
        $response = $this->postJson('/api/ask', [
            'message' => 'Test',
            'conversation_id' => 999999,
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['conversation_id']);

        // Test invalid preferred_service
        $response = $this->postJson('/api/ask', [
            'message' => 'Test',
            'preferred_service' => 'invalid_service',
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['preferred_service']);
    }

    public function test_ask_endpoint_handles_long_messages()
    {
        $longMessage = str_repeat('This is a very long message. ', 100);

        $response = $this->postJson('/api/ask', [
            'message' => $longMessage,
        ]);

        $response->assertStatus(200);
        $this->assertEquals(strlen($longMessage), $response->json('data.metadata.query_length'));
    }

    public function test_conversation_title_generation()
    {
        $response = $this->postJson('/api/ask', [
            'message' => 'This is my first message in a new conversation about Laravel development',
        ]);

        $response->assertStatus(200);

        $conversation = Conversation::find($response->json('data.conversation.id'));
        $this->assertNotEquals('New Conversation', $conversation->title);
        $this->assertStringContainsString('This is my first message', $conversation->title);
    }

    public function test_health_endpoint()
    {
        $response = $this->getJson('/api/health');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'timestamp',
                'version',
                'services',
            ])
            ->assertJson([
                'status' => 'ok',
            ]);
    }

    public function test_info_endpoint()
    {
        $response = $this->getJson('/api/info');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'name',
                'version',
                'description',
                'endpoints',
                'supported_services',
                'features',
            ])
            ->assertJson([
                'name' => 'WIDDX AI Assistant API',
            ]);
    }

    public function test_ask_endpoint_includes_processing_time()
    {
        $response = $this->postJson('/api/ask', [
            'message' => 'Quick test message',
        ]);

        $response->assertStatus(200);

        $processingTime = $response->json('data.response.processing_time');
        $this->assertIsNumeric($processingTime);
        $this->assertGreaterThan(0, $processingTime);
    }

    public function test_ask_endpoint_stores_metadata()
    {
        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
            'preferred_service' => 'gemini',
            'use_knowledge_base' => false,
            'tags' => ['test', 'api'],
        ]);

        $response->assertStatus(200);

        // Check user message metadata
        $userMessage = Message::where('role', 'user')->first();
        $metadata = $userMessage->metadata;

        $this->assertEquals('gemini', $metadata['preferred_service']);
        $this->assertFalse($metadata['use_knowledge_base']);
        $this->assertEquals(['test', 'api'], $metadata['tags']);

        // Check assistant message metadata
        $assistantMessage = Message::where('role', 'assistant')->first();
        $assistantMetadata = $assistantMessage->metadata;

        $this->assertArrayHasKey('service_used', $assistantMetadata);
        $this->assertArrayHasKey('fallback_used', $assistantMetadata);
        $this->assertArrayHasKey('raw_response', $assistantMetadata);
    }

    public function test_get_conversations_endpoint()
    {
        // Create test conversations
        $conversation1 = Conversation::create(['title' => 'First Conversation']);
        $conversation2 = Conversation::create(['title' => 'Second Conversation']);

        // Add messages to conversations
        Message::create([
            'conversation_id' => $conversation1->id,
            'role' => 'user',
            'content' => 'Hello',
        ]);

        Message::create([
            'conversation_id' => $conversation2->id,
            'role' => 'user',
            'content' => 'Hi there',
        ]);

        $response = $this->getJson('/api/conversations');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'title',
                        'created_at',
                        'updated_at',
                        'messages_count',
                    ],
                ],
                'meta' => [
                    'current_page',
                    'last_page',
                    'per_page',
                    'total',
                ],
                'timestamp',
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertCount(2, $response->json('data'));
    }

    public function test_get_conversation_endpoint()
    {
        $conversation = Conversation::create(['title' => 'Test Conversation']);

        Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => 'Hello',
        ]);

        Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'content' => 'Hi there!',
            'model_used' => 'deepseek',
        ]);

        $response = $this->getJson("/api/conversations/{$conversation->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'title',
                    'created_at',
                    'updated_at',
                    'messages' => [
                        '*' => [
                            'id',
                            'role',
                            'content',
                            'model_used',
                            'created_at',
                        ],
                    ],
                ],
                'timestamp',
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals($conversation->id, $response->json('data.id'));
        $this->assertCount(2, $response->json('data.messages'));
    }

    public function test_get_nonexistent_conversation_returns_404()
    {
        $response = $this->getJson('/api/conversations/999999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'CONVERSATION_NOT_FOUND',
                ],
            ]);
    }

    public function test_delete_conversation_endpoint()
    {
        $conversation = Conversation::create(['title' => 'To Delete']);

        Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => 'Test message',
        ]);

        $response = $this->deleteJson("/api/conversations/{$conversation->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Conversation deleted successfully.',
            ]);

        // Verify conversation and messages are deleted
        $this->assertDatabaseMissing('conversations', ['id' => $conversation->id]);
        $this->assertDatabaseMissing('messages', ['conversation_id' => $conversation->id]);
    }

    public function test_update_conversation_title_endpoint()
    {
        $conversation = Conversation::create(['title' => 'Old Title']);

        $response = $this->patchJson("/api/conversations/{$conversation->id}/title", [
            'title' => 'New Updated Title',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'title',
                    'created_at',
                    'updated_at',
                ],
                'timestamp',
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('New Updated Title', $response->json('data.title'));

        // Verify in database
        $this->assertDatabaseHas('conversations', [
            'id' => $conversation->id,
            'title' => 'New Updated Title',
        ]);
    }

    public function test_update_conversation_title_validation()
    {
        $conversation = Conversation::create(['title' => 'Test']);

        // Test missing title
        $response = $this->patchJson("/api/conversations/{$conversation->id}/title", []);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['title']);

        // Test title too long
        $response = $this->patchJson("/api/conversations/{$conversation->id}/title", [
            'title' => str_repeat('a', 256),
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['title']);
    }

    public function test_conversations_pagination()
    {
        // Create 25 conversations
        for ($i = 1; $i <= 25; $i++) {
            Conversation::create(['title' => "Conversation {$i}"]);
        }

        // Test first page
        $response = $this->getJson('/api/conversations?per_page=10&page=1');
        $response->assertStatus(200);

        $meta = $response->json('meta');
        $this->assertEquals(1, $meta['current_page']);
        $this->assertEquals(3, $meta['last_page']);
        $this->assertEquals(10, $meta['per_page']);
        $this->assertEquals(25, $meta['total']);
        $this->assertCount(10, $response->json('data'));

        // Test second page
        $response = $this->getJson('/api/conversations?per_page=10&page=2');
        $response->assertStatus(200);
        $this->assertEquals(2, $response->json('meta.current_page'));
        $this->assertCount(10, $response->json('data'));
    }
}
