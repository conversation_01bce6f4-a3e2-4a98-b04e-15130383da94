<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\Conversation;
use App\Services\DecisionEngine;
use App\Services\KnowledgeBaseService;
use App\Exceptions\ServiceUnavailableException;
use App\Exceptions\AIServiceException;
use App\Exceptions\RateLimitException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;

class ErrorHandlingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        Config::set('ai.services.deepseek.enabled', true);
        Config::set('ai.services.gemini.enabled', true);
        Config::set('ai.services.huggingface.enabled', true);
        Config::set('ai.knowledge_base.enabled', true);
        Config::set('ai.knowledge_base.use_mock_embeddings', true);
        Config::set('ai.rate_limits.enabled', true);
    }

    public function test_handles_service_unavailable_exception()
    {
        // Mock DecisionEngine to throw ServiceUnavailableException
        $this->mock(DecisionEngine::class, function ($mock) {
            $mock->shouldReceive('routeQuery')
                ->andThrow(new ServiceUnavailableException('All AI services are currently unavailable'));
        });

        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
        ]);

        $response->assertStatus(503)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'SERVICE_UNAVAILABLE',
                    'message' => 'AI services are temporarily unavailable. Please try again later.',
                    'retry_after' => 60,
                ],
            ])
            ->assertJsonStructure([
                'timestamp',
                'request_id',
            ]);
    }

    public function test_handles_ai_service_exception()
    {
        // Mock DecisionEngine to throw AIServiceException
        $this->mock(DecisionEngine::class, function ($mock) {
            $mock->shouldReceive('routeQuery')
                ->andThrow(new AIServiceException('API communication failed'));
        });

        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
        ]);

        $response->assertStatus(502)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'AI_SERVICE_ERROR',
                    'message' => 'There was an error processing your request with the AI service.',
                ],
            ]);
    }

    public function test_handles_validation_errors()
    {
        $response = $this->postJson('/api/ask', [
            // Missing required 'message' field
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'VALIDATION_ERROR',
                    'message' => 'The request data is invalid.',
                ],
            ])
            ->assertJsonStructure([
                'error' => [
                    'details' => [
                        'message',
                    ],
                ],
            ]);
    }

    public function test_handles_conversation_not_found()
    {
        $response = $this->getJson('/api/conversations/999999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'CONVERSATION_NOT_FOUND',
                    'message' => 'The requested conversation was not found.',
                ],
            ]);
    }

    public function test_rate_limiting_works()
    {
        Config::set('ai.rate_limits.enabled', true);
        
        // Clear any existing rate limit cache
        Cache::flush();

        // Make requests up to the limit (30 per minute for /ask endpoint)
        for ($i = 0; $i < 30; $i++) {
            $response = $this->postJson('/api/ask', [
                'message' => "Test message {$i}",
            ]);
            
            // All requests should succeed
            $this->assertLessThan(500, $response->getStatusCode());
        }

        // The 31st request should be rate limited
        $response = $this->postJson('/api/ask', [
            'message' => 'This should be rate limited',
        ]);

        $response->assertStatus(429)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'RATE_LIMIT_EXCEEDED',
                    'message' => 'Too many requests. Please slow down and try again later.',
                    'retry_after' => 300,
                ],
            ]);
    }

    public function test_rate_limiting_headers_are_present()
    {
        Config::set('ai.rate_limits.enabled', true);
        Cache::flush();

        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
        ]);

        $response->assertHeader('X-RateLimit-Limit')
            ->assertHeader('X-RateLimit-Remaining')
            ->assertHeader('X-RateLimit-Reset');
    }

    public function test_rate_limiting_can_be_disabled()
    {
        Config::set('ai.rate_limits.enabled', false);
        Cache::flush();

        // Make many requests - should not be rate limited
        for ($i = 0; $i < 50; $i++) {
            $response = $this->postJson('/api/ask', [
                'message' => "Test message {$i}",
            ]);
            
            $this->assertLessThan(500, $response->getStatusCode());
        }
    }

    public function test_request_logging_adds_request_id()
    {
        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
        ]);

        $response->assertHeader('X-Request-ID');
        
        $requestId = $response->headers->get('X-Request-ID');
        $this->assertNotEmpty($requestId);
        $this->assertStringStartsWith('req_', $requestId);
    }

    public function test_custom_request_id_is_preserved()
    {
        $customRequestId = 'custom-request-123';
        
        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
        ], [
            'X-Request-ID' => $customRequestId,
        ]);

        $response->assertHeader('X-Request-ID', $customRequestId);
    }

    public function test_fallback_mechanism_works()
    {
        // Mock DecisionEngine to simulate fallback behavior
        $this->mock(DecisionEngine::class, function ($mock) {
            $mock->shouldReceive('routeQuery')
                ->andReturn([
                    'service_used' => 'gemini',
                    'fallback_used' => true,
                    'result' => [
                        'content' => 'Fallback response from Gemini',
                        'model' => 'gemini-pro',
                    ],
                ]);
        });

        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
            'preferred_service' => 'deepseek', // This will "fail" and fallback to gemini
        ]);

        $response->assertStatus(200);
        
        $responseData = $response->json('data.response');
        $this->assertEquals('gemini', $responseData['service_used']);
        $this->assertTrue($responseData['fallback_used']);
        $this->assertEquals('Fallback response from Gemini', $responseData['content']);
    }

    public function test_knowledge_base_failure_is_handled_gracefully()
    {
        // Mock KnowledgeBaseService to throw exception
        $this->mock(KnowledgeBaseService::class, function ($mock) {
            $mock->shouldReceive('isEnabled')->andReturn(true);
            $mock->shouldReceive('search')->andThrow(new \Exception('Knowledge base error'));
        });

        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
            'use_knowledge_base' => true,
        ]);

        // Should still succeed but without knowledge base results
        $response->assertStatus(200);
        
        $knowledgeBase = $response->json('data.knowledge_base');
        $this->assertFalse($knowledgeBase['used']);
        $this->assertEquals(0, $knowledgeBase['entries_found']);
    }

    public function test_database_transaction_rollback_on_error()
    {
        // Mock DecisionEngine to throw exception after conversation is created
        $this->mock(DecisionEngine::class, function ($mock) {
            $mock->shouldReceive('routeQuery')
                ->andThrow(new \Exception('Simulated error after conversation creation'));
        });

        $initialConversationCount = Conversation::count();

        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
        ]);

        $response->assertStatus(500);
        
        // Conversation should not have been created due to transaction rollback
        $this->assertEquals($initialConversationCount, Conversation::count());
    }

    public function test_error_response_format_consistency()
    {
        $response = $this->postJson('/api/ask', [
            // Missing message to trigger validation error
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'error' => [
                    'code',
                    'message',
                    'details',
                ],
                'timestamp',
                'request_id',
            ]);

        $this->assertFalse($response->json('success'));
        $this->assertIsString($response->json('error.code'));
        $this->assertIsString($response->json('error.message'));
        $this->assertIsString($response->json('timestamp'));
        $this->assertIsString($response->json('request_id'));
    }

    public function test_debug_mode_shows_detailed_errors()
    {
        Config::set('app.debug', true);

        // Mock to throw a specific exception
        $this->mock(DecisionEngine::class, function ($mock) {
            $mock->shouldReceive('routeQuery')
                ->andThrow(new \Exception('Detailed error message for debugging'));
        });

        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
        ]);

        $response->assertStatus(500);
        $this->assertEquals('Detailed error message for debugging', $response->json('error.details'));
    }

    public function test_production_mode_hides_detailed_errors()
    {
        Config::set('app.debug', false);

        // Mock to throw a specific exception
        $this->mock(DecisionEngine::class, function ($mock) {
            $mock->shouldReceive('routeQuery')
                ->andThrow(new \Exception('Detailed error message that should be hidden'));
        });

        $response = $this->postJson('/api/ask', [
            'message' => 'Test message',
        ]);

        $response->assertStatus(500);
        $this->assertNull($response->json('error.details'));
    }
}
