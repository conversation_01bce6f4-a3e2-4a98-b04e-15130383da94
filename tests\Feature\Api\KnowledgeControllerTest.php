<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\KnowledgeEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;

class KnowledgeControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up test configuration
        Config::set('ai.knowledge_base.enabled', true);
        Config::set('ai.knowledge_base.use_mock_embeddings', true);
    }

    public function test_can_list_knowledge_entries()
    {
        // Create test entries
        KnowledgeEntry::factory()->count(5)->create();

        $response = $this->getJson('/api/knowledge');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'entries' => [
                        '*' => [
                            'id',
                            'content',
                            'source',
                            'tags',
                            'confidence_score',
                            'created_at',
                        ]
                    ],
                    'pagination' => [
                        'current_page',
                        'last_page',
                        'per_page',
                        'total',
                    ],
                    'stats' => [
                        'total',
                        'high_confidence',
                        'recent',
                        'unique_tags',
                    ]
                ]
            ]);

        $this->assertEquals(5, $response->json('data.stats.total'));
    }

    public function test_can_create_knowledge_entry()
    {
        $entryData = [
            'content' => 'Laravel is a PHP web application framework with expressive, elegant syntax.',
            'source' => 'manual',
            'tags' => ['laravel', 'php', 'framework'],
            'confidence_score' => 0.9,
            'metadata' => ['author' => 'test_user']
        ];

        $response = $this->postJson('/api/knowledge', $entryData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'content',
                    'source',
                    'tags',
                    'confidence_score',
                    'metadata',
                    'embedding',
                    'created_at',
                ]
            ]);

        $this->assertDatabaseHas('knowledge_entries', [
            'content' => $entryData['content'],
            'source' => $entryData['source'],
            'confidence_score' => $entryData['confidence_score'],
        ]);
    }

    public function test_can_search_knowledge_entries()
    {
        // Create test entries
        KnowledgeEntry::factory()->create([
            'content' => 'Laravel is a PHP framework for web development',
            'tags' => ['laravel', 'php'],
            'confidence_score' => 0.9
        ]);

        KnowledgeEntry::factory()->create([
            'content' => 'Vue.js is a JavaScript framework for building user interfaces',
            'tags' => ['vue', 'javascript'],
            'confidence_score' => 0.8
        ]);

        $response = $this->getJson('/api/knowledge/search?q=Laravel framework&threshold=0.5');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'query',
                    'results' => [
                        '*' => [
                            'id',
                            'content',
                            'source',
                            'tags',
                            'confidence_score',
                        ]
                    ],
                    'count',
                    'threshold',
                ]
            ]);

        $this->assertGreaterThan(0, $response->json('data.count'));
    }

    public function test_can_update_knowledge_entry()
    {
        $entry = KnowledgeEntry::factory()->create([
            'content' => 'Original content',
            'confidence_score' => 0.7
        ]);

        $updateData = [
            'content' => 'Updated content about Laravel framework',
            'confidence_score' => 0.9,
            'tags' => ['laravel', 'updated']
        ];

        $response = $this->patchJson("/api/knowledge/{$entry->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'content',
                    'confidence_score',
                    'tags',
                ]
            ]);

        $this->assertDatabaseHas('knowledge_entries', [
            'id' => $entry->id,
            'content' => $updateData['content'],
            'confidence_score' => $updateData['confidence_score'],
        ]);
    }

    public function test_can_delete_knowledge_entry()
    {
        $entry = KnowledgeEntry::factory()->create();

        $response = $this->deleteJson("/api/knowledge/{$entry->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Knowledge entry deleted successfully.'
            ]);

        $this->assertDatabaseMissing('knowledge_entries', [
            'id' => $entry->id
        ]);
    }

    public function test_can_get_knowledge_stats()
    {
        // Create test entries with different confidence scores
        KnowledgeEntry::factory()->count(3)->create(['confidence_score' => 0.9]);
        KnowledgeEntry::factory()->count(2)->create(['confidence_score' => 0.6]);

        $response = $this->getJson('/api/knowledge/stats');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'total',
                    'high_confidence',
                    'recent',
                    'unique_tags',
                    'source_distribution',
                    'average_confidence',
                ]
            ]);

        $stats = $response->json('data');
        $this->assertEquals(5, $stats['total']);
        $this->assertEquals(3, $stats['high_confidence']);
    }

    public function test_validates_required_fields_when_creating_entry()
    {
        $response = $this->postJson('/api/knowledge', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['content']);
    }

    public function test_validates_content_length_when_creating_entry()
    {
        $response = $this->postJson('/api/knowledge', [
            'content' => 'short' // Less than 10 characters
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['content']);
    }

    public function test_validates_confidence_score_range()
    {
        $response = $this->postJson('/api/knowledge', [
            'content' => 'Valid content that is long enough for testing',
            'confidence_score' => 1.5 // Invalid range
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['confidence_score']);
    }

    public function test_returns_404_for_nonexistent_entry()
    {
        $response = $this->getJson('/api/knowledge/999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'ENTRY_NOT_FOUND'
                ]
            ]);
    }

    public function test_can_filter_entries_by_source()
    {
        KnowledgeEntry::factory()->create(['source' => 'manual']);
        KnowledgeEntry::factory()->create(['source' => 'ai_response']);

        $response = $this->getJson('/api/knowledge?source=manual');

        $response->assertStatus(200);
        
        $entries = $response->json('data.entries');
        $this->assertCount(1, $entries);
        $this->assertEquals('manual', $entries[0]['source']);
    }

    public function test_can_search_entries_by_content()
    {
        KnowledgeEntry::factory()->create(['content' => 'Laravel framework tutorial']);
        KnowledgeEntry::factory()->create(['content' => 'Vue.js component guide']);

        $response = $this->getJson('/api/knowledge?search=Laravel');

        $response->assertStatus(200);
        
        $entries = $response->json('data.entries');
        $this->assertCount(1, $entries);
        $this->assertStringContains('Laravel', $entries[0]['content']);
    }
}
