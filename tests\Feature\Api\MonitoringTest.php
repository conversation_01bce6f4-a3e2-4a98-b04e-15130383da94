<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Services\MonitoringService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

class MonitoringTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear cache before each test
        Cache::flush();
    }

    public function test_metrics_endpoint_returns_system_metrics()
    {
        $response = $this->getJson('/api/metrics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'system_health' => [
                        'status',
                        'checks' => [
                            'database' => ['status'],
                            'cache' => ['status'],
                            'memory' => ['status', 'usage_bytes', 'usage_mb', 'percentage'],
                        ],
                        'timestamp',
                    ],
                    'service_metrics' => [
                        'deepseek' => [
                            'total_calls',
                            'successful_calls',
                            'failed_calls',
                            'average_duration',
                            'total_duration',
                        ],
                        'gemini' => [
                            'total_calls',
                            'successful_calls',
                            'failed_calls',
                            'average_duration',
                            'total_duration',
                        ],
                        'huggingface' => [
                            'total_calls',
                            'successful_calls',
                            'failed_calls',
                            'average_duration',
                            'total_duration',
                        ],
                    ],
                    'interaction_metrics' => [
                        'total_interactions',
                        'ask_requests',
                        'conversation_views',
                        'conversation_deletions',
                        'title_updates',
                    ],
                    'search_metrics' => [
                        'total_searches',
                        'average_results',
                        'average_search_time',
                        'total_search_time',
                    ],
                    'performance_metrics' => [
                        'operations',
                    ],
                    'error_metrics' => [
                        'total_errors',
                        'error_levels',
                    ],
                ],
                'timestamp',
            ]);

        $this->assertTrue($response->json('success'));
    }

    public function test_metrics_endpoint_shows_actual_data_after_api_calls()
    {
        // Make some API calls to generate metrics
        $this->postJson('/api/ask', [
            'message' => 'Test message for metrics',
        ]);

        $this->getJson('/api/conversations');

        // Get metrics
        $response = $this->getJson('/api/metrics');

        $response->assertStatus(200);

        $data = $response->json('data');

        // Should have interaction metrics
        $this->assertGreaterThan(0, $data['interaction_metrics']['total_interactions']);
        $this->assertGreaterThan(0, $data['interaction_metrics']['ask_requests']);
    }

    public function test_clear_metrics_endpoint_works()
    {
        // Generate some metrics first
        $monitoringService = app(MonitoringService::class);
        $monitoringService->logUserInteraction('ask', []);
        $monitoringService->logAIServiceCall('deepseek', 'model', [], [], 0.5, true);

        // Verify metrics exist
        $response = $this->getJson('/api/metrics');
        $data = $response->json('data');
        $this->assertGreaterThan(0, $data['interaction_metrics']['total_interactions']);
        $this->assertGreaterThan(0, $data['service_metrics']['deepseek']['total_calls']);

        // Clear metrics
        $response = $this->deleteJson('/api/metrics');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Metrics cleared successfully.',
            ]);

        // Verify metrics are cleared
        $response = $this->getJson('/api/metrics');
        $data = $response->json('data');
        $this->assertEquals(0, $data['interaction_metrics']['total_interactions']);
        $this->assertEquals(0, $data['service_metrics']['deepseek']['total_calls']);
    }

    public function test_system_health_shows_database_status()
    {
        $response = $this->getJson('/api/metrics');

        $response->assertStatus(200);

        $health = $response->json('data.system_health');
        $this->assertArrayHasKey('checks', $health);
        $this->assertArrayHasKey('database', $health['checks']);
        $this->assertEquals('healthy', $health['checks']['database']['status']);
        $this->assertArrayHasKey('response_time', $health['checks']['database']);
    }

    public function test_system_health_shows_cache_status()
    {
        $response = $this->getJson('/api/metrics');

        $response->assertStatus(200);

        $health = $response->json('data.system_health');
        $this->assertArrayHasKey('cache', $health['checks']);
        $this->assertEquals('healthy', $health['checks']['cache']['status']);
    }

    public function test_system_health_shows_memory_usage()
    {
        $response = $this->getJson('/api/metrics');

        $response->assertStatus(200);

        $memory = $response->json('data.system_health.checks.memory');
        $this->assertArrayHasKey('status', $memory);
        $this->assertArrayHasKey('usage_bytes', $memory);
        $this->assertArrayHasKey('usage_mb', $memory);
        $this->assertArrayHasKey('percentage', $memory);

        $this->assertIsNumeric($memory['usage_bytes']);
        $this->assertIsNumeric($memory['usage_mb']);
        $this->assertIsNumeric($memory['percentage']);
        $this->assertGreaterThan(0, $memory['usage_bytes']);
    }

    public function test_service_metrics_track_ai_service_calls()
    {
        // Make multiple API calls
        for ($i = 0; $i < 3; $i++) {
            $this->postJson('/api/ask', [
                'message' => "Test message {$i}",
                'preferred_service' => 'deepseek',
            ]);
        }

        $response = $this->getJson('/api/metrics');
        $response->assertStatus(200);

        $serviceMetrics = $response->json('data.service_metrics.deepseek');
        $this->assertGreaterThan(0, $serviceMetrics['total_calls']);
        $this->assertIsNumeric($serviceMetrics['average_duration']);
    }

    public function test_interaction_metrics_track_different_actions()
    {
        // Perform different types of interactions
        $this->postJson('/api/ask', ['message' => 'Test']);
        $this->getJson('/api/conversations');

        $response = $this->getJson('/api/metrics');
        $response->assertStatus(200);

        $interactionMetrics = $response->json('data.interaction_metrics');
        $this->assertGreaterThan(0, $interactionMetrics['total_interactions']);
        $this->assertGreaterThan(0, $interactionMetrics['ask_requests']);
    }

    public function test_search_metrics_track_knowledge_base_usage()
    {
        // Make request with knowledge base search
        $this->postJson('/api/ask', [
            'message' => 'What is Laravel?',
            'use_knowledge_base' => true,
            'knowledge_threshold' => 0.1,
        ]);

        $response = $this->getJson('/api/metrics');
        $response->assertStatus(200);

        $searchMetrics = $response->json('data.search_metrics');
        $this->assertGreaterThanOrEqual(0, $searchMetrics['total_searches']);
        $this->assertIsNumeric($searchMetrics['average_search_time']);
    }

    public function test_performance_metrics_track_operations()
    {
        // Make some API calls to generate performance data
        $this->postJson('/api/ask', ['message' => 'Performance test']);
        $this->getJson('/api/conversations');

        $response = $this->getJson('/api/metrics');
        $response->assertStatus(200);

        $performanceMetrics = $response->json('data.performance_metrics');
        $this->assertArrayHasKey('operations', $performanceMetrics);
        $this->assertIsArray($performanceMetrics['operations']);
    }

    public function test_error_metrics_start_at_zero()
    {
        $response = $this->getJson('/api/metrics');
        $response->assertStatus(200);

        $errorMetrics = $response->json('data.error_metrics');
        $this->assertEquals(0, $errorMetrics['total_errors']);
        $this->assertArrayHasKey('error_levels', $errorMetrics);
        $this->assertEquals(0, $errorMetrics['error_levels']['error']);
        $this->assertEquals(0, $errorMetrics['error_levels']['warning']);
    }

    public function test_metrics_endpoint_includes_timestamps()
    {
        $response = $this->getJson('/api/metrics');
        $response->assertStatus(200);

        $this->assertArrayHasKey('timestamp', $response->json());
        $this->assertArrayHasKey('timestamp', $response->json('data.system_health'));

        // Timestamps should be in ISO format
        $timestamp = $response->json('timestamp');
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{6}Z$/', $timestamp);
    }

    public function test_metrics_endpoint_handles_errors_gracefully()
    {
        // Mock MonitoringService to throw exception
        $this->mock(MonitoringService::class, function ($mock) {
            $mock->shouldReceive('getSystemHealth')
                ->andThrow(new \Exception('Monitoring service error'));
        });

        $response = $this->getJson('/api/metrics');

        $response->assertStatus(500)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'INTERNAL_ERROR',
                    'message' => 'An unexpected error occurred. Please try again later.',
                ],
            ]);
    }

    public function test_clear_metrics_endpoint_handles_errors_gracefully()
    {
        // Mock MonitoringService to throw exception
        $this->mock(MonitoringService::class, function ($mock) {
            $mock->shouldReceive('clearMetrics')
                ->andThrow(new \Exception('Clear metrics error'));
        });

        $response = $this->deleteJson('/api/metrics');

        $response->assertStatus(500)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'INTERNAL_ERROR',
                ],
            ]);
    }

    public function test_metrics_data_persists_across_requests()
    {
        // Make initial request to generate metrics
        $this->postJson('/api/ask', ['message' => 'First request']);

        // Get initial metrics
        $response1 = $this->getJson('/api/metrics');
        $initialInteractions = $response1->json('data.interaction_metrics.total_interactions');

        // Make another request
        $this->postJson('/api/ask', ['message' => 'Second request']);

        // Get updated metrics
        $response2 = $this->getJson('/api/metrics');
        $updatedInteractions = $response2->json('data.interaction_metrics.total_interactions');

        // Interactions should have increased
        $this->assertGreaterThan($initialInteractions, $updatedInteractions);
    }

    public function test_metrics_response_format_is_consistent()
    {
        $response = $this->getJson('/api/metrics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data',
                'timestamp',
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertIsArray($response->json('data'));
        $this->assertIsString($response->json('timestamp'));
    }
}
