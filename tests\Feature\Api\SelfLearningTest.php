<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\KnowledgeEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;

class SelfLearningTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Enable self-learning for tests
        Config::set('ai.self_learning.enabled', true);
        Config::set('ai.self_learning.min_confidence_score', 0.6);
        Config::set('ai.self_learning.local_response_threshold', 0.85);
        Config::set('ai.knowledge_base.enabled', true);
        Config::set('ai.knowledge_base.use_mock_embeddings', true);
    }

    public function test_assistant_response_is_auto_saved_to_knowledge_base()
    {
        // Mock AI service response
        $this->mockAIServiceResponse([
            'service_used' => 'deepseek',
            'fallback_used' => false,
            'result' => [
                'content' => 'Laravel is a PHP web application framework with expressive, elegant syntax. It provides tools for routing, authentication, sessions, caching, and more.',
                'model' => 'deepseek-coder',
            ]
        ]);

        $response = $this->postJson('/api/ask', [
            'message' => 'What is Laravel?',
            'tags' => ['laravel', 'php']
        ]);

        $response->assertStatus(200);

        // Check that the response was saved to knowledge base
        $this->assertDatabaseHas('knowledge_entries', [
            'source' => 'ai_response',
            'content' => 'Laravel is a PHP web application framework with expressive, elegant syntax. It provides tools for routing, authentication, sessions, caching, and more.',
        ]);

        // Check that tags were generated
        $entry = KnowledgeEntry::where('source', 'ai_response')->first();
        $this->assertNotNull($entry);
        $this->assertContains('service:deepseek', $entry->tags);
        $this->assertContains('laravel', $entry->tags);
        $this->assertContains('php', $entry->tags);
    }

    public function test_high_confidence_local_response_is_used_instead_of_ai()
    {
        // Create a high-confidence knowledge entry
        $knowledgeEntry = KnowledgeEntry::factory()->create([
            'content' => 'Laravel is a PHP framework for web artisans. It provides elegant syntax and powerful tools for web development.',
            'tags' => ['laravel', 'php', 'framework'],
            'confidence_score' => 0.9,
            'source' => 'documentation'
        ]);

        $response = $this->postJson('/api/ask', [
            'message' => 'What is Laravel framework?',
            'use_knowledge_base' => true,
            'knowledge_threshold' => 0.7
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.response.service_used', 'knowledge_base')
            ->assertJsonPath('data.response.local_response', true)
            ->assertJsonPath('data.knowledge_base.local_response_used', true);

        // Verify the response content matches the knowledge entry
        $responseContent = $response->json('data.response.content');
        $this->assertEquals($knowledgeEntry->content, $responseContent);
    }

    public function test_low_confidence_responses_are_not_auto_saved()
    {
        // Mock a low-quality AI response
        $this->mockAIServiceResponse([
            'service_used' => 'huggingface',
            'fallback_used' => true,
            'result' => [
                'content' => 'Short response.',
                'model' => 'fallback-model',
            ]
        ]);

        Config::set('ai.self_learning.min_confidence_score', 0.8);

        $response = $this->postJson('/api/ask', [
            'message' => 'Test question?'
        ]);

        $response->assertStatus(200);

        // Check that the response was NOT saved to knowledge base
        $this->assertDatabaseMissing('knowledge_entries', [
            'content' => 'Short response.',
            'source' => 'ai_response'
        ]);
    }

    public function test_fallback_responses_are_not_saved_when_disabled()
    {
        Config::set('ai.self_learning.save_fallback_responses', false);

        $this->mockAIServiceResponse([
            'service_used' => 'deepseek',
            'fallback_used' => true,
            'result' => [
                'content' => 'This is a fallback response with good content that would normally be saved.',
                'model' => 'fallback-model',
            ]
        ]);

        $response = $this->postJson('/api/ask', [
            'message' => 'Test question about Laravel?'
        ]);

        $response->assertStatus(200);

        // Check that the fallback response was NOT saved
        $this->assertDatabaseMissing('knowledge_entries', [
            'content' => 'This is a fallback response with good content that would normally be saved.',
            'source' => 'ai_response'
        ]);
    }

    public function test_knowledge_base_search_enhances_ai_responses()
    {
        // Create relevant knowledge entries
        KnowledgeEntry::factory()->create([
            'content' => 'Laravel Eloquent is an ORM that provides a beautiful, simple ActiveRecord implementation.',
            'tags' => ['laravel', 'eloquent', 'orm'],
            'confidence_score' => 0.8
        ]);

        $this->mockAIServiceResponse([
            'service_used' => 'gemini',
            'fallback_used' => false,
            'result' => [
                'content' => 'Laravel Eloquent allows you to work with databases using an object-oriented approach.',
                'model' => 'gemini-pro',
            ]
        ]);

        $response = $this->postJson('/api/ask', [
            'message' => 'How does Laravel Eloquent work?',
            'use_knowledge_base' => true
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.knowledge_base.used', true)
            ->assertJsonPath('data.knowledge_base.entries_found', 1);

        // Verify knowledge base entries were found and used
        $knowledgeBase = $response->json('data.knowledge_base');
        $this->assertGreaterThan(0, count($knowledgeBase['entries']));
    }

    public function test_auto_tag_generation_works_correctly()
    {
        $this->mockAIServiceResponse([
            'service_used' => 'deepseek',
            'fallback_used' => false,
            'result' => [
                'content' => 'To create a Laravel controller, use the artisan command: php artisan make:controller UserController',
                'model' => 'deepseek-coder',
            ]
        ]);

        $response = $this->postJson('/api/ask', [
            'message' => 'How to create a controller in Laravel?',
            'tags' => ['laravel']
        ]);

        $response->assertStatus(200);

        // Check generated tags
        $entry = KnowledgeEntry::where('source', 'ai_response')->first();
        $this->assertNotNull($entry);
        
        $tags = $entry->tags;
        $this->assertContains('laravel', $tags);
        $this->assertContains('service:deepseek', $tags);
        $this->assertContains('programming', $tags);
        $this->assertContains('code', $tags);
    }

    public function test_self_learning_can_be_disabled()
    {
        Config::set('ai.self_learning.enabled', false);

        $this->mockAIServiceResponse([
            'service_used' => 'gemini',
            'fallback_used' => false,
            'result' => [
                'content' => 'This response should not be saved when self-learning is disabled.',
                'model' => 'gemini-pro',
            ]
        ]);

        $response = $this->postJson('/api/ask', [
            'message' => 'Test question?'
        ]);

        $response->assertStatus(200);

        // Verify no knowledge entry was created
        $this->assertDatabaseMissing('knowledge_entries', [
            'source' => 'ai_response'
        ]);
    }

    public function test_confidence_score_calculation_varies_by_service()
    {
        // Test DeepSeek (should have higher confidence)
        $this->mockAIServiceResponse([
            'service_used' => 'deepseek',
            'fallback_used' => false,
            'result' => [
                'content' => 'DeepSeek response with good content for testing confidence scoring.',
                'model' => 'deepseek-coder',
            ]
        ]);

        $response1 = $this->postJson('/api/ask', [
            'message' => 'DeepSeek test question?'
        ]);

        // Test HuggingFace (should have lower confidence)
        $this->mockAIServiceResponse([
            'service_used' => 'huggingface',
            'fallback_used' => false,
            'result' => [
                'content' => 'HuggingFace response with good content for testing confidence scoring.',
                'model' => 'huggingface-model',
            ]
        ]);

        $response2 = $this->postJson('/api/ask', [
            'message' => 'HuggingFace test question?'
        ]);

        $response1->assertStatus(200);
        $response2->assertStatus(200);

        // Compare confidence scores
        $deepseekEntry = KnowledgeEntry::where('content', 'LIKE', '%DeepSeek response%')->first();
        $huggingfaceEntry = KnowledgeEntry::where('content', 'LIKE', '%HuggingFace response%')->first();

        $this->assertNotNull($deepseekEntry);
        $this->assertNotNull($huggingfaceEntry);
        $this->assertGreaterThan($huggingfaceEntry->confidence_score, $deepseekEntry->confidence_score);
    }

    /**
     * Mock AI service response for testing
     */
    protected function mockAIServiceResponse(array $response)
    {
        $this->mock(\App\Services\DecisionEngine::class, function ($mock) use ($response) {
            $mock->shouldReceive('processRequest')
                ->andReturn($response);
        });
    }
}
