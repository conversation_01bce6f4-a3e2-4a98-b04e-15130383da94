<?php

namespace Tests\Feature\Database;

use Tests\TestCase;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\KnowledgeEntry;
use App\Models\User;
use Database\Seeders\KnowledgeBaseSeeder;
use Database\Seeders\ConversationSeeder;
use Database\Seeders\DatabaseSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;

class SeederTest extends TestCase
{
    use RefreshDatabase;

    public function test_knowledge_base_seeder_creates_entries()
    {
        $this->assertDatabaseCount('knowledge_entries', 0);

        $seeder = new KnowledgeBaseSeeder();
        $seeder->run();

        // Should have created multiple knowledge entries
        $this->assertDatabaseCount('knowledge_entries', 21); // Based on the entries in the seeder

        // Check that entries have required fields
        $entry = KnowledgeEntry::first();
        $this->assertNotNull($entry->content);
        $this->assertNotNull($entry->embedding_vector);
        $this->assertNotNull($entry->source);
        $this->assertNotNull($entry->tags);
        $this->assertNotNull($entry->confidence_score);
        $this->assertIsArray($entry->embedding_vector);
        $this->assertIsArray($entry->tags);
        $this->assertIsFloat($entry->confidence_score);
    }

    public function test_knowledge_base_seeder_creates_diverse_content()
    {
        $seeder = new KnowledgeBaseSeeder();
        $seeder->run();

        // Check for different sources
        $sources = KnowledgeEntry::distinct('source')->pluck('source');
        $this->assertContains('laravel_docs', $sources);
        $this->assertContains('programming_concepts', $sources);
        $this->assertContains('ai_concepts', $sources);
        $this->assertContains('database_concepts', $sources);
        $this->assertContains('web_concepts', $sources);
        $this->assertContains('software_engineering', $sources);
        $this->assertContains('security_concepts', $sources);

        // Check for different tags
        $allTags = KnowledgeEntry::pluck('tags')->flatten()->unique();
        $this->assertContains('laravel', $allTags);
        $this->assertContains('php', $allTags);
        $this->assertContains('ai', $allTags);
        $this->assertContains('database', $allTags);
        $this->assertContains('security', $allTags);
    }

    public function test_knowledge_entries_have_valid_embeddings()
    {
        $seeder = new KnowledgeBaseSeeder();
        $seeder->run();

        $entries = KnowledgeEntry::all();
        
        foreach ($entries as $entry) {
            $this->assertIsArray($entry->embedding_vector);
            $this->assertCount(384, $entry->embedding_vector); // Default embedding dimension
            
            // Check that embedding values are numeric
            foreach ($entry->embedding_vector as $value) {
                $this->assertIsNumeric($value);
            }
        }
    }

    public function test_conversation_seeder_creates_conversations_and_messages()
    {
        $this->assertDatabaseCount('conversations', 0);
        $this->assertDatabaseCount('messages', 0);

        $seeder = new ConversationSeeder();
        $seeder->run();

        // Should have created multiple conversations
        $this->assertGreaterThan(0, Conversation::count());
        $this->assertGreaterThan(0, Message::count());

        // Check conversation structure
        $conversation = Conversation::with('messages')->first();
        $this->assertNotNull($conversation->title);
        $this->assertGreaterThan(0, $conversation->messages->count());

        // Check message structure
        $message = $conversation->messages->first();
        $this->assertNotNull($message->role);
        $this->assertNotNull($message->content);
        $this->assertIn($message->role, ['user', 'assistant']);
        $this->assertIsArray($message->metadata);
    }

    public function test_conversation_seeder_creates_realistic_conversations()
    {
        $seeder = new ConversationSeeder();
        $seeder->run();

        $conversations = Conversation::with('messages')->get();

        foreach ($conversations as $conversation) {
            // Each conversation should have at least one user message and one assistant message
            $userMessages = $conversation->messages->where('role', 'user');
            $assistantMessages = $conversation->messages->where('role', 'assistant');
            
            $this->assertGreaterThan(0, $userMessages->count());
            $this->assertGreaterThan(0, $assistantMessages->count());

            // Check message metadata structure
            foreach ($conversation->messages as $message) {
                if ($message->role === 'user') {
                    $this->assertArrayHasKey('context', $message->metadata);
                    $this->assertArrayHasKey('use_knowledge_base', $message->metadata);
                    $this->assertNull($message->model_used);
                } else {
                    $this->assertArrayHasKey('service_used', $message->metadata);
                    $this->assertArrayHasKey('fallback_used', $message->metadata);
                    $this->assertNotNull($message->model_used);
                }
            }
        }
    }

    public function test_conversation_messages_have_chronological_order()
    {
        $seeder = new ConversationSeeder();
        $seeder->run();

        $conversations = Conversation::with('messages')->get();

        foreach ($conversations as $conversation) {
            $messages = $conversation->messages->sortBy('created_at');
            $previousTimestamp = null;

            foreach ($messages as $message) {
                if ($previousTimestamp) {
                    $this->assertGreaterThanOrEqual(
                        $previousTimestamp,
                        $message->created_at,
                        'Messages should be in chronological order'
                    );
                }
                $previousTimestamp = $message->created_at;
            }
        }
    }

    public function test_database_seeder_runs_all_seeders()
    {
        $this->assertDatabaseCount('knowledge_entries', 0);
        $this->assertDatabaseCount('conversations', 0);
        $this->assertDatabaseCount('messages', 0);

        $seeder = new DatabaseSeeder();
        $seeder->run();

        // Should have created data from all seeders
        $this->assertGreaterThan(0, KnowledgeEntry::count());
        $this->assertGreaterThan(0, Conversation::count());
        $this->assertGreaterThan(0, Message::count());

        // Should have created test user
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ]);
    }

    public function test_seeder_handles_duplicate_user_creation()
    {
        // Create user first
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $initialUserCount = User::count();

        // Run seeder - should not create duplicate user
        $seeder = new DatabaseSeeder();
        $seeder->run();

        $this->assertEquals($initialUserCount, User::count());
    }

    public function test_artisan_db_seed_command_works()
    {
        $this->assertDatabaseCount('knowledge_entries', 0);
        $this->assertDatabaseCount('conversations', 0);

        // Run the artisan command
        Artisan::call('db:seed');

        // Should have seeded data
        $this->assertGreaterThan(0, KnowledgeEntry::count());
        $this->assertGreaterThan(0, Conversation::count());
        $this->assertGreaterThan(0, Message::count());
    }

    public function test_individual_seeder_commands_work()
    {
        // Test knowledge base seeder
        Artisan::call('db:seed', ['--class' => 'KnowledgeBaseSeeder']);
        $this->assertGreaterThan(0, KnowledgeEntry::count());

        // Reset and test conversation seeder
        $this->refreshDatabase();
        Artisan::call('db:seed', ['--class' => 'ConversationSeeder']);
        $this->assertGreaterThan(0, Conversation::count());
    }

    public function test_seeded_knowledge_entries_are_searchable()
    {
        $seeder = new KnowledgeBaseSeeder();
        $seeder->run();

        // Test that we can find Laravel-related entries
        $laravelEntries = KnowledgeEntry::whereJsonContains('tags', 'laravel')->get();
        $this->assertGreaterThan(0, $laravelEntries->count());

        // Test that we can find entries by source
        $laravelDocsEntries = KnowledgeEntry::where('source', 'laravel_docs')->get();
        $this->assertGreaterThan(0, $laravelDocsEntries->count());

        // Test content search
        $eloquentEntries = KnowledgeEntry::where('content', 'like', '%Eloquent%')->get();
        $this->assertGreaterThan(0, $eloquentEntries->count());
    }

    public function test_seeded_conversations_support_api_functionality()
    {
        $seeder = new ConversationSeeder();
        $seeder->run();

        $conversation = Conversation::with('messages')->first();

        // Test that conversation can be retrieved via API format
        $response = $this->getJson("/api/conversations/{$conversation->id}");
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'title',
                    'messages' => [
                        '*' => [
                            'id',
                            'role',
                            'content',
                            'model_used',
                            'created_at',
                        ],
                    ],
                ],
            ]);
    }

    public function test_seeded_data_integrity()
    {
        $seeder = new DatabaseSeeder();
        $seeder->run();

        // Check that all conversations have messages
        $conversationsWithoutMessages = Conversation::doesntHave('messages')->count();
        $this->assertEquals(0, $conversationsWithoutMessages);

        // Check that all messages belong to existing conversations
        $orphanMessages = Message::whereNotIn('conversation_id', Conversation::pluck('id'))->count();
        $this->assertEquals(0, $orphanMessages);

        // Check that all knowledge entries have required fields
        $incompleteEntries = KnowledgeEntry::whereNull('content')
            ->orWhereNull('embedding_vector')
            ->orWhereNull('source')
            ->count();
        $this->assertEquals(0, $incompleteEntries);
    }
}
