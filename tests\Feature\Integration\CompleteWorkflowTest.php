<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\KnowledgeEntry;
use App\Services\DecisionEngine;
use App\Services\KnowledgeBaseService;
use App\Services\CacheService;
use App\Services\MonitoringService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;

class CompleteWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        Config::set('ai.services.deepseek.enabled', true);
        Config::set('ai.services.gemini.enabled', true);
        Config::set('ai.services.huggingface.enabled', true);
        Config::set('ai.knowledge_base.enabled', true);
        Config::set('ai.knowledge_base.use_mock_embeddings', true);
        Config::set('ai.caching.enabled', true);
        Config::set('ai.rate_limits.enabled', false); // Disable for integration tests
        
        // Clear cache
        Cache::flush();
    }

    public function test_complete_conversation_workflow_with_knowledge_base()
    {
        // Step 1: Create knowledge base entries
        $knowledgeService = app(KnowledgeBaseService::class);
        $knowledgeService->createEntry(
            'Laravel is a PHP web framework with elegant syntax and powerful features.',
            'docs',
            ['laravel', 'php', 'framework'],
            0.9
        );

        // Step 2: Start a new conversation with knowledge base search
        $response = $this->postJson('/api/ask', [
            'message' => 'What is Laravel and how do I get started?',
            'use_knowledge_base' => true,
            'knowledge_threshold' => 0.1,
            'max_knowledge_results' => 5,
            'tags' => ['laravel'],
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'message' => ['id', 'conversation_id', 'role', 'content'],
                    'conversation' => ['id', 'title'],
                    'response' => ['content', 'service_used', 'fallback_used'],
                    'knowledge_base' => ['used', 'entries_found'],
                ],
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('assistant', $response->json('data.message.role'));
        $this->assertTrue($response->json('data.knowledge_base.used'));
        $this->assertGreaterThan(0, $response->json('data.knowledge_base.entries_found'));

        $conversationId = $response->json('data.conversation.id');
        $this->assertNotNull($conversationId);

        // Step 3: Continue the conversation
        $response2 = $this->postJson('/api/ask', [
            'message' => 'Can you tell me more about Laravel Eloquent?',
            'conversation_id' => $conversationId,
            'use_knowledge_base' => true,
        ]);

        $response2->assertStatus(200);
        $this->assertEquals($conversationId, $response2->json('data.conversation.id'));

        // Step 4: Verify conversation was created with messages
        $conversation = Conversation::with('messages')->find($conversationId);
        $this->assertNotNull($conversation);
        $this->assertGreaterThanOrEqual(4, $conversation->messages->count()); // 2 user + 2 assistant messages

        // Step 5: Test conversation retrieval API
        $response3 = $this->getJson("/api/conversations/{$conversationId}");
        $response3->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'title',
                    'messages' => [
                        '*' => ['id', 'role', 'content', 'created_at'],
                    ],
                ],
            ]);

        // Step 6: Test conversations list API
        $response4 = $this->getJson('/api/conversations');
        $response4->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => ['id', 'title', 'messages_count'],
                ],
                'meta' => ['current_page', 'total'],
            ]);

        $this->assertGreaterThan(0, count($response4->json('data')));

        // Step 7: Test conversation title update
        $response5 = $this->patchJson("/api/conversations/{$conversationId}/title", [
            'title' => 'Laravel Discussion - Updated',
        ]);

        $response5->assertStatus(200);
        $this->assertEquals('Laravel Discussion - Updated', $response5->json('data.title'));

        // Step 8: Verify metrics were collected
        $response6 = $this->getJson('/api/metrics');
        $response6->assertStatus(200);

        $metrics = $response6->json('data');
        $this->assertGreaterThan(0, $metrics['interaction_metrics']['total_interactions']);
        $this->assertGreaterThan(0, $metrics['interaction_metrics']['ask_requests']);
        $this->assertGreaterThan(0, $metrics['search_metrics']['total_searches']);

        // Step 9: Test conversation deletion
        $response7 = $this->deleteJson("/api/conversations/{$conversationId}");
        $response7->assertStatus(200);

        // Verify conversation is deleted
        $this->assertNull(Conversation::find($conversationId));
    }

    public function test_ai_service_fallback_mechanism()
    {
        // Mock DecisionEngine to simulate service failures and fallbacks
        $this->mock(DecisionEngine::class, function ($mock) {
            $mock->shouldReceive('routeQuery')
                ->once()
                ->andReturn([
                    'service_used' => 'gemini',
                    'fallback_used' => true,
                    'result' => [
                        'content' => 'This response came from the fallback service (Gemini)',
                        'model' => 'gemini-pro',
                    ],
                ]);
        });

        $response = $this->postJson('/api/ask', [
            'message' => 'Test fallback mechanism',
            'preferred_service' => 'deepseek', // This will "fail" and fallback
        ]);

        $response->assertStatus(200);
        
        $responseData = $response->json('data.response');
        $this->assertEquals('gemini', $responseData['service_used']);
        $this->assertTrue($responseData['fallback_used']);
        $this->assertStringContainsString('fallback service', $responseData['content']);
    }

    public function test_caching_improves_performance_across_requests()
    {
        // First request - should cache the response
        $startTime = microtime(true);
        $response1 = $this->postJson('/api/ask', [
            'message' => 'What is caching in web applications?',
            'context' => ['performance', 'optimization'],
        ]);
        $firstRequestTime = microtime(true) - $startTime;

        $response1->assertStatus(200);

        // Second identical request - should use cache
        $startTime = microtime(true);
        $response2 = $this->postJson('/api/ask', [
            'message' => 'What is caching in web applications?',
            'context' => ['performance', 'optimization'],
        ]);
        $secondRequestTime = microtime(true) - $startTime;

        $response2->assertStatus(200);

        // Cached request should be significantly faster
        $this->assertLessThan($firstRequestTime * 0.8, $secondRequestTime);
        
        // Both responses should have the same content
        $this->assertEquals(
            $response1->json('data.response.content'),
            $response2->json('data.response.content')
        );
    }

    public function test_knowledge_base_integration_with_different_filters()
    {
        $knowledgeService = app(KnowledgeBaseService::class);

        // Create diverse knowledge entries
        $knowledgeService->createEntry(
            'Laravel Eloquent ORM provides an elegant ActiveRecord implementation.',
            'laravel_docs',
            ['laravel', 'eloquent', 'orm'],
            0.95
        );

        $knowledgeService->createEntry(
            'Python Django is a high-level web framework.',
            'python_docs',
            ['python', 'django', 'framework'],
            0.90
        );

        $knowledgeService->createEntry(
            'Database optimization techniques for better performance.',
            'performance_guide',
            ['database', 'performance', 'optimization'],
            0.85
        );

        // Test 1: Search with tag filter
        $response1 = $this->postJson('/api/ask', [
            'message' => 'Tell me about web frameworks',
            'use_knowledge_base' => true,
            'tags' => ['laravel'],
            'knowledge_threshold' => 0.1,
        ]);

        $response1->assertStatus(200);
        $kb1 = $response1->json('data.knowledge_base');
        $this->assertTrue($kb1['used']);
        $this->assertGreaterThan(0, $kb1['entries_found']);

        // Test 2: Search with source filter
        $response2 = $this->postJson('/api/ask', [
            'message' => 'Framework information',
            'use_knowledge_base' => true,
            'source_filter' => 'laravel_docs',
            'knowledge_threshold' => 0.1,
        ]);

        $response2->assertStatus(200);
        $kb2 = $response2->json('data.knowledge_base');
        $this->assertTrue($kb2['used']);

        // Test 3: Search with high threshold (should find fewer results)
        $response3 = $this->postJson('/api/ask', [
            'message' => 'Web development',
            'use_knowledge_base' => true,
            'knowledge_threshold' => 0.95,
        ]);

        $response3->assertStatus(200);
        $kb3 = $response3->json('data.knowledge_base');
        // May or may not find results depending on similarity
        $this->assertIsInt($kb3['entries_found']);
    }

    public function test_error_handling_and_recovery()
    {
        // Test 1: Invalid conversation ID
        $response1 = $this->postJson('/api/ask', [
            'message' => 'Test message',
            'conversation_id' => 999999,
        ]);

        $response1->assertStatus(422)
            ->assertJsonValidationErrors(['conversation_id']);

        // Test 2: Missing required fields
        $response2 = $this->postJson('/api/ask', []);

        $response2->assertStatus(422)
            ->assertJsonValidationErrors(['message']);

        // Test 3: Invalid preferred service
        $response3 = $this->postJson('/api/ask', [
            'message' => 'Test message',
            'preferred_service' => 'invalid_service',
        ]);

        $response3->assertStatus(422)
            ->assertJsonValidationErrors(['preferred_service']);

        // Test 4: Non-existent conversation retrieval
        $response4 = $this->getJson('/api/conversations/999999');

        $response4->assertStatus(404)
            ->assertJson([
                'success' => false,
                'error' => ['code' => 'CONVERSATION_NOT_FOUND'],
            ]);

        // Test 5: Non-existent conversation deletion
        $response5 = $this->deleteJson('/api/conversations/999999');

        $response5->assertStatus(404);
    }

    public function test_system_health_and_monitoring_integration()
    {
        // Make some API calls to generate metrics
        $this->postJson('/api/ask', ['message' => 'Health test 1']);
        $this->postJson('/api/ask', ['message' => 'Health test 2']);
        $this->getJson('/api/conversations');

        // Check system health
        $response = $this->getJson('/api/metrics');

        $response->assertStatus(200);

        $health = $response->json('data.system_health');
        $this->assertEquals('healthy', $health['status']);
        $this->assertEquals('healthy', $health['checks']['database']['status']);
        $this->assertEquals('healthy', $health['checks']['cache']['status']);

        // Check that metrics were collected
        $metrics = $response->json('data');
        $this->assertGreaterThan(0, $metrics['interaction_metrics']['total_interactions']);
        $this->assertGreaterThan(0, $metrics['interaction_metrics']['ask_requests']);

        // Test metrics clearing
        $clearResponse = $this->deleteJson('/api/metrics');
        $clearResponse->assertStatus(200);

        // Verify metrics are cleared
        $response2 = $this->getJson('/api/metrics');
        $clearedMetrics = $response2->json('data');
        $this->assertEquals(0, $clearedMetrics['interaction_metrics']['total_interactions']);
    }

    public function test_pagination_and_large_dataset_handling()
    {
        // Create multiple conversations
        $conversations = [];
        for ($i = 0; $i < 25; $i++) {
            $response = $this->postJson('/api/ask', [
                'message' => "Test conversation {$i}",
            ]);
            $conversations[] = $response->json('data.conversation.id');
        }

        // Test pagination
        $response1 = $this->getJson('/api/conversations?per_page=10&page=1');
        $response1->assertStatus(200);
        
        $meta1 = $response1->json('meta');
        $this->assertEquals(1, $meta1['current_page']);
        $this->assertEquals(10, $meta1['per_page']);
        $this->assertEquals(25, $meta1['total']);
        $this->assertCount(10, $response1->json('data'));

        // Test second page
        $response2 = $this->getJson('/api/conversations?per_page=10&page=2');
        $response2->assertStatus(200);
        
        $meta2 = $response2->json('meta');
        $this->assertEquals(2, $meta2['current_page']);
        $this->assertCount(10, $response2->json('data'));

        // Test last page
        $response3 = $this->getJson('/api/conversations?per_page=10&page=3');
        $response3->assertStatus(200);
        
        $this->assertCount(5, $response3->json('data')); // Remaining 5 conversations
    }

    public function test_concurrent_request_handling()
    {
        // Simulate concurrent requests by making multiple requests quickly
        $responses = [];
        $startTime = microtime(true);

        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->postJson('/api/ask', [
                'message' => "Concurrent request {$i}",
            ]);
        }

        $totalTime = microtime(true) - $startTime;

        // All requests should succeed
        foreach ($responses as $response) {
            $response->assertStatus(200);
            $this->assertTrue($response->json('success'));
        }

        // Should handle concurrent requests reasonably quickly
        $this->assertLessThan(10.0, $totalTime); // Under 10 seconds for 5 requests

        // Each request should have created a separate conversation
        $conversationIds = array_map(function ($response) {
            return $response->json('data.conversation.id');
        }, $responses);

        $this->assertEquals(5, count(array_unique($conversationIds)));
    }

    public function test_data_consistency_across_operations()
    {
        // Create a conversation
        $response1 = $this->postJson('/api/ask', [
            'message' => 'Initial message for consistency test',
        ]);

        $conversationId = $response1->json('data.conversation.id');

        // Add more messages to the conversation
        $this->postJson('/api/ask', [
            'message' => 'Second message',
            'conversation_id' => $conversationId,
        ]);

        $this->postJson('/api/ask', [
            'message' => 'Third message',
            'conversation_id' => $conversationId,
        ]);

        // Retrieve conversation and verify message count
        $response2 = $this->getJson("/api/conversations/{$conversationId}");
        $messages = $response2->json('data.messages');
        
        $this->assertCount(6, $messages); // 3 user + 3 assistant messages

        // Verify message order (should be chronological)
        $timestamps = array_map(function ($message) {
            return $message['created_at'];
        }, $messages);

        $sortedTimestamps = $timestamps;
        sort($sortedTimestamps);
        
        $this->assertEquals($sortedTimestamps, $timestamps);

        // Update conversation title
        $this->patchJson("/api/conversations/{$conversationId}/title", [
            'title' => 'Updated Consistency Test',
        ]);

        // Verify title was updated
        $response3 = $this->getJson("/api/conversations/{$conversationId}");
        $this->assertEquals('Updated Consistency Test', $response3->json('data.title'));

        // Verify in conversations list
        $response4 = $this->getJson('/api/conversations');
        $conversation = collect($response4->json('data'))->firstWhere('id', $conversationId);
        $this->assertEquals('Updated Consistency Test', $conversation['title']);
    }
}
