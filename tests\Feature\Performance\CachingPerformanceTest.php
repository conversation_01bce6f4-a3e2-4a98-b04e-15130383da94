<?php

namespace Tests\Feature\Performance;

use Tests\TestCase;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\KnowledgeEntry;
use App\Services\CacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

class CachingPerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected CacheService $cacheService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cacheService = new CacheService();
        
        // Enable caching for tests
        Config::set('ai.caching.enabled', true);
        Config::set('ai.knowledge_base.enabled', true);
        Config::set('ai.knowledge_base.use_mock_embeddings', true);
    }

    public function test_conversations_list_caching_improves_performance()
    {
        // Create test conversations
        $conversations = Conversation::factory(50)->create();
        foreach ($conversations as $conversation) {
            Message::factory(3)->create(['conversation_id' => $conversation->id]);
        }

        // Measure time without cache
        $startTime = microtime(true);
        $response1 = $this->getJson('/api/conversations?per_page=20&page=1');
        $timeWithoutCache = microtime(true) - $startTime;

        $response1->assertStatus(200);

        // Measure time with cache (second request)
        $startTime = microtime(true);
        $response2 = $this->getJson('/api/conversations?per_page=20&page=1');
        $timeWithCache = microtime(true) - $startTime;

        $response2->assertStatus(200);

        // Cache should make the second request faster
        $this->assertLessThan($timeWithoutCache, $timeWithCache);
        
        // Log performance metrics
        $this->addToAssertionCount(1); // Ensure test is counted
        echo "\nConversations List Performance:\n";
        echo "Without cache: " . round($timeWithoutCache * 1000, 2) . "ms\n";
        echo "With cache: " . round($timeWithCache * 1000, 2) . "ms\n";
        echo "Improvement: " . round((($timeWithoutCache - $timeWithCache) / $timeWithoutCache) * 100, 1) . "%\n";
    }

    public function test_ai_response_caching_works()
    {
        $message = 'What is Laravel?';
        $context = ['test context'];
        $preferredService = 'deepseek';

        // First request - should not be cached
        $startTime = microtime(true);
        $response1 = $this->postJson('/api/ask', [
            'message' => $message,
            'context' => $context,
            'preferred_service' => $preferredService,
        ]);
        $timeFirstRequest = microtime(true) - $startTime;

        $response1->assertStatus(200);

        // Second identical request - should use cache
        $startTime = microtime(true);
        $response2 = $this->postJson('/api/ask', [
            'message' => $message,
            'context' => $context,
            'preferred_service' => $preferredService,
        ]);
        $timeSecondRequest = microtime(true) - $startTime;

        $response2->assertStatus(200);

        // Cached request should be faster
        $this->assertLessThan($timeFirstRequest, $timeSecondRequest);

        echo "\nAI Response Caching Performance:\n";
        echo "First request: " . round($timeFirstRequest * 1000, 2) . "ms\n";
        echo "Cached request: " . round($timeSecondRequest * 1000, 2) . "ms\n";
        echo "Improvement: " . round((($timeFirstRequest - $timeSecondRequest) / $timeFirstRequest) * 100, 1) . "%\n";
    }

    public function test_knowledge_base_search_performance()
    {
        // Create knowledge entries
        for ($i = 0; $i < 100; $i++) {
            KnowledgeEntry::create([
                'content' => "Test knowledge entry {$i} about Laravel and PHP development",
                'embedding_vector' => array_fill(0, 384, rand(-100, 100) / 100),
                'source' => 'test_source',
                'tags' => ['laravel', 'php', 'test'],
                'confidence_score' => 0.8,
            ]);
        }

        // Measure knowledge base search performance
        $startTime = microtime(true);
        $response = $this->postJson('/api/ask', [
            'message' => 'Tell me about Laravel',
            'use_knowledge_base' => true,
            'knowledge_threshold' => 0.1,
            'max_knowledge_results' => 5,
        ]);
        $searchTime = microtime(true) - $startTime;

        $response->assertStatus(200);
        
        $knowledgeBase = $response->json('data.knowledge_base');
        $this->assertTrue($knowledgeBase['used']);
        $this->assertGreaterThan(0, $knowledgeBase['entries_found']);

        echo "\nKnowledge Base Search Performance:\n";
        echo "Search time: " . round($searchTime * 1000, 2) . "ms\n";
        echo "Entries found: " . $knowledgeBase['entries_found'] . "\n";
        
        // Performance should be reasonable (under 2 seconds)
        $this->assertLessThan(2.0, $searchTime);
    }

    public function test_cache_service_operations_performance()
    {
        $testData = [
            'test' => 'data',
            'array' => [1, 2, 3, 4, 5],
            'nested' => ['key' => 'value'],
        ];

        // Test cache write performance
        $startTime = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            $this->cacheService->cacheConversation($i, $testData);
        }
        $writeTime = microtime(true) - $startTime;

        // Test cache read performance
        $startTime = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            $this->cacheService->getCachedConversation($i);
        }
        $readTime = microtime(true) - $startTime;

        echo "\nCache Operations Performance:\n";
        echo "100 writes: " . round($writeTime * 1000, 2) . "ms\n";
        echo "100 reads: " . round($readTime * 1000, 2) . "ms\n";
        echo "Avg write: " . round(($writeTime / 100) * 1000, 2) . "ms\n";
        echo "Avg read: " . round(($readTime / 100) * 1000, 2) . "ms\n";

        // Cache operations should be fast
        $this->assertLessThan(1.0, $writeTime); // Under 1 second for 100 writes
        $this->assertLessThan(0.5, $readTime);  // Under 0.5 seconds for 100 reads
    }

    public function test_database_query_optimization()
    {
        // Create test data
        $conversations = Conversation::factory(20)->create();
        foreach ($conversations as $conversation) {
            Message::factory(5)->create(['conversation_id' => $conversation->id]);
        }

        // Test optimized query (with eager loading)
        $startTime = microtime(true);
        $optimizedConversations = Conversation::with(['messages' => function ($query) {
            $query->latest()->limit(1);
        }])
        ->withCount('messages')
        ->orderBy('updated_at', 'desc')
        ->limit(10)
        ->get();
        $optimizedTime = microtime(true) - $startTime;

        // Test non-optimized query (without eager loading)
        $startTime = microtime(true);
        $nonOptimizedConversations = Conversation::orderBy('updated_at', 'desc')
            ->limit(10)
            ->get();
        
        // Access relationships to trigger N+1 queries
        foreach ($nonOptimizedConversations as $conversation) {
            $conversation->messages()->latest()->first();
            $conversation->messages()->count();
        }
        $nonOptimizedTime = microtime(true) - $startTime;

        echo "\nDatabase Query Optimization:\n";
        echo "Optimized query: " . round($optimizedTime * 1000, 2) . "ms\n";
        echo "Non-optimized query: " . round($nonOptimizedTime * 1000, 2) . "ms\n";
        echo "Improvement: " . round((($nonOptimizedTime - $optimizedTime) / $nonOptimizedTime) * 100, 1) . "%\n";

        // Optimized query should be faster
        $this->assertLessThan($nonOptimizedTime, $optimizedTime);
        $this->assertEquals(10, $optimizedConversations->count());
    }

    public function test_api_response_time_benchmarks()
    {
        // Create minimal test data
        $conversation = Conversation::factory()->create();
        Message::factory(2)->create(['conversation_id' => $conversation->id]);

        $endpoints = [
            ['method' => 'GET', 'url' => '/api/health'],
            ['method' => 'GET', 'url' => '/api/info'],
            ['method' => 'GET', 'url' => '/api/conversations'],
            ['method' => 'GET', 'url' => "/api/conversations/{$conversation->id}"],
            ['method' => 'POST', 'url' => '/api/ask', 'data' => ['message' => 'Quick test']],
        ];

        echo "\nAPI Response Time Benchmarks:\n";

        foreach ($endpoints as $endpoint) {
            $startTime = microtime(true);
            
            if ($endpoint['method'] === 'GET') {
                $response = $this->getJson($endpoint['url']);
            } else {
                $response = $this->postJson($endpoint['url'], $endpoint['data'] ?? []);
            }
            
            $responseTime = microtime(true) - $startTime;
            
            $response->assertSuccessful();
            
            echo "{$endpoint['method']} {$endpoint['url']}: " . round($responseTime * 1000, 2) . "ms\n";
            
            // All endpoints should respond within reasonable time
            $this->assertLessThan(5.0, $responseTime, "Endpoint {$endpoint['url']} took too long");
        }
    }

    public function test_cache_invalidation_performance()
    {
        // Create test data in cache
        for ($i = 0; $i < 50; $i++) {
            $this->cacheService->cacheConversation($i, ['test' => 'data']);
        }

        // Test cache invalidation performance
        $startTime = microtime(true);
        $this->cacheService->invalidateConversationsList();
        $invalidationTime = microtime(true) - $startTime;

        echo "\nCache Invalidation Performance:\n";
        echo "Invalidation time: " . round($invalidationTime * 1000, 2) . "ms\n";

        // Cache invalidation should be fast
        $this->assertLessThan(1.0, $invalidationTime);
    }

    public function test_memory_usage_during_operations()
    {
        $initialMemory = memory_get_usage(true);

        // Perform memory-intensive operations
        $conversations = Conversation::factory(100)->create();
        foreach ($conversations as $conversation) {
            Message::factory(3)->create(['conversation_id' => $conversation->id]);
        }

        // Make API calls
        $this->getJson('/api/conversations?per_page=50');
        $this->postJson('/api/ask', ['message' => 'Memory test']);

        $finalMemory = memory_get_usage(true);
        $memoryUsed = $finalMemory - $initialMemory;

        echo "\nMemory Usage:\n";
        echo "Initial: " . round($initialMemory / 1024 / 1024, 2) . " MB\n";
        echo "Final: " . round($finalMemory / 1024 / 1024, 2) . " MB\n";
        echo "Used: " . round($memoryUsed / 1024 / 1024, 2) . " MB\n";

        // Memory usage should be reasonable (under 50MB for this test)
        $this->assertLessThan(50 * 1024 * 1024, $memoryUsed);
    }
}
