<?php

namespace Tests\Unit\Exceptions;

use App\Exceptions\AIServiceException;
use App\Exceptions\RateLimitException;
use App\Exceptions\ServiceUnavailableException;
use App\Exceptions\InvalidQueryException;
use PHPUnit\Framework\TestCase;

class AIServiceExceptionTest extends TestCase
{
    public function testBaseExceptionProperties()
    {
        $exception = new AIServiceException(
            'Test error message',
            'test-service',
            ['error_code' => 'test_error'],
            500
        );

        $this->assertEquals('Test error message', $exception->getMessage());
        $this->assertEquals('test-service', $exception->getServiceName());
        $this->assertEquals(['error_code' => 'test_error'], $exception->getErrorDetails());
        $this->assertEquals(500, $exception->getCode());
    }

    public function testExceptionToArray()
    {
        $exception = new AIServiceException(
            'Test error message',
            'test-service',
            ['error_code' => 'test_error'],
            500
        );

        $array = $exception->toArray();

        $this->assertFalse($array['success']);
        $this->assertEquals('AIServiceException', $array['error']['code']);
        $this->assertEquals('Test error message', $array['error']['message']);
        $this->assertEquals(['error_code' => 'test_error'], $array['error']['details']);
        $this->assertEquals('test-service', $array['error']['service']);
    }

    public function testServiceUnavailableException()
    {
        $exception = new ServiceUnavailableException(
            'test-service',
            ['status_code' => 503]
        );

        $this->assertEquals('AI service temporarily unavailable', $exception->getMessage());
        $this->assertEquals('test-service', $exception->getServiceName());
        $this->assertEquals(['status_code' => 503], $exception->getErrorDetails());
        $this->assertEquals(503, $exception->getCode());
    }

    public function testRateLimitException()
    {
        $exception = new RateLimitException(
            'test-service',
            60,
            ['limit' => 100, 'remaining' => 0]
        );

        $this->assertEquals('API rate limit exceeded', $exception->getMessage());
        $this->assertEquals('test-service', $exception->getServiceName());
        $this->assertEquals(['limit' => 100, 'remaining' => 0], $exception->getErrorDetails());
        $this->assertEquals(429, $exception->getCode());
        $this->assertEquals(60, $exception->getRetryAfter());

        $array = $exception->toArray();
        $this->assertEquals(60, $array['error']['retry_after']);
    }

    public function testInvalidQueryException()
    {
        $exception = new InvalidQueryException(
            'Invalid query format',
            ['field' => 'message', 'error' => 'required']
        );

        $this->assertEquals('Invalid query format', $exception->getMessage());
        $this->assertNull($exception->getServiceName());
        $this->assertEquals(['field' => 'message', 'error' => 'required'], $exception->getErrorDetails());
        $this->assertEquals(400, $exception->getCode());
    }

    public function testCustomInvalidQueryException()
    {
        $exception = new InvalidQueryException(
            'Custom error message',
            ['field' => 'message', 'error' => 'too_long']
        );

        $this->assertEquals('Custom error message', $exception->getMessage());
    }
}