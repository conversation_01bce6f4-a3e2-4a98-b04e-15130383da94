<?php

namespace Tests\Unit\Exceptions;

use App\Exceptions\AIServiceException;
use App\Exceptions\RateLimitException;
use PHPUnit\Framework\TestCase;

class RateLimitExceptionTest extends TestCase
{
    public function testRateLimitExceptionIsAIServiceException()
    {
        $exception = new RateLimitException();
        
        $this->assertInstanceOf(AIServiceException::class, $exception);
    }
    
    public function testDefaultMessage()
    {
        $exception = new RateLimitException();
        
        $this->assertEquals('Rate limit exceeded for AI service', $exception->getMessage());
    }
    
    public function testDefaultCode()
    {
        $exception = new RateLimitException();
        
        $this->assertEquals(429, $exception->getCode());
    }
    
    public function testCustomMessage()
    {
        $exception = new RateLimitException('Custom rate limit message');
        
        $this->assertEquals('Custom rate limit message', $exception->getMessage());
    }
    
    public function testServiceName()
    {
        $exception = new RateLimitException('Rate limited', 'test-service');
        
        $this->assertEquals('test-service', $exception->getServiceName());
    }
    
    public function testErrorDetails()
    {
        $errorDetails = ['limit' => 60, 'reset_at' => '2023-01-01T00:00:00Z'];
        $exception = new RateLimitException('Rate limited', 'test-service', $errorDetails);
        
        $this->assertEquals($errorDetails, $exception->getErrorDetails());
    }
    
    public function testToArray()
    {
        $errorDetails = ['limit' => 60, 'reset_at' => '2023-01-01T00:00:00Z'];
        $exception = new RateLimitException('Rate limited', 'test-service', $errorDetails);
        
        $array = $exception->toArray();
        
        $this->assertIsArray($array);
        $this->assertFalse($array['success']);
        $this->assertEquals('RateLimitException', $array['error']['code']);
        $this->assertEquals('Rate limited', $array['error']['message']);
        $this->assertEquals($errorDetails, $array['error']['details']);
        $this->assertEquals('test-service', $array['error']['service']);
    }
}