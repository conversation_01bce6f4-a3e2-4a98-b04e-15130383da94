<?php

namespace Tests\Unit\Exceptions;

use App\Exceptions\AIServiceException;
use App\Exceptions\ServiceUnavailableException;
use PHPUnit\Framework\TestCase;

class ServiceUnavailableExceptionTest extends TestCase
{
    public function testServiceUnavailableExceptionIsAIServiceException()
    {
        $exception = new ServiceUnavailableException();
        
        $this->assertInstanceOf(AIServiceException::class, $exception);
    }
    
    public function testDefaultMessage()
    {
        $exception = new ServiceUnavailableException();
        
        $this->assertEquals('AI service is temporarily unavailable', $exception->getMessage());
    }
    
    public function testDefaultCode()
    {
        $exception = new ServiceUnavailableException();
        
        $this->assertEquals(503, $exception->getCode());
    }
    
    public function testCustomMessage()
    {
        $exception = new ServiceUnavailableException('Custom unavailable message');
        
        $this->assertEquals('Custom unavailable message', $exception->getMessage());
    }
    
    public function testServiceName()
    {
        $exception = new ServiceUnavailableException('Service down', 'test-service');
        
        $this->assertEquals('test-service', $exception->getServiceName());
    }
    
    public function testErrorDetails()
    {
        $errorDetails = ['reason' => 'maintenance', 'estimated_return' => '1 hour'];
        $exception = new ServiceUnavailableException('Service down', 'test-service', $errorDetails);
        
        $this->assertEquals($errorDetails, $exception->getErrorDetails());
    }
    
    public function testToArray()
    {
        $errorDetails = ['reason' => 'maintenance'];
        $exception = new ServiceUnavailableException('Service down', 'test-service', $errorDetails);
        
        $array = $exception->toArray();
        
        $this->assertIsArray($array);
        $this->assertFalse($array['success']);
        $this->assertEquals('ServiceUnavailableException', $array['error']['code']);
        $this->assertEquals('Service down', $array['error']['message']);
        $this->assertEquals($errorDetails, $array['error']['details']);
        $this->assertEquals('test-service', $array['error']['service']);
    }
}