<?php

namespace Tests\Unit\Http\Requests;

use Tests\TestCase;
use App\Http\Requests\AskRequest;
use App\Models\Conversation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;

class AskRequestTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_valid_request_passes_validation()
    {
        $data = [
            'message' => 'Hello, how can you help me with Lara<PERSON>?',
            'use_knowledge_base' => true,
            'knowledge_threshold' => 0.8,
            'max_knowledge_results' => 5,
        ];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_message_is_required()
    {
        $data = [];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('message', $validator->errors()->toArray());
    }

    public function test_message_cannot_be_empty()
    {
        $data = ['message' => ''];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('message', $validator->errors()->toArray());
    }

    public function test_message_cannot_exceed_max_length()
    {
        $data = ['message' => str_repeat('a', 10001)];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('message', $validator->errors()->toArray());
    }

    public function test_conversation_id_must_exist()
    {
        $data = [
            'message' => 'Test message',
            'conversation_id' => 999999, // Non-existent ID
        ];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('conversation_id', $validator->errors()->toArray());
    }

    public function test_valid_conversation_id_passes()
    {
        $conversation = Conversation::create(['title' => 'Test Conversation']);
        
        $data = [
            'message' => 'Test message',
            'conversation_id' => $conversation->id,
        ];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_preferred_service_must_be_valid()
    {
        $data = [
            'message' => 'Test message',
            'preferred_service' => 'invalid_service',
        ];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('preferred_service', $validator->errors()->toArray());
    }

    public function test_valid_preferred_services_pass()
    {
        $validServices = ['deepseek', 'gemini', 'huggingface'];

        foreach ($validServices as $service) {
            $data = [
                'message' => 'Test message',
                'preferred_service' => $service,
            ];

            $request = new AskRequest();
            $validator = Validator::make($data, $request->rules());

            $this->assertTrue($validator->passes(), "Service {$service} should be valid");
        }
    }

    public function test_knowledge_threshold_must_be_between_0_and_1()
    {
        $invalidThresholds = [-0.1, 1.1, 2.0];

        foreach ($invalidThresholds as $threshold) {
            $data = [
                'message' => 'Test message',
                'knowledge_threshold' => $threshold,
            ];

            $request = new AskRequest();
            $validator = Validator::make($data, $request->rules());

            $this->assertFalse($validator->passes(), "Threshold {$threshold} should be invalid");
            $this->assertArrayHasKey('knowledge_threshold', $validator->errors()->toArray());
        }
    }

    public function test_valid_knowledge_thresholds_pass()
    {
        $validThresholds = [0.0, 0.5, 0.7, 1.0];

        foreach ($validThresholds as $threshold) {
            $data = [
                'message' => 'Test message',
                'knowledge_threshold' => $threshold,
            ];

            $request = new AskRequest();
            $validator = Validator::make($data, $request->rules());

            $this->assertTrue($validator->passes(), "Threshold {$threshold} should be valid");
        }
    }

    public function test_max_knowledge_results_has_limits()
    {
        $data = [
            'message' => 'Test message',
            'max_knowledge_results' => 0,
        ];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('max_knowledge_results', $validator->errors()->toArray());

        $data['max_knowledge_results'] = 21;
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('max_knowledge_results', $validator->errors()->toArray());
    }

    public function test_tags_validation()
    {
        // Too many tags
        $data = [
            'message' => 'Test message',
            'tags' => array_fill(0, 11, 'tag'),
        ];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('tags', $validator->errors()->toArray());

        // Invalid tag format
        $data['tags'] = ['valid_tag', 'invalid tag with spaces'];
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('tags.1', $validator->errors()->toArray());
    }

    public function test_context_validation()
    {
        // Too many context items
        $data = [
            'message' => 'Test message',
            'context' => array_fill(0, 11, 'context item'),
        ];

        $request = new AskRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('context', $validator->errors()->toArray());

        // Context item too long
        $data['context'] = [str_repeat('a', 1001)];
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('context.0', $validator->errors()->toArray());
    }

    public function test_harmful_content_detection()
    {
        $harmfulMessages = [
            'SELECT * FROM users WHERE id = 1',
            '<script>alert("xss")</script>',
            'javascript:alert("xss")',
            'onclick="alert(1)"',
            'ls -la | grep password',
            '../../../etc/passwd',
        ];

        foreach ($harmfulMessages as $message) {
            $data = ['message' => $message];

            $request = new AskRequest();
            $validator = Validator::make($data, $request->rules());

            $this->assertFalse($validator->passes(), "Message '{$message}' should be detected as harmful");
            $this->assertArrayHasKey('message', $validator->errors()->toArray());
        }
    }

    public function test_message_sanitization()
    {
        $request = new AskRequest();
        
        // Test with data that needs sanitization
        $request->merge([
            'message' => "  Hello\r\nWorld\t\0  ",
        ]);
        
        $request->prepareForValidation();
        
        $sanitized = $request->input('message');
        
        // Should remove null bytes, normalize whitespace, and trim
        $this->assertStringNotContainsString("\0", $sanitized);
        $this->assertStringNotContainsString("\r", $sanitized);
        $this->assertEquals('Hello World', $sanitized);
    }

    public function test_helper_methods()
    {
        $data = [
            'message' => 'Test message',
            'conversation_id' => null,
            'context' => ['context1', 'context2'],
            'preferred_service' => 'deepseek',
            'use_knowledge_base' => false,
            'knowledge_threshold' => 0.8,
            'max_knowledge_results' => 10,
            'tags' => ['tag1', 'tag2'],
            'source_filter' => 'docs',
        ];

        $request = new AskRequest($data);
        $request->setValidator(Validator::make($data, $request->rules()));

        $this->assertEquals('Test message', $request->getMessage());
        $this->assertNull($request->getConversationId());
        $this->assertEquals(['context1', 'context2'], $request->getContext());
        $this->assertEquals('deepseek', $request->getPreferredService());
        $this->assertFalse($request->shouldUseKnowledgeBase());
        $this->assertEquals(0.8, $request->getKnowledgeThreshold());
        $this->assertEquals(10, $request->getMaxKnowledgeResults());
        $this->assertEquals(['tag1', 'tag2'], $request->getTags());
        $this->assertEquals('docs', $request->getSourceFilter());
    }
}
