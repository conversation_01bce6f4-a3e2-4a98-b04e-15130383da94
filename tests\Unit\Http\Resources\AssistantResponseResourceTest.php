<?php

namespace Tests\Unit\Http\Resources;

use Tests\TestCase;
use App\Http\Resources\AssistantResponseResource;
use App\Models\Message;
use App\Models\Conversation;
use App\Models\KnowledgeEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;

class AssistantResponseResourceTest extends TestCase
{
    use RefreshDatabase;

    public function test_assistant_response_resource_basic_structure()
    {
        $responseData = [
            'response' => [
                'content' => 'This is the AI response',
                'model_used' => 'deepseek',
                'service_used' => 'deepseek',
                'fallback_used' => false,
                'processing_time' => 1.5,
            ],
        ];

        $resource = new AssistantResponseResource($responseData);
        $request = Request::create('/test');
        
        $array = $resource->toArray($request);

        $this->assertTrue($array['success']);
        $this->assertArrayHasKey('data', $array);
        $this->assertArrayHasKey('response', $array['data']);
        $this->assertArrayHasKey('timestamp', $array);

        $response = $array['data']['response'];
        $this->assertEquals('This is the AI response', $response['content']);
        $this->assertEquals('deepseek', $response['model_used']);
        $this->assertEquals('deepseek', $response['service_used']);
        $this->assertFalse($response['fallback_used']);
        $this->assertEquals(1.5, $response['processing_time']);
    }

    public function test_assistant_response_with_message_and_conversation()
    {
        $conversation = Conversation::create(['title' => 'Test Conversation']);
        
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'content' => 'AI response content',
            'model_used' => 'gemini',
        ]);

        $responseData = [
            'message' => $message,
            'conversation' => $conversation,
            'response' => [
                'content' => 'AI response content',
                'model_used' => 'gemini',
                'service_used' => 'gemini',
                'fallback_used' => false,
            ],
        ];

        $resource = new AssistantResponseResource($responseData);
        $request = Request::create('/test');
        
        $array = $resource->toArray($request);

        $this->assertArrayHasKey('message', $array['data']);
        $this->assertArrayHasKey('conversation', $array['data']);

        $this->assertEquals($message->id, $array['data']['message']['id']);
        $this->assertEquals($conversation->id, $array['data']['conversation']['id']);
    }

    public function test_assistant_response_with_knowledge_base_data()
    {
        $knowledgeEntry = KnowledgeEntry::create([
            'content' => 'Laravel is a PHP framework',
            'embedding_vector' => array_fill(0, 384, 0.1),
            'source' => 'docs',
            'tags' => ['laravel', 'php'],
            'confidence_score' => 0.9,
        ]);

        $responseData = [
            'response' => [
                'content' => 'Based on the knowledge base...',
                'model_used' => 'deepseek',
                'service_used' => 'deepseek',
                'fallback_used' => false,
            ],
            'knowledge_base' => [
                'used' => true,
                'entries_found' => 1,
                'entries' => [$knowledgeEntry],
                'threshold_used' => 0.7,
            ],
        ];

        $resource = new AssistantResponseResource($responseData);
        $request = Request::create('/test');
        
        $array = $resource->toArray($request);

        $this->assertArrayHasKey('knowledge_base', $array['data']);
        
        $kb = $array['data']['knowledge_base'];
        $this->assertTrue($kb['used']);
        $this->assertEquals(1, $kb['entries_found']);
        $this->assertEquals(0.7, $kb['threshold_used']);
        
        // Entries should not be included by default
        $this->assertArrayNotHasKey('entries', $kb);
    }

    public function test_assistant_response_includes_knowledge_entries_when_requested()
    {
        $knowledgeEntry = KnowledgeEntry::create([
            'content' => 'Laravel is a PHP framework',
            'embedding_vector' => array_fill(0, 384, 0.1),
            'source' => 'docs',
            'tags' => ['laravel', 'php'],
            'confidence_score' => 0.9,
        ]);

        $responseData = [
            'response' => [
                'content' => 'Based on the knowledge base...',
                'model_used' => 'deepseek',
                'service_used' => 'deepseek',
                'fallback_used' => false,
            ],
            'knowledge_base' => [
                'used' => true,
                'entries_found' => 1,
                'entries' => [$knowledgeEntry],
                'threshold_used' => 0.7,
            ],
        ];

        $resource = new AssistantResponseResource($responseData);
        $request = Request::create('/test', 'GET', ['include_knowledge_entries' => 'true']);
        
        $array = $resource->toArray($request);

        $this->assertArrayHasKey('knowledge_base', $array['data']);
        $this->assertArrayHasKey('entries', $array['data']['knowledge_base']);
        $this->assertCount(1, $array['data']['knowledge_base']['entries']);
        
        $entry = $array['data']['knowledge_base']['entries'][0];
        $this->assertEquals($knowledgeEntry->id, $entry['id']);
        $this->assertEquals('Laravel is a PHP framework', $entry['content']);
    }

    public function test_assistant_response_with_metadata()
    {
        $responseData = [
            'response' => [
                'content' => 'Response with metadata',
                'model_used' => 'huggingface',
                'service_used' => 'huggingface',
                'fallback_used' => true,
            ],
            'metadata' => [
                'query_analysis' => 'creative',
                'confidence' => 0.85,
                'tokens_used' => 150,
            ],
        ];

        $resource = new AssistantResponseResource($responseData);
        $request = Request::create('/test');
        
        $array = $resource->toArray($request);

        $this->assertArrayHasKey('metadata', $array['data']);
        $this->assertEquals('creative', $array['data']['metadata']['query_analysis']);
        $this->assertEquals(0.85, $array['data']['metadata']['confidence']);
        $this->assertEquals(150, $array['data']['metadata']['tokens_used']);
    }

    public function test_assistant_response_includes_meta_information()
    {
        $responseData = [
            'response' => [
                'content' => 'Test response',
                'model_used' => 'deepseek',
                'service_used' => 'deepseek',
                'fallback_used' => false,
            ],
        ];

        $resource = new AssistantResponseResource($responseData);
        $request = Request::create('/api/ask', 'POST');
        $request->headers->set('X-Request-ID', 'test-request-123');
        
        $response = $resource->response($request);
        $data = $response->getData(true);

        $this->assertArrayHasKey('meta', $data);
        $this->assertEquals('1.0', $data['meta']['version']);
        $this->assertEquals('http://localhost/api/ask', $data['meta']['api_endpoint']);
        $this->assertEquals('test-request-123', $data['meta']['request_id']);
    }

    public function test_assistant_response_timestamp_format()
    {
        $responseData = [
            'response' => [
                'content' => 'Test response',
                'model_used' => 'deepseek',
                'service_used' => 'deepseek',
                'fallback_used' => false,
            ],
        ];

        $resource = new AssistantResponseResource($responseData);
        $request = Request::create('/test');
        
        $array = $resource->toArray($request);

        $this->assertArrayHasKey('timestamp', $array);
        $this->assertIsString($array['timestamp']);
        
        // Check ISO 8601 format
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{6}Z$/', $array['timestamp']);
    }
}
