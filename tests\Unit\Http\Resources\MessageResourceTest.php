<?php

namespace Tests\Unit\Http\Resources;

use Tests\TestCase;
use App\Http\Resources\MessageResource;
use App\Models\Message;
use App\Models\Conversation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;

class MessageResourceTest extends TestCase
{
    use RefreshDatabase;

    public function test_message_resource_structure()
    {
        $conversation = Conversation::create(['title' => 'Test Conversation']);
        
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => 'Hello, how are you?',
            'model_used' => null,
            'metadata' => ['test' => 'data'],
        ]);

        $resource = new MessageResource($message);
        $request = Request::create('/test');
        
        $array = $resource->toArray($request);

        $this->assertArrayHasKey('id', $array);
        $this->assertArrayHasKey('conversation_id', $array);
        $this->assertArrayHasKey('role', $array);
        $this->assertArrayHasKey('content', $array);
        $this->assertArrayHasKey('model_used', $array);
        $this->assertArrayHasKey('metadata', $array);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);

        $this->assertEquals($message->id, $array['id']);
        $this->assertEquals($conversation->id, $array['conversation_id']);
        $this->assertEquals('user', $array['role']);
        $this->assertEquals('Hello, how are you?', $array['content']);
        $this->assertNull($array['model_used']);
        $this->assertEquals(['test' => 'data'], $array['metadata']);
    }

    public function test_message_resource_with_loaded_conversation()
    {
        $conversation = Conversation::create(['title' => 'Test Conversation']);
        
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'content' => 'I am doing well, thank you!',
            'model_used' => 'deepseek',
        ]);

        // Load the conversation relationship
        $message->load('conversation');

        $resource = new MessageResource($message);
        $request = Request::create('/test');
        
        $array = $resource->toArray($request);

        $this->assertArrayHasKey('conversation', $array);
        $this->assertIsArray($array['conversation']);
        $this->assertEquals($conversation->id, $array['conversation']['id']);
        $this->assertEquals('Test Conversation', $array['conversation']['title']);
        $this->assertArrayHasKey('created_at', $array['conversation']);
    }

    public function test_message_resource_without_metadata()
    {
        $conversation = Conversation::create(['title' => 'Test Conversation']);
        
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => 'Test message',
            'model_used' => null,
            'metadata' => null,
        ]);

        $resource = new MessageResource($message);
        $request = Request::create('/test');
        
        $array = $resource->toArray($request);

        $this->assertArrayNotHasKey('metadata', $array);
    }

    public function test_message_resource_datetime_formatting()
    {
        $conversation = Conversation::create(['title' => 'Test Conversation']);
        
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => 'Test message',
        ]);

        $resource = new MessageResource($message);
        $request = Request::create('/test');
        
        $array = $resource->toArray($request);

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        
        // Check ISO 8601 format
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{6}Z$/', $array['created_at']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{6}Z$/', $array['updated_at']);
    }
}
