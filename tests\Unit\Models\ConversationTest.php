<?php

namespace Tests\Unit\Models;

use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class ConversationTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_has_many_messages()
    {
        $conversation = Conversation::factory()->create();
        $message = Message::factory()->create(['conversation_id' => $conversation->id]);

        $this->assertInstanceOf(Message::class, $conversation->messages->first());
        $this->assertEquals($message->id, $conversation->messages->first()->id);
    }

    #[Test]
    public function it_can_generate_title_from_first_message()
    {
        $conversation = Conversation::factory()->create(['title' => 'Default Title']);
        $longMessage = 'This is a very long message that should be truncated when used as a conversation title in the system';
        
        $title = $conversation->generateTitle($longMessage);
        
        $this->assertEquals('This is a very long message that should ...', $title);
        $this->assertEquals($title, $conversation->fresh()->title);
    }

    #[Test]
    public function it_can_get_latest_message()
    {
        $conversation = Conversation::factory()->create();
        
        // Clear any existing messages
        Message::where('conversation_id', $conversation->id)->delete();
        
        // Create messages with explicit IDs to ensure order
        $firstMessage = new Message([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => 'First message',
            'created_at' => now()->subMinutes(5)
        ]);
        $firstMessage->save();
        
        $latestMessage = new Message([
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'content' => 'Latest message',
            'created_at' => now()
        ]);
        $latestMessage->save();

        $result = $conversation->getLatestMessage();
        
        $this->assertEquals($latestMessage->id, $result->id);
    }

    #[Test]
    public function it_can_get_context_messages()
    {
        $conversation = Conversation::factory()->create();
        
        // Create 5 messages with user role
        $messages = Message::factory()->count(5)->state(['role' => 'user'])->create([
            'conversation_id' => $conversation->id
        ]);
        
        $contextMessages = $conversation->getContextMessages();
        
        $this->assertCount(5, $contextMessages);
        $this->assertEquals('user', $contextMessages[0]['role']);
        $this->assertArrayHasKey('content', $contextMessages[0]);
    }
}