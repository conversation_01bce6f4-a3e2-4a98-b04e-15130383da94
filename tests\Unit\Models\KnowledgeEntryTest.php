<?php

namespace Tests\Unit\Models;

use App\Models\KnowledgeEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class KnowledgeEntryTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_can_filter_by_source()
    {
        KnowledgeEntry::factory()->create(['source' => 'source1']);
        KnowledgeEntry::factory()->create(['source' => 'source2']);

        $entries = KnowledgeEntry::fromSource('source1')->get();
        
        $this->assertCount(1, $entries);
        $this->assertEquals('source1', $entries->first()->source);
    }

    #[Test]
    public function it_can_filter_by_tag()
    {
        KnowledgeEntry::factory()->create(['tags' => ['tag1', 'tag2']]);
        KnowledgeEntry::factory()->create(['tags' => ['tag3']]);

        $entries = KnowledgeEntry::withTag('tag1')->get();
        
        $this->assertCount(1, $entries);
        $this->assertContains('tag1', $entries->first()->tags);
    }

    #[Test]
    public function it_can_find_similar_entries()
    {
        // Create entries with different embedding vectors
        KnowledgeEntry::factory()->create([
            'content' => 'Test content 1',
            'embedding_vector' => [0.1, 0.2, 0.3],
        ]);
        
        KnowledgeEntry::factory()->create([
            'content' => 'Test content 2',
            'embedding_vector' => [0.9, 0.8, 0.7],
        ]);

        // This should be more similar to the first entry
        $queryVector = [0.15, 0.25, 0.35];
        
        // Use a higher threshold to get only one result
        $similarEntries = KnowledgeEntry::findSimilar($queryVector, 0.95);
        
        $this->assertCount(1, $similarEntries);
        $this->assertEquals('Test content 1', $similarEntries->first()->content);
        $this->assertGreaterThan(0.9, $similarEntries->first()->similarity_score);
    }

    #[Test]
    public function it_can_add_tags()
    {
        $entry = KnowledgeEntry::factory()->create(['tags' => ['tag1']]);
        
        $entry->addTags('tag2');
        $this->assertEquals(['tag1', 'tag2'], $entry->fresh()->tags);
        
        $entry->addTags(['tag3', 'tag4']);
        $this->assertEquals(['tag1', 'tag2', 'tag3', 'tag4'], $entry->fresh()->tags);
        
        // Test duplicate tags are not added
        $entry->addTags('tag1');
        $this->assertEquals(['tag1', 'tag2', 'tag3', 'tag4'], $entry->fresh()->tags);
    }

    #[Test]
    public function it_can_remove_tags()
    {
        $entry = KnowledgeEntry::factory()->create(['tags' => ['tag1', 'tag2', 'tag3']]);
        
        $entry->removeTags('tag2');
        $this->assertEquals(['tag1', 'tag3'], $entry->fresh()->tags);
        
        $entry->removeTags(['tag1', 'tag3']);
        $this->assertEquals([], $entry->fresh()->tags);
    }

    #[Test]
    public function it_calculates_cosine_similarity_correctly()
    {
        // Using reflection to test protected method
        $method = new \ReflectionMethod(KnowledgeEntry::class, 'cosineSimilarity');
        $method->setAccessible(true);
        
        $vectorA = [1, 0, 0];
        $vectorB = [1, 0, 0];
        $this->assertEquals(1.0, $method->invoke(null, $vectorA, $vectorB));
        
        $vectorA = [1, 0, 0];
        $vectorB = [0, 1, 0];
        $this->assertEquals(0.0, $method->invoke(null, $vectorA, $vectorB));
        
        $vectorA = [1, 1, 0];
        $vectorB = [1, 0, 0];
        $this->assertEqualsWithDelta(0.7071, $method->invoke(null, $vectorA, $vectorB), 0.0001);
    }
}