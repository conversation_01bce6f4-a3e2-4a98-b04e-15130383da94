<?php

namespace Tests\Unit\Models;

use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class MessageTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_belongs_to_a_conversation()
    {
        $conversation = Conversation::factory()->create();
        $message = Message::factory()->create(['conversation_id' => $conversation->id]);

        $this->assertInstanceOf(Conversation::class, $message->conversation);
        $this->assertEquals($conversation->id, $message->conversation->id);
    }

    #[Test]
    public function it_can_scope_to_user_messages()
    {
        Message::factory()->create(['role' => 'user']);
        Message::factory()->create(['role' => 'assistant']);

        $userMessages = Message::user()->get();
        
        $this->assertCount(1, $userMessages);
        $this->assertEquals('user', $userMessages->first()->role);
    }

    #[Test]
    public function it_can_scope_to_assistant_messages()
    {
        Message::factory()->create(['role' => 'user']);
        Message::factory()->create(['role' => 'assistant']);

        $assistantMessages = Message::assistant()->get();
        
        $this->assertCount(1, $assistantMessages);
        $this->assertEquals('assistant', $assistantMessages->first()->role);
    }

    #[Test]
    public function it_can_add_metadata()
    {
        $message = Message::factory()->create(['metadata' => ['key1' => 'value1']]);
        
        $message->addMetadata(['key2' => 'value2']);
        
        $this->assertEquals([
            'key1' => 'value1',
            'key2' => 'value2'
        ], $message->fresh()->metadata);
    }

    #[Test]
    public function it_can_get_formatted_content()
    {
        $content = 'This is a test message';
        $message = Message::factory()->create(['content' => $content]);
        
        $this->assertEquals($content, $message->getFormattedContentAttribute());
    }
}