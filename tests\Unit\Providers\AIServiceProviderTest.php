<?php

namespace Tests\Unit\Providers;

use App\Providers\AIServiceProvider;
use App\Services\AI\Contracts\AIServiceInterface;
use PHPUnit\Framework\TestCase;

class AIServiceProviderTest extends TestCase
{
    /**
     * Test that the AIServiceProvider class exists and has the required methods
     */
    public function testProviderExists()
    {
        $this->assertTrue(class_exists(AIServiceProvider::class));
        $this->assertTrue(method_exists(AIServiceProvider::class, 'register'));
        $this->assertTrue(method_exists(AIServiceProvider::class, 'boot'));
        $this->assertTrue(method_exists(AIServiceProvider::class, 'registerAIServices'));
    }
    
    /**
     * Test that the AIServiceInterface exists with the required methods
     */
    public function testInterfaceExists()
    {
        $this->assertTrue(interface_exists(AIServiceInterface::class));
        $this->assertTrue(method_exists(AIServiceInterface::class, 'sendMessage'));
        $this->assertTrue(method_exists(AIServiceInterface::class, 'isAvailable'));
        $this->assertTrue(method_exists(AIServiceInterface::class, 'getModelName'));
    }
}