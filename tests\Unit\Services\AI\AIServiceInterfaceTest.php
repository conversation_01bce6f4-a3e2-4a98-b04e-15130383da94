<?php

namespace Tests\Unit\Services\AI;

use App\Exceptions\AIServiceException;
use PHPUnit\Framework\TestCase;

class AIServiceInterfaceTest extends TestCase
{
    public function testSendMessage()
    {
        $service = new MockAIService('test-model');
        
        $response = $service->sendMessage('Hello, world!');
        
        $this->assertIsArray($response);
        $this->assertArrayHasKey('content', $response);
        $this->assertArrayHasKey('model', $response);
        $this->assertEquals('test-model', $response['model']);
        $this->assertStringContainsString('Hello, world!', $response['content']);
    }
    
    public function testSendMessageWithContext()
    {
        $service = new MockAIService();
        
        $context = [
            'conversation_id' => 123,
            'history' => [
                ['role' => 'user', 'content' => 'Previous message'],
                ['role' => 'assistant', 'content' => 'Previous response']
            ]
        ];
        
        $response = $service->sendMessage('Hello with context', $context);
        
        $this->assertIsArray($response);
        $this->assertArrayHasKey('content', $response);
        $this->assertStringContainsString('Hello with context', $response['content']);
    }
    
    public function testServiceUnavailable()
    {
        $service = new MockAIService('test-model', false, 'Service is down for maintenance');
        
        $this->assertFalse($service->isAvailable());
        
        $this->expectException(AIServiceException::class);
        $this->expectExceptionMessage('Service is down for maintenance');
        
        $service->sendMessage('This should fail');
    }
    
    public function testGetModelName()
    {
        $service = new MockAIService('custom-model-name');
        
        $this->assertEquals('custom-model-name', $service->getModelName());
    }
    
    public function testServiceAvailabilityChange()
    {
        $service = new MockAIService('test-model', true);
        
        $this->assertTrue($service->isAvailable());
        
        $service->setAvailable(false);
        
        $this->assertFalse($service->isAvailable());
    }
}