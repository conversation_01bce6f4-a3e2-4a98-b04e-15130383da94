<?php

namespace Tests\Unit\Services\AI;

use App\Exceptions\AIServiceException;
use App\Exceptions\RateLimitException;
use App\Exceptions\ServiceUnavailableException;
use App\Services\AI\DeepSeekService;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\TestCase;

class DeepSeekServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any mocked responses
        Http::fake()->assertNothingSent();
    }

    public function testConstructor()
    {
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "test-model",
            ["temperature" => 0.5]
        );
        
        $this->assertEquals("test-model", $service->getModelName());
        $this->assertTrue($service->isAvailable());
    }
    
    public function testIsAvailableReturnsFalseWhenApiKeyIsEmpty()
    {
        $service = new DeepSeekService(
            "",
            "https://api.test.com",
            "test-model"
        );
        
        $this->assertFalse($service->isAvailable());
    }
    
    public function testSendMessageThrowsExceptionWhenApiKeyIsEmpty()
    {
        $service = new DeepSeekService(
            "",
            "https://api.test.com",
            "test-model"
        );
        
        $this->expectException(ServiceUnavailableException::class);
        $this->expectExceptionMessage("DeepSeek API is not available: API key is missing");
        
        $service->sendMessage("Test message");
    }
    
    public function testSendMessageSuccess()
    {
        // Mock the HTTP response
        Http::fake([
            "*" => Http::response([
                "id" => "chatcmpl-123",
                "object" => "chat.completion",
                "created" => 1677858242,
                "model" => "deepseek-chat",
                "choices" => [
                    [
                        "message" => [
                            "role" => "assistant",
                            "content" => "This is a test response"
                        ],
                        "finish_reason" => "stop",
                        "index" => 0
                    ]
                ],
                "usage" => [
                    "prompt_tokens" => 10,
                    "completion_tokens" => 20,
                    "total_tokens" => 30
                ]
            ], 200)
        ]);
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat",
            ["temperature" => 0.7, "max_tokens" => 1000]
        );
        
        $response = $service->sendMessage("Test message");
        
        $this->assertIsArray($response);
        $this->assertEquals("This is a test response", $response["content"]);
        $this->assertEquals("deepseek-chat", $response["model"]);
        $this->assertArrayHasKey("usage", $response);
        $this->assertEquals("stop", $response["finish_reason"]);
        
        // Verify the request was sent correctly
        Http::assertSent(function (Request $request) {
            return $request->url() === "https://api.test.com" &&
                   $request->hasHeader("Authorization", "Bearer test-api-key") &&
                   $request->hasHeader("Content-Type", "application/json") &&
                   $request["model"] === "deepseek-chat" &&
                   $request["temperature"] === 0.7 &&
                   $request["max_tokens"] === 1000 &&
                   $request["messages"][0]["role"] === "user" &&
                   $request["messages"][0]["content"] === "Test message";
        });
    }
    
    public function testSendMessageWithContext()
    {
        // Mock the HTTP response
        Http::fake([
            "*" => Http::response([
                "id" => "chatcmpl-123",
                "object" => "chat.completion",
                "created" => 1677858242,
                "model" => "deepseek-chat",
                "choices" => [
                    [
                        "message" => [
                            "role" => "assistant",
                            "content" => "This is a response with context"
                        ],
                        "finish_reason" => "stop",
                        "index" => 0
                    ]
                ],
                "usage" => [
                    "prompt_tokens" => 30,
                    "completion_tokens" => 20,
                    "total_tokens" => 50
                ]
            ], 200)
        ]);
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat"
        );
        
        $context = [
            "history" => [
                ["role" => "user", "content" => "Previous message"],
                ["role" => "assistant", "content" => "Previous response"]
            ]
        ];
        
        $response = $service->sendMessage("Test message with context", $context);
        
        $this->assertIsArray($response);
        $this->assertEquals("This is a response with context", $response["content"]);
        
        // Verify the request was sent with context
        Http::assertSent(function (Request $request) {
            return $request->url() === "https://api.test.com" &&
                   count($request["messages"]) === 3 &&
                   $request["messages"][0]["role"] === "user" &&
                   $request["messages"][0]["content"] === "Previous message" &&
                   $request["messages"][1]["role"] === "assistant" &&
                   $request["messages"][1]["content"] === "Previous response" &&
                   $request["messages"][2]["role"] === "user" &&
                   $request["messages"][2]["content"] === "Test message with context";
        });
    }
    
    public function testSendMessageWithApiError()
    {
        // Mock the HTTP response with an error
        Http::fake([
            "*" => Http::response([
                "error" => [
                    "message" => "Invalid API key",
                    "type" => "authentication_error",
                    "code" => "invalid_api_key"
                ]
            ], 401)
        ]);
        
        $service = new DeepSeekService(
            "invalid-api-key",
            "https://api.test.com",
            "deepseek-chat"
        );
        
        $this->expectException(AIServiceException::class);
        $this->expectExceptionMessage("DeepSeek API error: Invalid API key");
        
        $service->sendMessage("Test message");
    }
    
    public function testSendMessageWithRateLimitError()
    {
        // Mock the HTTP response with a rate limit error
        Http::fake([
            "*" => Http::response([
                "error" => [
                    "message" => "Rate limit exceeded",
                    "type" => "rate_limit_error",
                    "code" => "rate_limit_exceeded"
                ]
            ], 429)
        ]);
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat"
        );
        
        $this->expectException(RateLimitException::class);
        $this->expectExceptionMessage("DeepSeek API rate limit exceeded: Rate limit exceeded");
        
        $service->sendMessage("Test message");
    }
    
    public function testSendMessageWithInvalidResponseFormat()
    {
        // Mock the HTTP response with an invalid format
        Http::fake([
            "*" => Http::response([
                "id" => "chatcmpl-123",
                "object" => "chat.completion",
                "created" => 1677858242,
                "model" => "deepseek-chat",
                // Missing "choices" key
            ], 200)
        ]);
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat"
        );
        
        $this->expectException(AIServiceException::class);
        $this->expectExceptionMessage("Invalid response format from DeepSeek API");
        
        $service->sendMessage("Test message");
    }
    
    public function testSendMessageWithRetry()
    {
        // Mock HTTP to fail once then succeed
        Http::fake([
            "*" => Http::sequence()
                ->push(["error" => ["message" => "Server error"]], 500)
                ->push([
                    "id" => "chatcmpl-123",
                    "object" => "chat.completion",
                    "created" => 1677858242,
                    "model" => "deepseek-chat",
                    "choices" => [
                        [
                            "message" => [
                                "role" => "assistant",
                                "content" => "This is a retry response"
                            ],
                            "finish_reason" => "stop",
                            "index" => 0
                        ]
                    ],
                    "usage" => [
                        "prompt_tokens" => 10,
                        "completion_tokens" => 20,
                        "total_tokens" => 30
                    ]
                ], 200)
        ]);
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat",
            ["timeout" => 1] // Set a short timeout for testing
        );
        
        // Use reflection to set maxRetries to 1 for faster testing
        $reflection = new \ReflectionClass($service);
        $property = $reflection->getProperty("maxRetries");
        $property->setAccessible(true);
        $property->setValue($service, 1);
        
        $response = $service->sendMessage("Test message");
        
        $this->assertIsArray($response);
        $this->assertEquals("This is a retry response", $response["content"]);
        
        // Verify the request was sent twice
        Http::assertSentCount(2);
    }
    
    public function testSendMessageWithMaxRetriesExceeded()
    {
        // Mock HTTP to always fail with server error
        Http::fake([
            "*" => Http::response(["error" => ["message" => "Server error"]], 500)
        ]);
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat",
            ["timeout" => 1] // Set a short timeout for testing
        );
        
        // Use reflection to set maxRetries to 2 for faster testing
        $reflection = new \ReflectionClass($service);
        $property = $reflection->getProperty("maxRetries");
        $property->setAccessible(true);
        $property->setValue($service, 2);
        
        $this->expectException(ServiceUnavailableException::class);
        $this->expectExceptionMessage("DeepSeek API server error: Server error");
        
        $service->sendMessage("Test message");
        
        // Verify the request was sent the maximum number of times
        Http::assertSentCount(2);
    }

    public function testIsRateLimitedWhenLimitExceeded()
    {
        // Mock the config
        config(["ai.rate_limits.enabled" => true]);
        config(["ai.rate_limits.max_requests" => 2]);
        config(["ai.rate_limits.period_minutes" => 60]);
        
        // Set up the cache with rate limit data
        $cacheKey = "deepseek_rate_limit";
        $usageData = [
            "count" => 3, // Exceeds the limit of 2
            "reset_at" => now()->addMinutes(10) // Not expired yet
        ];
        Cache::put($cacheKey, $usageData, $usageData["reset_at"]);
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat"
        );
        
        // Use reflection to access the protected isRateLimited method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod("isRateLimited");
        $method->setAccessible(true);
        
        $this->assertTrue($method->invoke($service));
        
        // Clean up
        Cache::forget($cacheKey);
    }
    
    public function testIsRateLimitedWhenLimitNotExceeded()
    {
        // Mock the config
        config(["ai.rate_limits.enabled" => true]);
        config(["ai.rate_limits.max_requests" => 5]);
        config(["ai.rate_limits.period_minutes" => 60]);
        
        // Set up the cache with rate limit data
        $cacheKey = "deepseek_rate_limit";
        $usageData = [
            "count" => 3, // Below the limit of 5
            "reset_at" => now()->addMinutes(10) // Not expired yet
        ];
        Cache::put($cacheKey, $usageData, $usageData["reset_at"]);
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat"
        );
        
        // Use reflection to access the protected isRateLimited method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod("isRateLimited");
        $method->setAccessible(true);
        
        $this->assertFalse($method->invoke($service));
        
        // Clean up
        Cache::forget($cacheKey);
    }
    
    public function testIsRateLimitedWhenTimeExpired()
    {
        // Mock the config
        config(["ai.rate_limits.enabled" => true]);
        config(["ai.rate_limits.max_requests" => 2]);
        config(["ai.rate_limits.period_minutes" => 60]);
        
        // Set up the cache with rate limit data
        $cacheKey = "deepseek_rate_limit";
        $usageData = [
            "count" => 3, // Exceeds the limit of 2
            "reset_at" => now()->subMinutes(10) // Already expired
        ];
        Cache::put($cacheKey, $usageData, now()->addMinutes(10)); // Keep in cache for test
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat"
        );
        
        // Use reflection to access the protected isRateLimited method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod("isRateLimited");
        $method->setAccessible(true);
        
        $this->assertFalse($method->invoke($service));
        
        // Clean up
        Cache::forget($cacheKey);
    }
    
    public function testTrackRateLimitUsage()
    {
        // Mock the config
        config(["ai.rate_limits.enabled" => true]);
        config(["ai.rate_limits.period_minutes" => 60]);
        
        $cacheKey = "deepseek_rate_limit";
        Cache::forget($cacheKey); // Ensure clean state
        
        $service = new DeepSeekService(
            "test-api-key",
            "https://api.test.com",
            "deepseek-chat"
        );
        
        // Use reflection to access the protected trackRateLimitUsage method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod("trackRateLimitUsage");
        $method->setAccessible(true);
        
        // First call should initialize the counter
        $method->invoke($service);
        $usageData = Cache::get($cacheKey);
        $this->assertEquals(1, $usageData["count"]);
        
        // Second call should increment the counter
        $method->invoke($service);
        $usageData = Cache::get($cacheKey);
        $this->assertEquals(2, $usageData["count"]);
        
        // Clean up
        Cache::forget($cacheKey);
    }
}
