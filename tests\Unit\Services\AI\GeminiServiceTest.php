<?php

namespace Tests\Unit\Services\AI;

use App\Exceptions\AIServiceException;
use App\Exceptions\RateLimitException;
use App\Exceptions\ServiceUnavailableException;
use App\Services\AI\GeminiService;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GeminiServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any mocked responses
        Http::fake();
    }

    public function testConstructor()
    {
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro',
            ['temperature' => 0.5]
        );
        
        $this->assertEquals('gemini-pro', $service->getModelName());
        $this->assertTrue($service->isAvailable());
    }
    
    public function testIsAvailableReturnsFalseWhenApiKeyIsEmpty()
    {
        $service = new GeminiService(
            '',
            'https://api.test.com',
            'gemini-pro'
        );
        
        $this->assertFalse($service->isAvailable());
    }
    
    public function testSendMessageThrowsExceptionWhenApiKeyIsEmpty()
    {
        $service = new GeminiService(
            '',
            'https://api.test.com',
            'gemini-pro'
        );
        
        $this->expectException(ServiceUnavailableException::class);
        $this->expectExceptionMessage('Gemini API is not available: API key is missing');
        
        $service->sendMessage('Test message');
    }
    
    public function testSendMessageSuccess()
    {
        // Mock the HTTP response
        Http::fake([
            '*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                ['text' => 'This is a test response']
                            ],
                            'role' => 'model'
                        ],
                        'finishReason' => 'STOP'
                    ]
                ],
                'promptFeedback' => [
                    'safetyRatings' => []
                ]
            ], 200)
        ]);
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro',
            ['temperature' => 0.7, 'max_tokens' => 1000]
        );
        
        $response = $service->sendMessage('Test message');
        
        $this->assertIsArray($response);
        $this->assertEquals('This is a test response', $response['content']);
        $this->assertEquals('gemini-pro', $response['model']);
        $this->assertArrayHasKey('usage', $response);
        $this->assertEquals('STOP', $response['finish_reason']);
        
        // Verify the request was sent correctly
        Http::assertSent(function (Request $request) {
            return strpos($request->url(), 'https://api.test.com/gemini-pro:generateContent?key=test-api-key') !== false &&
                   $request->hasHeader('Content-Type', 'application/json') &&
                   $request['generationConfig']['temperature'] === 0.7 &&
                   $request['generationConfig']['maxOutputTokens'] === 1000 &&
                   $request['contents'][0]['role'] === 'user' &&
                   $request['contents'][0]['parts'][0]['text'] === 'Test message';
        });
    }
    
    public function testSendMessageWithContext()
    {
        // Mock the HTTP response
        Http::fake([
            '*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                ['text' => 'This is a response with context']
                            ],
                            'role' => 'model'
                        ],
                        'finishReason' => 'STOP'
                    ]
                ],
                'promptFeedback' => [
                    'safetyRatings' => []
                ]
            ], 200)
        ]);
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro'
        );
        
        $context = [
            'history' => [
                ['role' => 'user', 'content' => 'Previous message'],
                ['role' => 'assistant', 'content' => 'Previous response']
            ]
        ];
        
        $response = $service->sendMessage('Test message with context', $context);
        
        $this->assertIsArray($response);
        $this->assertEquals('This is a response with context', $response['content']);
        
        // Verify the request was sent with context
        Http::assertSent(function (Request $request) {
            return strpos($request->url(), 'https://api.test.com/gemini-pro:generateContent?key=test-api-key') !== false &&
                   count($request['contents']) === 3 &&
                   $request['contents'][0]['role'] === 'user' &&
                   $request['contents'][0]['parts'][0]['text'] === 'Previous message' &&
                   $request['contents'][1]['role'] === 'model' &&
                   $request['contents'][1]['parts'][0]['text'] === 'Previous response' &&
                   $request['contents'][2]['role'] === 'user' &&
                   $request['contents'][2]['parts'][0]['text'] === 'Test message with context';
        });
    }
    
    public function testSendMessageWithApiError()
    {
        // Mock the HTTP response with an error
        Http::fake([
            '*' => Http::response([
                'error' => [
                    'message' => 'Invalid API key',
                    'status' => 'INVALID_ARGUMENT',
                    'code' => 400
                ]
            ], 401)
        ]);
        
        $service = new GeminiService(
            'invalid-api-key',
            'https://api.test.com',
            'gemini-pro'
        );
        
        $this->expectException(AIServiceException::class);
        $this->expectExceptionMessage('Gemini API error: Invalid API key');
        
        $service->sendMessage('Test message');
    }
    
    public function testSendMessageWithRateLimitError()
    {
        // Mock the HTTP response with a rate limit error
        Http::fake([
            '*' => Http::response([
                'error' => [
                    'message' => 'Rate limit exceeded',
                    'status' => 'RESOURCE_EXHAUSTED',
                    'code' => 429
                ]
            ], 429)
        ]);
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro'
        );
        
        $this->expectException(RateLimitException::class);
        $this->expectExceptionMessage('Gemini API rate limit exceeded: Rate limit exceeded');
        
        $service->sendMessage('Test message');
    }
    
    public function testSendMessageWithInvalidResponseFormat()
    {
        // Mock the HTTP response with an invalid format
        Http::fake([
            '*' => Http::response([
                'promptFeedback' => [
                    'safetyRatings' => []
                ]
                // Missing 'candidates' key
            ], 200)
        ]);
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro'
        );
        
        $this->expectException(AIServiceException::class);
        $this->expectExceptionMessage('Invalid response format from Gemini API');
        
        $service->sendMessage('Test message');
    }
    
    public function testSendMessageWithRetry()
    {
        // Mock HTTP to fail once then succeed
        Http::fake([
            '*' => Http::sequence()
                ->push(['error' => ['message' => 'Server error']], 500)
                ->push([
                    'candidates' => [
                        [
                            'content' => [
                                'parts' => [
                                    ['text' => 'This is a retry response']
                                ],
                                'role' => 'model'
                            ],
                            'finishReason' => 'STOP'
                        ]
                    ],
                    'promptFeedback' => [
                        'safetyRatings' => []
                    ]
                ], 200)
        ]);
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro',
            ['timeout' => 1] // Set a short timeout for testing
        );
        
        // Use reflection to set maxRetries to 1 for faster testing
        $reflection = new \ReflectionClass($service);
        $property = $reflection->getProperty('maxRetries');
        $property->setAccessible(true);
        $property->setValue($service, 1);
        
        $response = $service->sendMessage('Test message');
        
        $this->assertIsArray($response);
        $this->assertEquals('This is a retry response', $response['content']);
        
        // Verify the request was sent twice
        Http::assertSentCount(2);
    }
    
    public function testSendMessageWithMaxRetriesExceeded()
    {
        // Mock HTTP to always fail with server error
        Http::fake([
            '*' => Http::response(['error' => ['message' => 'Server error']], 500)
        ]);
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro',
            ['timeout' => 1] // Set a short timeout for testing
        );
        
        // Use reflection to set maxRetries to 2 for faster testing
        $reflection = new \ReflectionClass($service);
        $property = $reflection->getProperty('maxRetries');
        $property->setAccessible(true);
        $property->setValue($service, 2);
        
        $this->expectException(ServiceUnavailableException::class);
        $this->expectExceptionMessage('Gemini API server error: Server error');
        
        $service->sendMessage('Test message');
        
        // Verify the request was sent the maximum number of times
        Http::assertSentCount(2);
    }
    
    public function testIsRateLimitedWhenLimitExceeded()
    {
        // Mock the config
        config(['ai.rate_limits.enabled' => true]);
        config(['ai.rate_limits.max_requests' => 2]);
        config(['ai.rate_limits.period_minutes' => 60]);
        
        // Set up the cache with rate limit data
        $cacheKey = 'gemini_rate_limit';
        $usageData = [
            'count' => 3, // Exceeds the limit of 2
            'reset_at' => now()->addMinutes(10) // Not expired yet
        ];
        Cache::put($cacheKey, $usageData, $usageData['reset_at']);
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro'
        );
        
        // Use reflection to access the protected isRateLimited method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('isRateLimited');
        $method->setAccessible(true);
        
        $this->assertTrue($method->invoke($service));
        
        // Clean up
        Cache::forget($cacheKey);
    }
    
    public function testIsRateLimitedWhenLimitNotExceeded()
    {
        // Mock the config
        config(['ai.rate_limits.enabled' => true]);
        config(['ai.rate_limits.max_requests' => 5]);
        config(['ai.rate_limits.period_minutes' => 60]);
        
        // Set up the cache with rate limit data
        $cacheKey = 'gemini_rate_limit';
        $usageData = [
            'count' => 3, // Below the limit of 5
            'reset_at' => now()->addMinutes(10) // Not expired yet
        ];
        Cache::put($cacheKey, $usageData, $usageData['reset_at']);
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro'
        );
        
        // Use reflection to access the protected isRateLimited method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('isRateLimited');
        $method->setAccessible(true);
        
        $this->assertFalse($method->invoke($service));
        
        // Clean up
        Cache::forget($cacheKey);
    }
    
    public function testIsRateLimitedWhenTimeExpired()
    {
        // Mock the config
        config(['ai.rate_limits.enabled' => true]);
        config(['ai.rate_limits.max_requests' => 2]);
        config(['ai.rate_limits.period_minutes' => 60]);
        
        // Set up the cache with rate limit data
        $cacheKey = 'gemini_rate_limit';
        $usageData = [
            'count' => 3, // Exceeds the limit of 2
            'reset_at' => now()->subMinutes(10) // Already expired
        ];
        Cache::put($cacheKey, $usageData, now()->addMinutes(10)); // Keep in cache for test
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro'
        );
        
        // Use reflection to access the protected isRateLimited method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('isRateLimited');
        $method->setAccessible(true);
        
        $this->assertFalse($method->invoke($service));
        
        // Clean up
        Cache::forget($cacheKey);
    }
    
    public function testTrackRateLimitUsage()
    {
        // Mock the config
        config(['ai.rate_limits.enabled' => true]);
        config(['ai.rate_limits.period_minutes' => 60]);
        
        $cacheKey = 'gemini_rate_limit';
        Cache::forget($cacheKey); // Ensure clean state
        
        $service = new GeminiService(
            'test-api-key',
            'https://api.test.com',
            'gemini-pro'
        );
        
        // Use reflection to access the protected trackRateLimitUsage method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('trackRateLimitUsage');
        $method->setAccessible(true);
        
        // First call should initialize the counter
        $method->invoke($service);
        $usageData = Cache::get($cacheKey);
        $this->assertEquals(1, $usageData['count']);
        
        // Second call should increment the counter
        $method->invoke($service);
        $usageData = Cache::get($cacheKey);
        $this->assertEquals(2, $usageData['count']);
        
        // Clean up
        Cache::forget($cacheKey);
    }
}