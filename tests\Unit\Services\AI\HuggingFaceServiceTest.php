<?php

namespace Tests\Unit\Services\AI;

use App\Exceptions\AIServiceException;
use App\Exceptions\RateLimitException;
use App\Exceptions\ServiceUnavailableException;
use App\Services\AI\HuggingFaceService;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\TestCase;

class HuggingFaceServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any mocked responses
        Http::fake()->assertNothingSent();
    }

    public function testConstructor()
    {
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2",
            ["temperature" => 0.5]
        );
        
        $this->assertEquals("mistralai/Mistral-7B-Instruct-v0.2", $service->getModelName());
        $this->assertTrue($service->isAvailable());
    }
    
    public function testIsAvailableReturnsFalseWhenApiKeyIsEmpty()
    {
        $service = new HuggingFaceService(
            "",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        $this->assertFalse($service->isAvailable());
    }
    
    public function testSendMessageThrowsExceptionWhenApiKeyIsEmpty()
    {
        $service = new HuggingFaceService(
            "",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        $this->expectException(ServiceUnavailableException::class);
        $this->expectExceptionMessage("HuggingFace API is not available: API key is missing");
        
        $service->sendMessage("Test message");
    }
    
    public function testSendMessageSuccess()
    {
        // Mock the HTTP response for text generation model
        Http::fake([
            "*" => Http::response([
                [
                    "generated_text" => "This is a test response from HuggingFace"
                ]
            ], 200)
        ]);
        
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2",
            ["temperature" => 0.7, "max_tokens" => 1000]
        );
        
        $response = $service->sendMessage("Test message");
        
        $this->assertIsArray($response);
        $this->assertEquals("This is a test response from HuggingFace", $response["content"]);
        $this->assertEquals("mistralai/Mistral-7B-Instruct-v0.2", $response["model"]);
        $this->assertArrayHasKey("usage", $response);
        $this->assertEquals("stop", $response["finish_reason"]);
        
        // Verify the request was sent correctly
        Http::assertSent(function (Request $request) {
            return $request->url() === "https://api.test.com/mistralai/Mistral-7B-Instruct-v0.2" &&
                   $request->hasHeader("Authorization", "Bearer test-api-key") &&
                   $request->hasHeader("Content-Type", "application/json") &&
                   $request["inputs"] === "Test message" &&
                   $request["parameters"]["temperature"] === 0.7 &&
                   $request["parameters"]["max_new_tokens"] === 1000 &&
                   $request["parameters"]["return_full_text"] === false;
        });
    }
    
    public function testSendMessageWithContext()
    {
        // Mock the HTTP response
        Http::fake([
            "*" => Http::response([
                [
                    "generated_text" => "This is a response with context from HuggingFace"
                ]
            ], 200)
        ]);
        
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        $context = [
            "history" => [
                ["role" => "user", "content" => "Previous message"],
                ["role" => "assistant", "content" => "Previous response"]
            ]
        ];
        
        $response = $service->sendMessage("Test message with context", $context);
        
        $this->assertIsArray($response);
        $this->assertEquals("This is a response with context from HuggingFace", $response["content"]);
        
        // Verify the request was sent with formatted context
        Http::assertSent(function (Request $request) {
            $expectedInput = "User: Previous message\nAssistant: Previous response\nUser: Test message with context\nAssistant:";
            return $request->url() === "https://api.test.com/mistralai/Mistral-7B-Instruct-v0.2" &&
                   $request["inputs"] === $expectedInput;
        });
    }
    
    public function testSendMessageWithApiError()
    {
        // Mock the HTTP response with an error
        Http::fake([
            "*" => Http::response([
                "error" => "Invalid API key"
            ], 401)
        ]);
        
        $service = new HuggingFaceService(
            "invalid-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        $this->expectException(AIServiceException::class);
        $this->expectExceptionMessage("HuggingFace API error: Invalid API key");
        
        $service->sendMessage("Test message");
    }
    
    public function testSendMessageWithRateLimitError()
    {
        // Mock the HTTP response with a rate limit error
        Http::fake([
            "*" => Http::response([
                "error" => "Rate limit exceeded"
            ], 429)
        ]);
        
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        $this->expectException(RateLimitException::class);
        $this->expectExceptionMessage("HuggingFace API rate limit exceeded: Rate limit exceeded");
        
        $service->sendMessage("Test message");
    }
    
    public function testSendMessageWithInvalidResponseFormat()
    {
        // Mock the HTTP response with an invalid format
        Http::fake([
            "*" => Http::response([
                "unexpected_format" => "This is not the expected format"
            ], 200)
        ]);
        
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        $this->expectException(AIServiceException::class);
        $this->expectExceptionMessage("Invalid response format from HuggingFace API");
        
        $service->sendMessage("Test message");
    }
    
    public function testSendMessageWithRetry()
    {
        // Mock HTTP to fail once then succeed
        Http::fake([
            "*" => Http::sequence()
                ->push(["error" => "Server error"], 500)
                ->push([
                    [
                        "generated_text" => "This is a retry response from HuggingFace"
                    ]
                ], 200)
        ]);
        
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2",
            ["timeout" => 1] // Set a short timeout for testing
        );
        
        // Use reflection to set maxRetries to 1 for faster testing
        $reflection = new \ReflectionClass($service);
        $property = $reflection->getProperty("maxRetries");
        $property->setAccessible(true);
        $property->setValue($service, 1);
        
        $response = $service->sendMessage("Test message");
        
        $this->assertIsArray($response);
        $this->assertEquals("This is a retry response from HuggingFace", $response["content"]);
        
        // Verify the request was sent twice
        Http::assertSentCount(2);
    }
    
    public function testSendMessageWithMaxRetriesExceeded()
    {
        // Mock HTTP to always fail with server error
        Http::fake([
            "*" => Http::response(["error" => "Server error"], 500)
        ]);
        
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2",
            ["timeout" => 1] // Set a short timeout for testing
        );
        
        // Use reflection to set maxRetries to 2 for faster testing
        $reflection = new \ReflectionClass($service);
        $property = $reflection->getProperty("maxRetries");
        $property->setAccessible(true);
        $property->setValue($service, 2);
        
        $this->expectException(ServiceUnavailableException::class);
        $this->expectExceptionMessage("HuggingFace API server error: Server error");
        
        $service->sendMessage("Test message");
        
        // Verify the request was sent the maximum number of times
        Http::assertSentCount(2);
    }
    
    public function testParseResponseWithDifferentFormats()
    {
        // Test with different response formats that HuggingFace might return
        
        // 1. Test with generated_text in array format
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        // Use reflection to access the protected parseResponse method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod("parseResponse");
        $method->setAccessible(true);
        
        // Test format 1: Array with generated_text
        $response1 = $method->invoke($service, [
            [
                "generated_text" => "Response format 1"
            ]
        ]);
        $this->assertEquals("Response format 1", $response1["content"]);
        
        // Test format 2: Object with generated_text
        $response2 = $method->invoke($service, [
            "generated_text" => "Response format 2"
        ]);
        $this->assertEquals("Response format 2", $response2["content"]);
        
        // Test format 3: Array of strings
        $response3 = $method->invoke($service, [
            "Response format 3"
        ]);
        $this->assertEquals("Response format 3", $response3["content"]);
    }
    
    public function testIsRateLimitedWhenLimitExceeded()
    {
        // Mock the config
        config(["ai.rate_limits.enabled" => true]);
        config(["ai.rate_limits.max_requests" => 2]);
        config(["ai.rate_limits.period_minutes" => 60]);
        
        // Set up the cache with rate limit data
        $cacheKey = "huggingface_rate_limit";
        $usageData = [
            "count" => 3, // Exceeds the limit of 2
            "reset_at" => now()->addMinutes(10) // Not expired yet
        ];
        Cache::put($cacheKey, $usageData, $usageData["reset_at"]);
        
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        // Use reflection to access the protected isRateLimited method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod("isRateLimited");
        $method->setAccessible(true);
        
        $this->assertTrue($method->invoke($service));
        
        // Clean up
        Cache::forget($cacheKey);
    }
    
    public function testIsRateLimitedWhenLimitNotExceeded()
    {
        // Mock the config
        config(["ai.rate_limits.enabled" => true]);
        config(["ai.rate_limits.max_requests" => 5]);
        config(["ai.rate_limits.period_minutes" => 60]);
        
        // Set up the cache with rate limit data
        $cacheKey = "huggingface_rate_limit";
        $usageData = [
            "count" => 3, // Below the limit of 5
            "reset_at" => now()->addMinutes(10) // Not expired yet
        ];
        Cache::put($cacheKey, $usageData, $usageData["reset_at"]);
        
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        // Use reflection to access the protected isRateLimited method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod("isRateLimited");
        $method->setAccessible(true);
        
        $this->assertFalse($method->invoke($service));
        
        // Clean up
        Cache::forget($cacheKey);
    }
    
    public function testTrackRateLimitUsage()
    {
        // Mock the config
        config(["ai.rate_limits.enabled" => true]);
        config(["ai.rate_limits.period_minutes" => 60]);
        
        $cacheKey = "huggingface_rate_limit";
        Cache::forget($cacheKey); // Ensure clean state
        
        $service = new HuggingFaceService(
            "test-api-key",
            "https://api.test.com",
            "mistralai/Mistral-7B-Instruct-v0.2"
        );
        
        // Use reflection to access the protected trackRateLimitUsage method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod("trackRateLimitUsage");
        $method->setAccessible(true);
        
        // First call should initialize the counter
        $method->invoke($service);
        $usageData = Cache::get($cacheKey);
        $this->assertEquals(1, $usageData["count"]);
        
        // Second call should increment the counter
        $method->invoke($service);
        $usageData = Cache::get($cacheKey);
        $this->assertEquals(2, $usageData["count"]);
        
        // Clean up
        Cache::forget($cacheKey);
    }
}
