<?php

namespace Tests\Unit\Services\AI;

use App\Exceptions\AIServiceException;
use App\Services\AI\Contracts\AIServiceInterface;

class MockAIService implements AIServiceInterface
{
    protected string $modelName;
    protected bool $available;
    protected ?string $errorMessage;

    public function __construct(string $modelName = 'mock-model', bool $available = true, ?string $errorMessage = null)
    {
        $this->modelName = $modelName;
        $this->available = $available;
        $this->errorMessage = $errorMessage;
    }

    public function sendMessage(string $message, array $context = []): array
    {
        if (!$this->available) {
            throw new AIServiceException(
                $this->errorMessage ?? 'Mock service unavailable',
                'mock-service',
                ['mock_error' => true]
            );
        }

        return [
            'content' => "Mock response to: $message",
            'model' => $this->modelName,
            'usage' => [
                'prompt_tokens' => strlen($message) / 4,
                'completion_tokens' => 20,
                'total_tokens' => (strlen($message) / 4) + 20
            ]
        ];
    }

    public function isAvailable(): bool
    {
        return $this->available;
    }

    public function getModelName(): string
    {
        return $this->modelName;
    }

    public function setAvailable(bool $available): self
    {
        $this->available = $available;
        return $this;
    }

    public function setErrorMessage(?string $errorMessage): self
    {
        $this->errorMessage = $errorMessage;
        return $this;
    }
}