<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

class CacheServiceTest extends TestCase
{
    protected CacheService $cacheService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->cacheService = new CacheService();
        
        // Clear cache before each test
        Cache::flush();
    }

    public function test_can_cache_and_retrieve_conversation()
    {
        $conversationId = 123;
        $data = [
            'id' => $conversationId,
            'title' => 'Test Conversation',
            'messages' => [
                ['role' => 'user', 'content' => 'Hello'],
                ['role' => 'assistant', 'content' => 'Hi there!'],
            ],
        ];

        // Cache the conversation
        $result = $this->cacheService->cacheConversation($conversationId, $data);
        $this->assertTrue($result);

        // Retrieve the conversation
        $cached = $this->cacheService->getCachedConversation($conversationId);
        $this->assertEquals($data, $cached);
    }

    public function test_returns_null_for_non_existent_conversation()
    {
        $cached = $this->cacheService->getCachedConversation(999);
        $this->assertNull($cached);
    }

    public function test_can_cache_and_retrieve_conversations_list()
    {
        $cacheKey = 'page_1_per_20';
        $conversations = collect([
            ['id' => 1, 'title' => 'Conversation 1'],
            ['id' => 2, 'title' => 'Conversation 2'],
        ]);

        // Cache the conversations list
        $result = $this->cacheService->cacheConversationsList($cacheKey, $conversations);
        $this->assertTrue($result);

        // Retrieve the conversations list
        $cached = $this->cacheService->getCachedConversationsList($cacheKey);
        $this->assertInstanceOf(Collection::class, $cached);
        $this->assertEquals($conversations->toArray(), $cached->toArray());
    }

    public function test_can_cache_and_retrieve_ai_response()
    {
        $queryHash = 'test_hash_123';
        $response = [
            'service_used' => 'deepseek',
            'fallback_used' => false,
            'result' => [
                'content' => 'This is a test response',
                'model' => 'deepseek-chat',
            ],
        ];

        // Cache the AI response
        $result = $this->cacheService->cacheAIResponse($queryHash, $response);
        $this->assertTrue($result);

        // Retrieve the AI response
        $cached = $this->cacheService->getCachedAIResponse($queryHash);
        $this->assertEquals($response, $cached);
    }

    public function test_can_cache_and_retrieve_knowledge_stats()
    {
        $stats = [
            'total_entries' => 100,
            'unique_sources' => 5,
            'unique_tags' => 20,
            'average_confidence' => 0.85,
        ];

        // Cache the knowledge stats
        $result = $this->cacheService->cacheKnowledgeStats($stats);
        $this->assertTrue($result);

        // Retrieve the knowledge stats
        $cached = $this->cacheService->getCachedKnowledgeStats();
        $this->assertEquals($stats, $cached);
    }

    public function test_can_invalidate_conversation_cache()
    {
        $conversationId = 123;
        $data = ['id' => $conversationId, 'title' => 'Test'];

        // Cache the conversation
        $this->cacheService->cacheConversation($conversationId, $data);
        $this->assertNotNull($this->cacheService->getCachedConversation($conversationId));

        // Invalidate the cache
        $result = $this->cacheService->invalidateConversation($conversationId);
        $this->assertTrue($result);

        // Should return null after invalidation
        $this->assertNull($this->cacheService->getCachedConversation($conversationId));
    }

    public function test_can_invalidate_conversations_list_cache()
    {
        $cacheKey1 = 'page_1_per_20';
        $cacheKey2 = 'page_2_per_20';
        $conversations = collect([['id' => 1, 'title' => 'Test']]);

        // Cache multiple conversations lists
        $this->cacheService->cacheConversationsList($cacheKey1, $conversations);
        $this->cacheService->cacheConversationsList($cacheKey2, $conversations);

        $this->assertNotNull($this->cacheService->getCachedConversationsList($cacheKey1));
        $this->assertNotNull($this->cacheService->getCachedConversationsList($cacheKey2));

        // Invalidate all conversations lists
        $result = $this->cacheService->invalidateConversationsList();
        $this->assertTrue($result);

        // Both should be null after invalidation
        $this->assertNull($this->cacheService->getCachedConversationsList($cacheKey1));
        $this->assertNull($this->cacheService->getCachedConversationsList($cacheKey2));
    }

    public function test_can_invalidate_knowledge_cache()
    {
        $stats = ['total_entries' => 100];

        // Cache knowledge stats
        $this->cacheService->cacheKnowledgeStats($stats);
        $this->assertNotNull($this->cacheService->getCachedKnowledgeStats());

        // Invalidate knowledge cache
        $result = $this->cacheService->invalidateKnowledgeCache();
        $this->assertTrue($result);

        // Should be null after invalidation
        $this->assertNull($this->cacheService->getCachedKnowledgeStats());
    }

    public function test_can_clear_all_cache()
    {
        // Cache various types of data
        $this->cacheService->cacheConversation(1, ['test' => 'data']);
        $this->cacheService->cacheConversationsList('test', collect([['id' => 1]]));
        $this->cacheService->cacheAIResponse('hash', ['response' => 'data']);
        $this->cacheService->cacheKnowledgeStats(['stats' => 'data']);

        // Verify data is cached
        $this->assertNotNull($this->cacheService->getCachedConversation(1));
        $this->assertNotNull($this->cacheService->getCachedConversationsList('test'));
        $this->assertNotNull($this->cacheService->getCachedAIResponse('hash'));
        $this->assertNotNull($this->cacheService->getCachedKnowledgeStats());

        // Clear all cache
        $result = $this->cacheService->clearAll();
        $this->assertTrue($result);

        // All should be null after clearing
        $this->assertNull($this->cacheService->getCachedConversation(1));
        $this->assertNull($this->cacheService->getCachedConversationsList('test'));
        $this->assertNull($this->cacheService->getCachedAIResponse('hash'));
        $this->assertNull($this->cacheService->getCachedKnowledgeStats());
    }

    public function test_cache_respects_ttl()
    {
        $conversationId = 123;
        $data = ['id' => $conversationId, 'title' => 'Test'];
        $shortTtl = 1; // 1 second

        // Cache with short TTL
        $this->cacheService->cacheConversation($conversationId, $data, $shortTtl);
        $this->assertNotNull($this->cacheService->getCachedConversation($conversationId));

        // Wait for cache to expire
        sleep(2);

        // Should be null after expiration
        $this->assertNull($this->cacheService->getCachedConversation($conversationId));
    }

    public function test_get_cache_stats()
    {
        $stats = $this->cacheService->getStats();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('store_type', $stats);
        $this->assertArrayHasKey('prefix', $stats);
        $this->assertArrayHasKey('default_ttl', $stats);

        $this->assertEquals('widdx_ai', $stats['prefix']);
        $this->assertEquals(3600, $stats['default_ttl']);
    }

    public function test_handles_cache_failures_gracefully()
    {
        // Mock Cache facade to throw exceptions
        Cache::shouldReceive('put')->andThrow(new \Exception('Cache error'));
        Cache::shouldReceive('get')->andThrow(new \Exception('Cache error'));
        Cache::shouldReceive('forget')->andThrow(new \Exception('Cache error'));

        // Operations should not throw exceptions
        $result = $this->cacheService->cacheConversation(1, ['test' => 'data']);
        $this->assertFalse($result);

        $cached = $this->cacheService->getCachedConversation(1);
        $this->assertNull($cached);

        $result = $this->cacheService->invalidateConversation(1);
        $this->assertFalse($result);
    }

    public function test_cache_key_generation_is_consistent()
    {
        $conversationId = 123;
        $data = ['test' => 'data'];

        // Cache the same conversation twice
        $this->cacheService->cacheConversation($conversationId, $data);
        $this->cacheService->cacheConversation($conversationId, ['updated' => 'data']);

        // Should get the updated data (same key was used)
        $cached = $this->cacheService->getCachedConversation($conversationId);
        $this->assertEquals(['updated' => 'data'], $cached);
    }

    public function test_different_cache_types_use_different_keys()
    {
        $id = 123;
        $conversationData = ['type' => 'conversation'];
        $responseData = ['type' => 'response'];

        // Cache different types with same ID
        $this->cacheService->cacheConversation($id, $conversationData);
        $this->cacheService->cacheAIResponse((string)$id, $responseData);

        // Should retrieve correct data for each type
        $cachedConversation = $this->cacheService->getCachedConversation($id);
        $cachedResponse = $this->cacheService->getCachedAIResponse((string)$id);

        $this->assertEquals($conversationData, $cachedConversation);
        $this->assertEquals($responseData, $cachedResponse);
    }

    public function test_cache_handles_large_data()
    {
        $largeData = [
            'messages' => array_fill(0, 1000, [
                'role' => 'user',
                'content' => str_repeat('This is a long message. ', 100),
                'metadata' => array_fill(0, 50, 'metadata_value'),
            ]),
        ];

        // Should handle large data without issues
        $result = $this->cacheService->cacheConversation(1, $largeData);
        $this->assertTrue($result);

        $cached = $this->cacheService->getCachedConversation(1);
        $this->assertEquals($largeData, $cached);
    }

    public function test_cache_handles_special_characters()
    {
        $dataWithSpecialChars = [
            'title' => 'Test with émojis 🚀 and spëcial chars',
            'content' => 'Content with "quotes", \'apostrophes\', and <tags>',
            'unicode' => '测试中文字符',
        ];

        $result = $this->cacheService->cacheConversation(1, $dataWithSpecialChars);
        $this->assertTrue($result);

        $cached = $this->cacheService->getCachedConversation(1);
        $this->assertEquals($dataWithSpecialChars, $cached);
    }
}
