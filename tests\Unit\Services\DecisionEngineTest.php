<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\DecisionEngine;
use App\Services\AI\Contracts\AIServiceInterface;
use App\Exceptions\AIServiceException;
use App\Exceptions\ServiceUnavailableException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Mockery;

class DecisionEngineTest extends TestCase
{
    protected DecisionEngine $decisionEngine;
    protected $mockDeepSeekService;
    protected $mockGeminiService;
    protected $mockHuggingFaceService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock AI services
        $this->mockDeepSeekService = Mockery::mock(AIServiceInterface::class);
        $this->mockGeminiService = Mockery::mock(AIServiceInterface::class);
        $this->mockHuggingFaceService = Mockery::mock(AIServiceInterface::class);

        // Set up configuration
        Config::set('ai.routing', [
            'code' => [
                'service' => 'deepseek',
                'keywords' => ['code', 'function', 'class', 'programming', 'algorithm', 'debug'],
                'patterns' => [
                    '/```[a-z]*\n[\s\S]*?\n```/',
                    '/function\s+\w+\s*\(/',
                    '/class\s+\w+/',
                ],
            ],
            'research' => [
                'service' => 'gemini',
                'keywords' => ['research', 'information', 'data', 'analysis', 'explain', 'what is'],
                'patterns' => [
                    '/what\s+is\s+/',
                    '/how\s+does\s+/',
                    '/can\s+you\s+explain/',
                ],
            ],
            'creative' => [
                'service' => 'huggingface',
                'keywords' => ['write', 'create', 'story', 'poem', 'creative', 'sentiment'],
                'patterns' => [
                    '/write\s+a\s+(story|poem|song|novel|script)/',
                    '/create\s+a\s+(story|poem|song|novel|script)/',
                    '/sentiment\s+of\s+/',
                ],
            ],
        ]);

        Config::set('ai.fallback', [
            'enabled' => true,
            'order' => ['deepseek', 'gemini', 'huggingface'],
        ]);

        Config::set('ai.default_service', 'deepseek');

        // Create DecisionEngine with mock services
        $services = [
            'deepseek' => $this->mockDeepSeekService,
            'gemini' => $this->mockGeminiService,
            'huggingface' => $this->mockHuggingFaceService,
        ];

        $this->decisionEngine = new DecisionEngine($services);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_analyzeQuery_routes_code_queries_to_deepseek()
    {
        $codeQueries = [
            'Write a function to sort an array',
            'How do I debug this code?',
            'Create a class for user management',
            'Show me the algorithm implementation',
        ];

        foreach ($codeQueries as $query) {
            $result = $this->decisionEngine->analyzeQuery($query);
            $this->assertEquals('deepseek', $result, "Query '{$query}' should route to deepseek");
        }
    } 
   public function test_analyzeQuery_routes_research_queries_to_gemini()
    {
        $researchQueries = [
            'What is machine learning?',
            'Can you explain quantum computing?',
            'How does photosynthesis work?',
            'I need information about climate change',
        ];

        foreach ($researchQueries as $query) {
            $result = $this->decisionEngine->analyzeQuery($query);
            $this->assertEquals('gemini', $result, "Query '{$query}' should route to gemini");
        }
    }

    public function test_analyzeQuery_routes_creative_queries_to_huggingface()
    {
        $creativeQueries = [
            'Write a story about dragons',
            'Create a poem about love',
            'Analyze the sentiment of this text',
            'Write a creative essay',
        ];

        foreach ($creativeQueries as $query) {
            $result = $this->decisionEngine->analyzeQuery($query);
            $this->assertEquals('huggingface', $result, "Query '{$query}' should route to huggingface");
        }
    }

    public function test_analyzeQuery_uses_patterns_for_routing()
    {
        $patternQueries = [
            '```python\nprint("hello")\n```' => 'deepseek',
            'function calculateSum() {' => 'deepseek',
            'class UserModel extends Model' => 'deepseek',
            'what is artificial intelligence?' => 'gemini',
            'how does the internet work?' => 'gemini',
            'write a story about adventure' => 'huggingface',
            'create a poem about nature' => 'huggingface',
        ];

        foreach ($patternQueries as $query => $expectedService) {
            $result = $this->decisionEngine->analyzeQuery($query);
            $this->assertEquals($expectedService, $result, "Query '{$query}' should route to {$expectedService}");
        }
    }

    public function test_analyzeQuery_returns_default_service_for_unknown_queries()
    {
        $unknownQueries = [
            'Hello there',
            'Random text without specific keywords',
            'Just a simple greeting',
        ];

        foreach ($unknownQueries as $query) {
            $result = $this->decisionEngine->analyzeQuery($query);
            $this->assertEquals('deepseek', $result, "Unknown query should use default service");
        }
    }

    public function test_routeQuery_successful_primary_service()
    {
        $query = 'Write a function to calculate fibonacci';
        $expectedResponse = ['content' => 'Here is the fibonacci function...'];

        $this->mockDeepSeekService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockDeepSeekService
            ->shouldReceive('sendMessage')
            ->once()
            ->with($query, [])
            ->andReturn($expectedResponse);

        $result = $this->decisionEngine->routeQuery($query);

        $this->assertEquals('deepseek', $result['service_used']);
        $this->assertFalse($result['fallback_used']);
        $this->assertEquals($expectedResponse, $result['result']);
    }

    public function test_routeQuery_fallback_when_primary_service_fails()
    {
        $query = 'Write a function to calculate fibonacci';
        $expectedResponse = ['content' => 'Here is the fibonacci function...'];

        // Primary service (deepseek) fails
        $this->mockDeepSeekService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockDeepSeekService
            ->shouldReceive('sendMessage')
            ->once()
            ->andThrow(new ServiceUnavailableException('DeepSeek is down'));

        // Fallback to gemini succeeds
        $this->mockGeminiService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockGeminiService
            ->shouldReceive('sendMessage')
            ->once()
            ->with($query, [])
            ->andReturn($expectedResponse);

        $result = $this->decisionEngine->routeQuery($query);

        $this->assertEquals('gemini', $result['service_used']);
        $this->assertTrue($result['fallback_used']);
        $this->assertEquals($expectedResponse, $result['result']);
    }

    public function test_routeQuery_throws_exception_when_all_services_fail()
    {
        $query = 'Write a function to calculate fibonacci';

        // All services fail
        $this->mockDeepSeekService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockDeepSeekService
            ->shouldReceive('sendMessage')
            ->once()
            ->andThrow(new ServiceUnavailableException('DeepSeek is down'));

        $this->mockGeminiService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockGeminiService
            ->shouldReceive('sendMessage')
            ->once()
            ->andThrow(new ServiceUnavailableException('Gemini is down'));

        $this->mockHuggingFaceService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockHuggingFaceService
            ->shouldReceive('sendMessage')
            ->once()
            ->andThrow(new ServiceUnavailableException('HuggingFace is down'));

        $this->expectException(ServiceUnavailableException::class);
        $this->expectExceptionMessage('All AI services including fallbacks are currently unavailable');

        $this->decisionEngine->routeQuery($query);
    }

    public function test_routeQuery_with_fallback_disabled()
    {
        Config::set('ai.fallback.enabled', false);
        
        // Create a new DecisionEngine instance with updated config
        $services = [
            'deepseek' => $this->mockDeepSeekService,
            'gemini' => $this->mockGeminiService,
            'huggingface' => $this->mockHuggingFaceService,
        ];
        $decisionEngine = new DecisionEngine($services);
        
        $query = 'Write a function to calculate fibonacci';

        $this->mockDeepSeekService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockDeepSeekService
            ->shouldReceive('sendMessage')
            ->once()
            ->andThrow(new ServiceUnavailableException('DeepSeek is down'));

        $this->expectException(ServiceUnavailableException::class);
        $this->expectExceptionMessage('All AI services are currently unavailable');

        $decisionEngine->routeQuery($query);
    }

    public function test_isServiceAvailable_returns_correct_status()
    {
        $this->mockDeepSeekService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockGeminiService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(false);

        $this->assertTrue($this->decisionEngine->isServiceAvailable('deepseek'));
        $this->assertFalse($this->decisionEngine->isServiceAvailable('gemini'));
        $this->assertFalse($this->decisionEngine->isServiceAvailable('nonexistent'));
    }

    public function test_getAvailableServices_returns_only_available_services()
    {
        $this->mockDeepSeekService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockGeminiService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(false);

        $this->mockHuggingFaceService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $available = $this->decisionEngine->getAvailableServices();

        $this->assertContains('deepseek', $available);
        $this->assertNotContains('gemini', $available);
        $this->assertContains('huggingface', $available);
    }

    public function test_stats_tracking()
    {
        Cache::shouldReceive('get')
            ->with('decision_engine_stats', [])
            ->andReturn([]);

        Cache::shouldReceive('put')
            ->with('decision_engine_stats', Mockery::type('array'), Mockery::type('object'))
            ->once();

        $query = 'Write a function';
        $expectedResponse = ['content' => 'Function code...'];

        $this->mockDeepSeekService
            ->shouldReceive('isAvailable')
            ->once()
            ->andReturn(true);

        $this->mockDeepSeekService
            ->shouldReceive('sendMessage')
            ->once()
            ->andReturn($expectedResponse);

        $this->decisionEngine->routeQuery($query);
    }

    public function test_clearStats_removes_cached_stats()
    {
        Cache::shouldReceive('forget')
            ->with('decision_engine_stats')
            ->once();

        $this->decisionEngine->clearStats();
    }

    public function test_setServices_updates_available_services()
    {
        $newServices = [
            'test_service' => Mockery::mock(AIServiceInterface::class)
        ];

        $this->decisionEngine->setServices($newServices);

        // Test that the new service is recognized
        $this->assertFalse($this->decisionEngine->isServiceAvailable('deepseek'));
    }
}