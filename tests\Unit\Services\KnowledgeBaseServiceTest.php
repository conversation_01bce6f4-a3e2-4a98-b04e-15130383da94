<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\KnowledgeBaseService;
use App\Models\KnowledgeEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;

class KnowledgeBaseServiceTest extends TestCase
{
    use RefreshDatabase;

    protected KnowledgeBaseService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new KnowledgeBaseService();
        
        // Set up test configuration
        Config::set('ai.knowledge_base.enabled', true);
        Config::set('ai.knowledge_base.cache_enabled', true);
        Config::set('ai.knowledge_base.use_mock_embeddings', true);
        Config::set('ai.knowledge_base.similarity_threshold', 0.7);
        Config::set('ai.knowledge_base.embedding_dimension', 384);
    }

    public function test_can_create_knowledge_entry()
    {
        $content = 'This is a test knowledge entry about <PERSON><PERSON>.';
        $source = 'test_source';
        $tags = ['laravel', 'php', 'testing'];
        $confidenceScore = 0.9;

        $entry = $this->service->createEntry($content, $source, $tags, $confidenceScore);

        $this->assertInstanceOf(KnowledgeEntry::class, $entry);
        $this->assertEquals($content, $entry->content);
        $this->assertEquals($source, $entry->source);
        $this->assertEquals($tags, $entry->tags);
        $this->assertEquals($confidenceScore, $entry->confidence_score);
        $this->assertIsArray($entry->embedding_vector);
        $this->assertCount(384, $entry->embedding_vector);
    }

    public function test_can_search_knowledge_entries()
    {
        // Create test entries
        $entry1 = $this->service->createEntry('Laravel is a PHP framework', 'docs', ['laravel', 'php']);
        $entry2 = $this->service->createEntry('Python is a programming language', 'docs', ['python']);
        $entry3 = $this->service->createEntry('Laravel has great documentation', 'docs', ['laravel', 'docs']);

        // Search for Laravel-related content
        $results = $this->service->search('Laravel framework', 0.1, 10);

        $this->assertGreaterThan(0, $results->count());
        
        // The results should be ordered by similarity
        $firstResult = $results->first();
        $this->assertObjectHasProperty('similarity_score', $firstResult);
    }

    public function test_can_update_knowledge_entry()
    {
        $entry = $this->service->createEntry('Original content', 'test', ['tag1']);
        
        $updatedEntry = $this->service->updateEntry($entry->id, [
            'content' => 'Updated content',
            'tags' => ['tag1', 'tag2'],
            'confidence_score' => 0.95,
        ]);

        $this->assertNotNull($updatedEntry);
        $this->assertEquals('Updated content', $updatedEntry->content);
        $this->assertEquals(['tag1', 'tag2'], $updatedEntry->tags);
        $this->assertEquals(0.95, $updatedEntry->confidence_score);
    }

    public function test_can_delete_knowledge_entry()
    {
        $entry = $this->service->createEntry('Content to delete', 'test');
        
        $result = $this->service->deleteEntry($entry->id);
        
        $this->assertTrue($result);
        $this->assertNull(KnowledgeEntry::find($entry->id));
    }

    public function test_can_add_tags_to_entry()
    {
        $entry = $this->service->createEntry('Test content', 'test', ['tag1']);
        
        $updatedEntry = $this->service->addTags($entry->id, ['tag2', 'tag3']);
        
        $this->assertNotNull($updatedEntry);
        $this->assertEquals(['tag1', 'tag2', 'tag3'], $updatedEntry->tags);
    }

    public function test_can_remove_tags_from_entry()
    {
        $entry = $this->service->createEntry('Test content', 'test', ['tag1', 'tag2', 'tag3']);
        
        $updatedEntry = $this->service->removeTags($entry->id, ['tag2']);
        
        $this->assertNotNull($updatedEntry);
        $this->assertEquals(['tag1', 'tag3'], $updatedEntry->tags);
    }

    public function test_can_get_entries_by_source()
    {
        $this->service->createEntry('Content 1', 'source1', ['tag1']);
        $this->service->createEntry('Content 2', 'source1', ['tag2']);
        $this->service->createEntry('Content 3', 'source2', ['tag3']);

        $results = $this->service->getBySource('source1');

        $this->assertEquals(2, $results->count());
        $results->each(function ($entry) {
            $this->assertEquals('source1', $entry->source);
        });
    }

    public function test_can_get_entries_by_tag()
    {
        $this->service->createEntry('Content 1', 'source1', ['laravel', 'php']);
        $this->service->createEntry('Content 2', 'source2', ['laravel', 'framework']);
        $this->service->createEntry('Content 3', 'source3', ['python']);

        $results = $this->service->getByTag('laravel');

        $this->assertEquals(2, $results->count());
        $results->each(function ($entry) {
            $this->assertContains('laravel', $entry->tags);
        });
    }

    public function test_can_get_all_sources()
    {
        $this->service->createEntry('Content 1', 'source1');
        $this->service->createEntry('Content 2', 'source2');
        $this->service->createEntry('Content 3', 'source1'); // Duplicate source

        $sources = $this->service->getAllSources();

        $this->assertEquals(2, $sources->count());
        $this->assertContains('source1', $sources);
        $this->assertContains('source2', $sources);
    }

    public function test_can_get_all_tags()
    {
        $this->service->createEntry('Content 1', 'source1', ['tag1', 'tag2']);
        $this->service->createEntry('Content 2', 'source2', ['tag2', 'tag3']);
        $this->service->createEntry('Content 3', 'source3', ['tag3', 'tag4']);

        $tags = $this->service->getAllTags();

        $this->assertEquals(4, $tags->count());
        $this->assertContains('tag1', $tags);
        $this->assertContains('tag2', $tags);
        $this->assertContains('tag3', $tags);
        $this->assertContains('tag4', $tags);
    }

    public function test_can_get_statistics()
    {
        $this->service->createEntry('Content 1', 'source1', ['tag1'], 0.8);
        $this->service->createEntry('Content 2', 'source2', ['tag2'], 0.9);
        $this->service->createEntry('Content 3', 'source1', ['tag3'], 0.7);

        $stats = $this->service->getStatistics();

        $this->assertEquals(3, $stats['total_entries']);
        $this->assertEquals(2, $stats['unique_sources']);
        $this->assertEquals(3, $stats['unique_tags']);
        $this->assertEquals(0.8, $stats['average_confidence']);
        $this->assertNotNull($stats['last_updated']);
    }

    public function test_search_uses_cache()
    {
        Cache::shouldReceive('get')
            ->once()
            ->andReturn(null);
            
        Cache::shouldReceive('put')
            ->once();

        $this->service->createEntry('Test content for caching', 'test');
        $this->service->search('test content');
    }

    public function test_can_bulk_import_entries()
    {
        $entries = [
            [
                'content' => 'Entry 1 content',
                'source' => 'bulk_import',
                'tags' => ['tag1'],
                'confidence_score' => 0.8,
            ],
            [
                'content' => 'Entry 2 content',
                'source' => 'bulk_import',
                'tags' => ['tag2'],
                'confidence_score' => 0.9,
            ],
        ];

        $results = $this->service->bulkImport($entries);

        $this->assertEquals(2, $results['success']);
        $this->assertEquals(0, $results['failed']);
        $this->assertEmpty($results['errors']);
        
        $importedEntries = $this->service->getBySource('bulk_import');
        $this->assertEquals(2, $importedEntries->count());
    }

    public function test_is_enabled_returns_correct_value()
    {
        Config::set('ai.knowledge_base.enabled', true);
        $this->assertTrue($this->service->isEnabled());

        Config::set('ai.knowledge_base.enabled', false);
        $this->assertFalse($this->service->isEnabled());
    }

    public function test_search_with_filters()
    {
        $this->service->createEntry('Laravel content', 'docs', ['laravel', 'php']);
        $this->service->createEntry('Python content', 'docs', ['python']);
        $this->service->createEntry('Laravel tutorial', 'tutorial', ['laravel']);

        // Search with tag filter
        $results = $this->service->search('content', 0.1, 10, ['laravel']);
        $this->assertEquals(2, $results->count());

        // Search with source filter
        $results = $this->service->search('content', 0.1, 10, [], 'docs');
        $this->assertEquals(2, $results->count());

        // Search with both filters
        $results = $this->service->search('Laravel', 0.1, 10, ['laravel'], 'docs');
        $this->assertEquals(1, $results->count());
    }

    public function test_returns_empty_collection_on_search_failure()
    {
        // Mock a failure in embedding generation
        Config::set('ai.knowledge_base.use_mock_embeddings', false);
        
        Http::fake([
            '*' => Http::response([], 500)
        ]);

        $results = $this->service->search('test query');
        
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $results);
        $this->assertEquals(0, $results->count());
    }
}
