<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\MonitoringService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class MonitoringServiceTest extends TestCase
{
    protected MonitoringService $monitoringService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->monitoringService = new MonitoringService();
        
        // Clear cache before each test
        Cache::flush();
    }

    public function test_logs_ai_service_call_successfully()
    {
        Log::shouldReceive('info')->once()->with('AI Service Call Successful', \Mockery::type('array'));

        $this->monitoringService->logAIServiceCall(
            'deepseek',
            'deepseek-chat',
            ['message' => 'test'],
            ['content' => 'response'],
            0.5,
            true
        );

        // Check that metrics were updated
        $metrics = $this->monitoringService->getServiceMetrics('deepseek');
        $this->assertEquals(1, $metrics['total_calls']);
        $this->assertEquals(1, $metrics['successful_calls']);
        $this->assertEquals(0, $metrics['failed_calls']);
    }

    public function test_logs_ai_service_call_failure()
    {
        Log::shouldReceive('error')->once()->with('AI Service Call Failed', \Mockery::type('array'));

        $this->monitoringService->logAIServiceCall(
            'gemini',
            'gemini-pro',
            ['message' => 'test'],
            [],
            1.2,
            false,
            'API timeout'
        );

        // Check that metrics were updated
        $metrics = $this->monitoringService->getServiceMetrics('gemini');
        $this->assertEquals(1, $metrics['total_calls']);
        $this->assertEquals(0, $metrics['successful_calls']);
        $this->assertEquals(1, $metrics['failed_calls']);
    }

    public function test_logs_user_interaction()
    {
        Log::shouldReceive('info')->once()->with('User Interaction', \Mockery::type('array'));

        $this->monitoringService->logUserInteraction(
            'ask',
            ['message_length' => 50],
            'user123',
            'session456'
        );

        // Check that interaction metrics were updated
        $metrics = $this->monitoringService->getInteractionMetrics();
        $this->assertEquals(1, $metrics['total_interactions']);
        $this->assertEquals(1, $metrics['ask_requests']);
    }

    public function test_logs_knowledge_base_search()
    {
        Log::shouldReceive('info')->once()->with('Knowledge Base Search', \Mockery::type('array'));

        $this->monitoringService->logKnowledgeBaseSearch(
            'test query',
            5,
            0.3,
            0.7,
            ['laravel', 'php'],
            'docs'
        );

        // Check that search metrics were updated
        $metrics = $this->monitoringService->getSearchMetrics();
        $this->assertEquals(1, $metrics['total_searches']);
        $this->assertEquals(5, $metrics['average_results']);
    }

    public function test_logs_performance_metric()
    {
        Log::shouldReceive('info')->once()->with('Performance Metric', \Mockery::type('array'));

        $this->monitoringService->logPerformanceMetric(
            'database_query',
            0.05,
            ['query' => 'SELECT * FROM users']
        );

        // Check that performance metrics were updated
        $metrics = $this->monitoringService->getPerformanceMetrics();
        $this->assertArrayHasKey('database_query', $metrics['operations']);
        $this->assertEquals(1, $metrics['operations']['database_query']['count']);
    }

    public function test_logs_error()
    {
        Log::shouldReceive('log')->once()->with('error', 'Application Error', \Mockery::type('array'));

        $this->monitoringService->logError(
            'Test error message',
            ['context' => 'test'],
            'error'
        );

        // Check that error metrics were updated
        $metrics = $this->monitoringService->getErrorMetrics();
        $this->assertEquals(1, $metrics['total_errors']);
        $this->assertEquals(1, $metrics['error_levels']['error']);
    }

    public function test_get_service_metrics_for_all_services()
    {
        // Log calls for different services
        $this->monitoringService->logAIServiceCall('deepseek', 'model', [], [], 0.5, true);
        $this->monitoringService->logAIServiceCall('gemini', 'model', [], [], 0.3, true);
        $this->monitoringService->logAIServiceCall('huggingface', 'model', [], [], 0.8, false);

        $allMetrics = $this->monitoringService->getServiceMetrics();

        $this->assertArrayHasKey('deepseek', $allMetrics);
        $this->assertArrayHasKey('gemini', $allMetrics);
        $this->assertArrayHasKey('huggingface', $allMetrics);

        $this->assertEquals(1, $allMetrics['deepseek']['successful_calls']);
        $this->assertEquals(1, $allMetrics['gemini']['successful_calls']);
        $this->assertEquals(1, $allMetrics['huggingface']['failed_calls']);
    }

    public function test_get_interaction_metrics()
    {
        // Log different types of interactions
        $this->monitoringService->logUserInteraction('ask', []);
        $this->monitoringService->logUserInteraction('view_conversation', []);
        $this->monitoringService->logUserInteraction('delete_conversation', []);

        $metrics = $this->monitoringService->getInteractionMetrics();

        $this->assertEquals(3, $metrics['total_interactions']);
        $this->assertEquals(1, $metrics['ask_requests']);
        $this->assertEquals(1, $metrics['conversation_views']);
        $this->assertEquals(1, $metrics['conversation_deletions']);
    }

    public function test_get_search_metrics()
    {
        // Log multiple searches
        $this->monitoringService->logKnowledgeBaseSearch('query1', 3, 0.2, 0.7);
        $this->monitoringService->logKnowledgeBaseSearch('query2', 7, 0.4, 0.8);

        $metrics = $this->monitoringService->getSearchMetrics();

        $this->assertEquals(2, $metrics['total_searches']);
        $this->assertEquals(5, $metrics['average_results']); // (3+7)/2
        $this->assertEquals(0.3, $metrics['average_search_time']); // (0.2+0.4)/2
    }

    public function test_get_performance_metrics()
    {
        // Log multiple performance metrics for same operation
        $this->monitoringService->logPerformanceMetric('api_call', 0.1);
        $this->monitoringService->logPerformanceMetric('api_call', 0.3);
        $this->monitoringService->logPerformanceMetric('database_query', 0.05);

        $metrics = $this->monitoringService->getPerformanceMetrics();

        $this->assertArrayHasKey('api_call', $metrics['operations']);
        $this->assertArrayHasKey('database_query', $metrics['operations']);

        $apiMetrics = $metrics['operations']['api_call'];
        $this->assertEquals(2, $apiMetrics['count']);
        $this->assertEquals(0.2, $apiMetrics['average_duration']); // (0.1+0.3)/2
        $this->assertEquals(0.1, $apiMetrics['min_duration']);
        $this->assertEquals(0.3, $apiMetrics['max_duration']);
    }

    public function test_get_error_metrics()
    {
        // Log different error levels
        $this->monitoringService->logError('Critical error', [], 'critical');
        $this->monitoringService->logError('Warning message', [], 'warning');
        $this->monitoringService->logError('Another error', [], 'error');

        $metrics = $this->monitoringService->getErrorMetrics();

        $this->assertEquals(3, $metrics['total_errors']);
        $this->assertEquals(1, $metrics['error_levels']['critical']);
        $this->assertEquals(1, $metrics['error_levels']['warning']);
        $this->assertEquals(1, $metrics['error_levels']['error']);
    }

    public function test_get_system_health()
    {
        $health = $this->monitoringService->getSystemHealth();

        $this->assertArrayHasKey('status', $health);
        $this->assertArrayHasKey('checks', $health);
        $this->assertArrayHasKey('timestamp', $health);

        $this->assertArrayHasKey('database', $health['checks']);
        $this->assertArrayHasKey('cache', $health['checks']);
        $this->assertArrayHasKey('memory', $health['checks']);

        // Database should be healthy in tests
        $this->assertEquals('healthy', $health['checks']['database']['status']);
    }

    public function test_clear_metrics()
    {
        // Add some metrics
        $this->monitoringService->logAIServiceCall('deepseek', 'model', [], [], 0.5, true);
        $this->monitoringService->logUserInteraction('ask', []);
        $this->monitoringService->logKnowledgeBaseSearch('query', 3, 0.2, 0.7);

        // Verify metrics exist
        $this->assertGreaterThan(0, $this->monitoringService->getServiceMetrics('deepseek')['total_calls']);
        $this->assertGreaterThan(0, $this->monitoringService->getInteractionMetrics()['total_interactions']);
        $this->assertGreaterThan(0, $this->monitoringService->getSearchMetrics()['total_searches']);

        // Clear metrics
        $this->monitoringService->clearMetrics();

        // Verify metrics are cleared
        $this->assertEquals(0, $this->monitoringService->getServiceMetrics('deepseek')['total_calls']);
        $this->assertEquals(0, $this->monitoringService->getInteractionMetrics()['total_interactions']);
        $this->assertEquals(0, $this->monitoringService->getSearchMetrics()['total_searches']);
    }

    public function test_metrics_accumulate_correctly()
    {
        // Log multiple calls for the same service
        for ($i = 0; $i < 5; $i++) {
            $this->monitoringService->logAIServiceCall('deepseek', 'model', [], [], 0.1 * ($i + 1), true);
        }

        $metrics = $this->monitoringService->getServiceMetrics('deepseek');

        $this->assertEquals(5, $metrics['total_calls']);
        $this->assertEquals(5, $metrics['successful_calls']);
        $this->assertEquals(0, $metrics['failed_calls']);
        $this->assertEquals(0.3, $metrics['average_duration']); // (0.1+0.2+0.3+0.4+0.5)/5
    }

    public function test_handles_database_connection_failure_in_health_check()
    {
        // Mock database connection failure
        DB::shouldReceive('connection->getPdo')->andThrow(new \Exception('Connection failed'));

        $health = $this->monitoringService->getSystemHealth();

        $this->assertEquals('unhealthy', $health['status']);
        $this->assertEquals('unhealthy', $health['checks']['database']['status']);
        $this->assertArrayHasKey('error', $health['checks']['database']);
    }

    public function test_handles_cache_failure_in_health_check()
    {
        // Mock cache failure
        Cache::shouldReceive('put')->andThrow(new \Exception('Cache failed'));
        Cache::shouldReceive('get')->andThrow(new \Exception('Cache failed'));

        $health = $this->monitoringService->getSystemHealth();

        $this->assertEquals('unhealthy', $health['checks']['cache']['status']);
        $this->assertArrayHasKey('error', $health['checks']['cache']);
    }

    public function test_memory_usage_check_in_health()
    {
        $health = $this->monitoringService->getSystemHealth();

        $this->assertArrayHasKey('memory', $health['checks']);
        $this->assertArrayHasKey('usage_bytes', $health['checks']['memory']);
        $this->assertArrayHasKey('usage_mb', $health['checks']['memory']);
        $this->assertArrayHasKey('percentage', $health['checks']['memory']);

        // Memory usage should be reasonable in tests
        $this->assertLessThan(80, $health['checks']['memory']['percentage']);
    }
}
