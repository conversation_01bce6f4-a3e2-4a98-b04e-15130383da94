import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import ChatMessage from '@/components/ChatMessage.vue';
import ModelBadge from '@/components/ModelBadge.vue';

// Mock the icons
vi.mock('@heroicons/vue/24/outline', () => ({
  PencilIcon: { template: '<div data-testid="pencil-icon"></div>' },
  ClipboardIcon: { template: '<div data-testid="clipboard-icon"></div>' },
}));

describe('ChatMessage', () => {
  let wrapper;

  const mockUserMessage = {
    id: 1,
    role: 'user',
    content: 'Hello, how can you help me?',
    created_at: '2024-01-15T10:30:00Z',
  };

  const mockAssistantMessage = {
    id: 2,
    role: 'assistant',
    content: 'I can help you with various tasks including coding, writing, and answering questions.',
    model_used: 'deepseek',
    service_used: 'deepseek',
    knowledge_base_used: false,
    fallback_used: false,
    created_at: '2024-01-15T10:30:30Z',
    metadata: {
      processing_time: 1.5,
    },
  };

  const mockLocalKnowledgeMessage = {
    id: 3,
    role: 'assistant',
    content: 'This response came from local knowledge base.',
    model_used: 'local_knowledge',
    service_used: 'knowledge_base',
    knowledge_base_used: true,
    created_at: '2024-01-15T10:31:00Z',
    metadata: {
      local_response: true,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders user message correctly', () => {
    wrapper = mount(ChatMessage, {
      props: { message: mockUserMessage },
      global: {
        components: { ModelBadge },
      },
    });

    expect(wrapper.find('.bg-blue-600').exists()).toBe(true);
    expect(wrapper.text()).toContain('Hello, how can you help me?');
    expect(wrapper.find('[data-testid="pencil-icon"]').exists()).toBe(true);
  });

  it('renders assistant message correctly', () => {
    wrapper = mount(ChatMessage, {
      props: { message: mockAssistantMessage },
      global: {
        components: { ModelBadge },
      },
    });

    expect(wrapper.find('.bg-gray-700').exists()).toBe(true);
    expect(wrapper.text()).toContain('I can help you with various tasks');
    expect(wrapper.findComponent(ModelBadge).exists()).toBe(true);
  });

  it('shows local knowledge indicator for knowledge base responses', () => {
    wrapper = mount(ChatMessage, {
      props: { message: mockLocalKnowledgeMessage },
      global: {
        components: { ModelBadge },
      },
    });

    expect(wrapper.text()).toContain('Answer from local knowledge');
    expect(wrapper.find('.text-emerald-400').exists()).toBe(true);
  });

  it('shows processing time for assistant messages', () => {
    wrapper = mount(ChatMessage, {
      props: { message: mockAssistantMessage },
      global: {
        components: { ModelBadge },
      },
    });

    expect(wrapper.text()).toContain('1.50s');
  });

  it('enters edit mode when edit button is clicked', async () => {
    wrapper = mount(ChatMessage, {
      props: { message: mockUserMessage },
      global: {
        components: { ModelBadge },
      },
    });

    await wrapper.find('[data-testid="pencil-icon"]').trigger('click');
    
    expect(wrapper.find('textarea').exists()).toBe(true);
    expect(wrapper.find('textarea').element.value).toBe(mockUserMessage.content);
  });

  it('emits edit event when save is clicked', async () => {
    wrapper = mount(ChatMessage, {
      props: { message: mockUserMessage },
      global: {
        components: { ModelBadge },
      },
    });

    // Enter edit mode
    await wrapper.find('[data-testid="pencil-icon"]').trigger('click');
    
    // Change content
    const textarea = wrapper.find('textarea');
    await textarea.setValue('Updated message content');
    
    // Click save
    await wrapper.find('button:contains("Save & Resend")').trigger('click');
    
    expect(wrapper.emitted('edit')).toBeTruthy();
    expect(wrapper.emitted('edit')[0]).toEqual([1, 'Updated message content']);
  });

  it('cancels edit mode when cancel is clicked', async () => {
    wrapper = mount(ChatMessage, {
      props: { message: mockUserMessage },
      global: {
        components: { ModelBadge },
      },
    });

    // Enter edit mode
    await wrapper.find('[data-testid="pencil-icon"]').trigger('click');
    expect(wrapper.find('textarea').exists()).toBe(true);
    
    // Click cancel
    await wrapper.find('button:contains("Cancel")').trigger('click');
    
    expect(wrapper.find('textarea').exists()).toBe(false);
  });

  it('copies message content when copy button is clicked', async () => {
    const writeTextSpy = vi.spyOn(navigator.clipboard, 'writeText');
    
    wrapper = mount(ChatMessage, {
      props: { message: mockUserMessage },
      global: {
        components: { ModelBadge },
      },
    });

    await wrapper.find('[data-testid="clipboard-icon"]').trigger('click');
    
    expect(writeTextSpy).toHaveBeenCalledWith(mockUserMessage.content);
  });

  it('formats content with basic markdown', () => {
    const messageWithCode = {
      ...mockAssistantMessage,
      content: 'Here is some `inline code` and a code block:\n```javascript\nconsole.log("hello");\n```',
    };

    wrapper = mount(ChatMessage, {
      props: { message: messageWithCode },
      global: {
        components: { ModelBadge },
      },
    });

    const content = wrapper.find('.prose');
    expect(content.html()).toContain('<code');
    expect(content.html()).toContain('<pre');
  });

  it('shows fallback indicator when fallback was used', () => {
    const fallbackMessage = {
      ...mockAssistantMessage,
      fallback_used: true,
    };

    wrapper = mount(ChatMessage, {
      props: { message: fallbackMessage },
      global: {
        components: { ModelBadge },
      },
    });

    expect(wrapper.text()).toContain('Fallback');
    expect(wrapper.find('.text-yellow-400').exists()).toBe(true);
  });

  it('formats timestamp correctly', () => {
    wrapper = mount(ChatMessage, {
      props: { message: mockUserMessage },
      global: {
        components: { ModelBadge },
      },
    });

    // Should show time in HH:MM format
    expect(wrapper.text()).toMatch(/\d{1,2}:\d{2}/);
  });
});
