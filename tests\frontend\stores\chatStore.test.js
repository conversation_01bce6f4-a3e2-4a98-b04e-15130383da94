import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setActive<PERSON>inia, createP<PERSON> } from 'pinia';
import { useChatStore } from '@/stores/chatStore';

// Mock the chat service
vi.mock('@/services/chatService', () => ({
  chatService: {
    sendMessage: vi.fn(),
    getConversation: vi.fn(),
    getConversations: vi.fn(),
  },
}));

describe('Chat Store', () => {
  let store;
  let mockChatService;

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useChatStore();
    
    // Get the mocked chat service
    mockChatService = require('@/services/chatService').chatService;
    
    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('initial state', () => {
    it('has correct initial state', () => {
      expect(store.conversations).toEqual([]);
      expect(store.currentConversation).toBeNull();
      expect(store.messages).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.isTyping).toBe(false);
      expect(store.error).toBeNull();
    });
  });

  describe('sendMessage', () => {
    it('sends message successfully', async () => {
      const mockResponse = {
        data: {
          conversation: { id: 1, title: 'Test Conversation' },
          message: { id: 1, role: 'user', content: 'Hello' },
          response: {
            content: 'Hi there!',
            model_used: 'deepseek',
            service_used: 'deepseek',
            fallback_used: false,
            processing_time: 1.2,
          },
          knowledge_base: {
            used: false,
            entries_found: 0,
          },
        },
      };

      mockChatService.sendMessage.mockResolvedValue(mockResponse);

      await store.sendMessage('Hello');

      expect(mockChatService.sendMessage).toHaveBeenCalledWith('Hello', {
        conversation_id: null,
      });

      expect(store.currentConversation).toEqual(mockResponse.data.conversation);
      expect(store.messages).toHaveLength(2); // User message + assistant message
      expect(store.isLoading).toBe(false);
      expect(store.isTyping).toBe(false);
    });

    it('handles empty messages', async () => {
      await store.sendMessage('   ');

      expect(mockChatService.sendMessage).not.toHaveBeenCalled();
      expect(store.messages).toHaveLength(0);
    });

    it('handles API errors', async () => {
      const errorMessage = 'API Error';
      mockChatService.sendMessage.mockRejectedValue({
        response: { data: { message: errorMessage } },
      });

      await expect(store.sendMessage('Hello')).rejects.toThrow();

      expect(store.error).toBe(errorMessage);
      expect(store.isLoading).toBe(false);
      expect(store.isTyping).toBe(false);
    });

    it('sets loading and typing states correctly', async () => {
      const mockResponse = {
        data: {
          conversation: { id: 1 },
          message: { id: 1, role: 'user', content: 'Hello' },
          response: { content: 'Hi!' },
          knowledge_base: { used: false, entries_found: 0 },
        },
      };

      // Create a promise that we can control
      let resolvePromise;
      const controlledPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      mockChatService.sendMessage.mockReturnValue(controlledPromise);

      // Start sending message
      const sendPromise = store.sendMessage('Hello');

      // Check loading states are set
      expect(store.isLoading).toBe(true);
      expect(store.isTyping).toBe(true);

      // Resolve the promise
      resolvePromise(mockResponse);
      await sendPromise;

      // Check loading states are cleared
      expect(store.isLoading).toBe(false);
      expect(store.isTyping).toBe(false);
    });
  });

  describe('editAndResendMessage', () => {
    beforeEach(() => {
      // Set up some initial messages
      store.messages = [
        { id: 1, content: 'First message' },
        { id: 2, content: 'Second message' },
        { id: 3, content: 'Third message' },
      ];
    });

    it('removes messages after edited message and resends', async () => {
      const mockResponse = {
        data: {
          conversation: { id: 1 },
          message: { id: 4, role: 'user', content: 'Edited message' },
          response: { content: 'Response to edited message' },
          knowledge_base: { used: false, entries_found: 0 },
        },
      };

      mockChatService.sendMessage.mockResolvedValue(mockResponse);

      await store.editAndResendMessage(2, 'Edited message');

      // Should remove messages after ID 2 and add new ones
      expect(store.messages).toHaveLength(3); // First message + new user + new assistant
      expect(store.messages[0].id).toBe(1);
      expect(mockChatService.sendMessage).toHaveBeenCalledWith('Edited message');
    });

    it('handles non-existent message ID', async () => {
      const originalLength = store.messages.length;

      await store.editAndResendMessage(999, 'New content');

      // Should not modify messages if ID not found
      expect(store.messages).toHaveLength(originalLength);
    });
  });

  describe('loadConversation', () => {
    it('loads conversation successfully', async () => {
      const mockConversation = { id: 1, title: 'Test Conversation' };
      const mockMessages = [
        { id: 1, content: 'Hello', created_at: '2024-01-15T10:00:00Z' },
        { id: 2, content: 'Hi there!', created_at: '2024-01-15T10:00:30Z' },
      ];

      mockChatService.getConversation.mockResolvedValue({
        data: {
          conversation: mockConversation,
          messages: mockMessages,
        },
      });

      await store.loadConversation(1);

      expect(mockChatService.getConversation).toHaveBeenCalledWith(1);
      expect(store.currentConversation).toEqual(mockConversation);
      expect(store.messages).toHaveLength(2);
      expect(store.messages[0].created_at).toBe('2024-01-15T10:00:00.000Z');
    });

    it('handles load conversation errors', async () => {
      mockChatService.getConversation.mockRejectedValue(new Error('Load failed'));

      await expect(store.loadConversation(1)).rejects.toThrow('Load failed');

      expect(store.error).toBe('Failed to load conversation');
    });
  });

  describe('loadConversations', () => {
    it('loads conversations list successfully', async () => {
      const mockConversations = [
        { id: 1, title: 'Conversation 1' },
        { id: 2, title: 'Conversation 2' },
      ];

      mockChatService.getConversations.mockResolvedValue({
        data: { conversations: mockConversations },
      });

      await store.loadConversations();

      expect(mockChatService.getConversations).toHaveBeenCalled();
      expect(store.conversations).toEqual(mockConversations);
    });

    it('handles load conversations errors', async () => {
      mockChatService.getConversations.mockRejectedValue(new Error('Load failed'));

      await expect(store.loadConversations()).rejects.toThrow('Load failed');

      expect(store.error).toBe('Failed to load conversations');
    });
  });

  describe('createNewConversation', () => {
    it('resets conversation state', () => {
      // Set up some state
      store.currentConversation = { id: 1, title: 'Old Conversation' };
      store.messages = [{ id: 1, content: 'Old message' }];
      store.error = 'Some error';

      store.createNewConversation();

      expect(store.currentConversation).toBeNull();
      expect(store.messages).toEqual([]);
      expect(store.error).toBeNull();
    });
  });

  describe('computed properties', () => {
    beforeEach(() => {
      store.currentConversation = { id: 1 };
      store.messages = [
        { id: 1, conversation_id: 1, content: 'Message 1' },
        { id: 2, conversation_id: 2, content: 'Message 2' },
        { id: 3, conversation_id: 1, content: 'Message 3' },
      ];
    });

    it('currentMessages filters by conversation ID', () => {
      expect(store.currentMessages).toHaveLength(2);
      expect(store.currentMessages[0].id).toBe(1);
      expect(store.currentMessages[1].id).toBe(3);
    });

    it('hasMessages returns correct boolean', () => {
      expect(store.hasMessages).toBe(true);

      store.messages = [];
      expect(store.hasMessages).toBe(false);
    });
  });

  describe('utility methods', () => {
    it('setTypingModel sets typing state', () => {
      store.setTypingModel('deepseek');

      expect(store.typingModel).toBe('deepseek');
      expect(store.isTyping).toBe(true);

      store.setTypingModel('');

      expect(store.typingModel).toBe('');
      expect(store.isTyping).toBe(false);
    });

    it('clearError clears error state', () => {
      store.error = 'Some error';

      store.clearError();

      expect(store.error).toBeNull();
    });
  });
});
